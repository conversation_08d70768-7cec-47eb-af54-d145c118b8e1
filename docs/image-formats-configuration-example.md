# 图片格式配置化功能说明

## 概述

本次修改将 `ImageUploadServiceImpl.java` 中硬编码的 `SUPPORTED_FORMATS` 常量配置化，支持通过配置文件动态调整支持的图片格式。

## 修改内容

### 1. DataLoaderConfig.java 新增配置项

```java
/**
 * 支持的图片格式配置
 * 默认支持 FastImageInfoHelper 能解析的所有格式
 */
public volatile Set<String> supportedImageFormats = new HashSet<>(Arrays.asList(
        "gif", "jpg", "jpeg", "png", "bmp", "webp", "tiff", "heif"
));
```

### 2. ImageUploadServiceImpl.java 使用配置

```java
// 原来的硬编码方式（已移除）
// private static final Set<String> SUPPORTED_FORMATS = new HashSet<>(Arrays.asList(
//         "png", "jpg", "jpeg", "gif", "bmp", "webp"
// ));

// 新的配置化方式
Set<String> supportedFormats = DataLoaderConfig.INSTANCE.getSupportedImageFormats();
if (format != null && !supportedFormats.contains(format.toLowerCase())) {
    // 格式不支持的处理逻辑
}
```

## 配置方式

### 默认配置

如果不进行任何配置，系统将使用默认支持的格式：
- gif
- jpg
- jpeg  
- png
- bmp
- webp
- tiff
- heif

### 自定义配置

在配置文件中添加 `supportedImageFormats` 配置项：

```properties
# 支持的图片格式（逗号分隔）
supportedImageFormats=gif,jpg,jpeg,png,bmp,webp,tiff,heif
```

或者只支持部分格式：

```properties
# 只支持常用格式
supportedImageFormats=jpg,jpeg,png,gif
```

## 向后兼容性

1. **默认格式扩展**：新增了 `tiff` 和 `heif` 格式支持（基于 FastImageInfoHelper 的能力）
2. **原有格式保持**：保留了原来支持的所有格式 `png, jpg, jpeg, gif, bmp, webp`
3. **配置可选**：如果不配置，使用默认值，不影响现有功能

## 优势

1. **灵活配置**：可以根据业务需要动态调整支持的图片格式
2. **无需重启**：配置支持热更新（基于 autoconf 机制）
3. **向后兼容**：不影响现有功能
4. **格式扩展**：新增了 TIFF 和 HEIF 格式支持

## 注意事项

1. 配置的格式必须是 `FastImageInfoHelper` 能够解析的格式
2. 格式名称不区分大小写，系统会自动转换为小写
3. 配置格式错误时会自动回退到默认配置
4. 建议保留 `jpg` 和 `jpeg` 两种格式以确保兼容性
