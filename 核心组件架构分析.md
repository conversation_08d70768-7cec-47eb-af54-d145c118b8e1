# 核心组件架构分析

## 🏗️ 整体架构概览

### 架构设计原则
1. **装饰器模式**: 无侵入式增强现有Excel读取器
2. **策略模式**: 支持多种图片存储格式的统一处理
3. **依赖注入**: 基于Spring框架的松耦合设计
4. **单一职责**: 每个组件职责明确，高内聚低耦合

### 核心组件层次结构
```
Excel图片导入功能架构
├── 装饰器层 (Decorator Layer)
│   ├── OptimizedImageAwareExcelReaderDecorator
│   └── OptimizedImageAwareRowReader
├── 服务层 (Service Layer)
│   ├── ImageUploadService/ImageUploadServiceImpl
│   └── CrmMetadataService
├── 提取器层 (Extractor Layer)
│   └── UnifiedImageExtractor
└── 模型层 (Model Layer)
    ├── CellPosition
    ├── ImageFieldMapping
    ├── ImageStorageType
    └── ImageUploadResult
```

## 🔧 核心组件详细分析

### 1. UnifiedImageExtractor (统一图片提取器)
**文件**: `UnifiedImageExtractor.java` (900行)  
**职责**: 统一图片提取，支持多种存储格式

#### 核心功能
- **多格式支持**: 支持OFFICE_STANDARD、WPS_DISPIMG、WPS_LEGACY、EMBEDDED_CELL四种存储类型
- **智能检测**: 自动检测图片存储类型并选择最佳提取策略
- **多工作表支持**: 支持Excel多工作表的图片提取

#### 关键方法
```java
// 主要入口方法
public byte[] extractImage(String filePath, CellPosition position, int sheetIndex)

// 存储类型检测
private ImageStorageType detectImageStorageType(String filePath, CellPosition position, int sheetIndex)

// 按类型提取
private byte[] extractByStorageType(String filePath, CellPosition position, ImageStorageType storageType, int sheetIndex)
```

#### 设计模式应用
- **策略模式**: 根据不同的ImageStorageType选择不同的提取策略
- **模板方法模式**: extractImage方法定义了提取的标准流程

### 2. OptimizedImageAwareExcelReaderDecorator (装饰器)
**文件**: `OptimizedImageAwareExcelReaderDecorator.java` (87行)  
**职责**: 装饰现有Excel读取器，增加图片处理能力

#### 核心功能
- **无侵入式增强**: 包装现有IExcelReader，不修改原有逻辑
- **依赖注入集成**: 通过SpringContextUtil获取所需的服务Bean
- **错误处理**: 图片处理失败时的优雅降级

#### 装饰器模式实现
```java
public class OptimizedImageAwareExcelReaderDecorator implements IExcelReader {
    private final IExcelReader delegate;  // 被装饰的对象
    
    @Override
    public void process(String fileName) throws IOException {
        // 增强逻辑：创建图片感知的RowReader
        this.wrappedRowReader = OptimizedImageAwareRowReader.builder()
            .imageExtractor(imageExtractor)
            .originalRowReader(originalRowReader)
            // ... 其他依赖
            .build();
        
        delegate.setRowReader(wrappedRowReader);
        delegate.process(fileName);  // 委托给原始对象
    }
}
```

### 3. OptimizedImageAwareRowReader (图片感知行读取器)
**文件**: `OptimizedImageAwareRowReader.java` (455行)  
**职责**: 在行级别处理图片识别和上传

#### 核心功能
- **表头识别**: 第一行处理时识别图片字段
- **图片处理**: 数据行处理时进行图片提取和上传
- **多列合并**: 支持图片字段的多列合并处理

#### 处理流程
```java
@Override
public boolean getRows(int sheetIndex, int curRow, List<String> rowList) {
    if (curRow == 1) {
        handleHeaderRow(rowList, sheetIndex);  // 表头处理
    } else {
        handleDataRow(curRow, rowList, sheetIndex);  // 数据行处理
    }
    return originalRowReader.getRows(sheetIndex, curRow, rowList);
}
```

### 4. ImageUploadService (图片上传服务)
**接口**: `ImageUploadService.java` (10行)  
**实现**: `ImageUploadServiceImpl.java` (202行)  
**职责**: 处理图片上传到文件服务器

#### 核心功能
- **格式验证**: 支持png、jpg、jpeg、gif、bmp、webp格式
- **大小限制**: 最大文件大小10MB
- **Stone集成**: 集成Stone文件服务进行图片上传

#### 服务接口设计
```java
public interface ImageUploadService {
    ImageUploadResult uploadImage(byte[] imageData, String fileName, String format, User user);
}
```

### 5. CrmMetadataService (CRM元数据服务)
**文件**: `CrmMetadataService.java` (122行)  
**职责**: 集成CRM服务，获取字段类型信息

#### 核心功能
- **字段类型查询**: 通过CRM API获取对象字段描述
- **图片字段识别**: 判断字段是否为图片类型
- **缓存优化**: 避免重复的CRM API调用

## 📊 组件关系图

```mermaid
classDiagram
    class IExcelReader {
        <<interface>>
        +process(String fileName)
        +setRowReader(IRowReader reader)
    }
    
    class OptimizedImageAwareExcelReaderDecorator {
        -IExcelReader delegate
        -List~String~ apiNameList
        -User user
        +process(String fileName)
        +setRowReader(IRowReader reader)
    }
    
    class OptimizedImageAwareRowReader {
        -IRowReader originalRowReader
        -UnifiedImageExtractor imageExtractor
        -ImageUploadService imageUploadService
        -CrmMetadataService crmMetadataService
        +getRows(int sheetIndex, int curRow, List~String~ rowList)
        -handleHeaderRow(List~String~ rowData, int sheetIndex)
        -handleDataRow(int curRow, List~String~ rowData, int sheetIndex)
    }
    
    class UnifiedImageExtractor {
        +extractImage(String filePath, CellPosition position, int sheetIndex)
        -detectImageStorageType(String filePath, CellPosition position, int sheetIndex)
        -extractByStorageType(String filePath, CellPosition position, ImageStorageType storageType, int sheetIndex)
    }
    
    class ImageUploadService {
        <<interface>>
        +uploadImage(byte[] imageData, String fileName, String format, User user)
    }
    
    class ImageUploadServiceImpl {
        -StoneProxyApi stoneProxyApi
        -EIEAConverter eieaConverter
        +uploadImage(byte[] imageData, String fileName, String format, User user)
    }
    
    class CrmMetadataService {
        -ExportRestProxy exportRestProxy
        +getObjectDescribe(String apiName, User user)
        +getImageFields(String apiName, User user)
    }
    
    class ImageStorageType {
        <<enumeration>>
        OFFICE_STANDARD
        WPS_DISPIMG
        WPS_LEGACY
        EMBEDDED_CELL
    }
    
    class CellPosition {
        -int row
        -int column
        +of(int row, int column)
    }
    
    class ImageUploadResult {
        -boolean success
        -String uploadedPath
        -String errorMessage
        -long fileSize
    }
    
    IExcelReader <|.. OptimizedImageAwareExcelReaderDecorator
    OptimizedImageAwareExcelReaderDecorator --> IExcelReader : delegates to
    OptimizedImageAwareExcelReaderDecorator --> OptimizedImageAwareRowReader : creates
    OptimizedImageAwareRowReader --> UnifiedImageExtractor : uses
    OptimizedImageAwareRowReader --> ImageUploadService : uses
    OptimizedImageAwareRowReader --> CrmMetadataService : uses
    ImageUploadService <|.. ImageUploadServiceImpl
    UnifiedImageExtractor --> ImageStorageType : uses
    UnifiedImageExtractor --> CellPosition : uses
    ImageUploadService --> ImageUploadResult : returns
```

## 🎯 设计模式应用分析

### 1. 装饰器模式 (Decorator Pattern)
**应用场景**: OptimizedImageAwareExcelReaderDecorator  
**优势**:
- 无需修改现有Excel读取器代码
- 可以动态地给对象添加功能
- 符合开闭原则

**实现要点**:
```java
// 装饰器实现相同接口
public class OptimizedImageAwareExcelReaderDecorator implements IExcelReader {
    private final IExcelReader delegate;  // 持有被装饰对象的引用
    
    // 在原有功能基础上增加新功能
    public void process(String fileName) throws IOException {
        // 增强逻辑
        setupImageProcessing();
        // 委托给原对象
        delegate.process(fileName);
    }
}
```

### 2. 策略模式 (Strategy Pattern)
**应用场景**: UnifiedImageExtractor的多格式支持  
**优势**:
- 支持多种图片提取算法
- 可以在运行时切换策略
- 易于扩展新的图片格式

**实现要点**:
```java
// 根据检测到的存储类型选择不同策略
private byte[] extractByStorageType(String filePath, CellPosition position, 
                                   ImageStorageType storageType, int sheetIndex) {
    switch (storageType) {
        case OFFICE_STANDARD:
            return extractStandardOOXMLImage(filePath, position, sheetIndex);
        case WPS_DISPIMG:
            return extractWPSDispimgImage(filePath, position, sheetIndex);
        // ... 其他策略
    }
}
```

### 3. 建造者模式 (Builder Pattern)
**应用场景**: OptimizedImageAwareRowReader的构建  
**优势**:
- 简化复杂对象的创建
- 参数传递清晰
- 支持链式调用

## 🔗 组件依赖关系

### 依赖层次
1. **装饰器层** → **服务层** → **提取器层** → **模型层**
2. **Spring容器管理**: 所有服务组件通过@Service、@Component注解管理
3. **接口抽象**: 通过接口定义契约，降低耦合度

### 关键依赖
- OptimizedImageAwareExcelReaderDecorator → SpringContextUtil (获取Bean)
- OptimizedImageAwareRowReader → UnifiedImageExtractor (图片提取)
- OptimizedImageAwareRowReader → ImageUploadService (图片上传)
- OptimizedImageAwareRowReader → CrmMetadataService (字段识别)
- ImageUploadServiceImpl → StoneProxyApi (文件上传)
- CrmMetadataService → ExportRestProxy (CRM API调用)

## ✨ 架构优势

### 1. 松耦合设计
- 通过接口定义契约
- 依赖注入降低耦合
- 装饰器模式避免侵入性修改

### 2. 高可扩展性
- 策略模式支持新图片格式
- 装饰器模式支持功能增强
- 模块化设计便于独立开发

### 3. 高可维护性
- 单一职责原则
- 清晰的分层架构
- 完善的错误处理机制

### 4. 高性能
- 按需处理策略
- 智能预筛选机制
- 缓存优化减少重复调用

## 🔍 组件交互时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Decorator as OptimizedImageAware<br/>ExcelReaderDecorator
    participant RowReader as OptimizedImageAware<br/>RowReader
    participant CrmService as CrmMetadataService
    participant Extractor as UnifiedImageExtractor
    participant UploadService as ImageUploadService
    participant BaseReader as 基础Excel读取器

    Client->>Decorator: process(fileName)
    Decorator->>Decorator: 获取Spring Bean
    Decorator->>RowReader: 创建图片感知RowReader
    Decorator->>BaseReader: setRowReader(wrappedRowReader)
    Decorator->>BaseReader: process(fileName)

    BaseReader->>RowReader: getRows(1, headerRow) [表头行]
    RowReader->>CrmService: getObjectDescribe(apiName, user)
    CrmService-->>RowReader: 返回字段描述信息
    RowReader->>RowReader: identifyImageColumns(headerRow)
    RowReader->>BaseReader: 继续处理表头

    BaseReader->>RowReader: getRows(2, dataRow) [数据行]
    RowReader->>RowReader: 检查是否为图片字段
    alt 是图片字段
        RowReader->>Extractor: extractImage(filePath, position, sheetIndex)
        Extractor->>Extractor: detectImageStorageType()
        Extractor->>Extractor: extractByStorageType()
        Extractor-->>RowReader: 返回图片字节数据
        RowReader->>UploadService: uploadImage(imageData, fileName, format, user)
        UploadService-->>RowReader: 返回上传结果(npath)
        RowReader->>RowReader: 更新单元格值为图片路径
    else 非图片字段
        RowReader->>RowReader: 保持原始值不变
    end
    RowReader->>BaseReader: 继续处理数据行
```
