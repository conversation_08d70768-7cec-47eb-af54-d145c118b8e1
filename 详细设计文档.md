# Excel图片导入功能详细设计文档

## 🏗️ 整体架构设计

### 1. 分层架构

```mermaid
%%{ init: {'themeVariables': { 
    'background': '#ffffff',
    'primaryColor': '#daeafe',
    'edgeLabelBackground':'#F2F2F2',
    'fontSize': '16px'
  }} }%%
flowchart TB

  subgraph subGraph0["表现层 (Presentation Layer)"]
    direction TB
    A@{ shape: circle, label: "用户界面\\nUser Interface" }
    B["REST API"]
    style subGraph0 fill:#e3f2fd,stroke:#42a5f5,stroke-width:2px
  end

  subgraph subGraph1["控制层 (Controller Layer)"]
    C["ImportTask"]
    D["ExcelExecutor"]
    style subGraph1 fill:#e8f5e9,stroke:#66bb6a,stroke-width:2px
  end

  subgraph subGraph2["装饰器层 (Decorator Layer)"]
    E["OptimizedImageAwareExcelReaderDecorator"]
    F["OptimizedImageAwareRowReader"]
    style subGraph2 fill:#fff8e1,stroke:#ffc107,stroke-width:2px
  end

  subgraph subGraph3["服务层 (Service Layer)"]
    G["CrmMetadataService"]
    H["ImageUploadService"]
    I["UnifiedImageExtractor"]
    style subGraph3 fill:#f9fbe7,stroke:#d4e157,stroke-width:2px
  end

  subgraph subGraph4["基础设施层 (Infrastructure Layer)"]
    J["基础Excel读取器"]
    K["Stone文件服务"]
    L["CRM API服务"]
    style subGraph4 fill:#f3e5f5,stroke:#ab47bc,stroke-width:2px
  end

  subgraph subGraph5["数据层 (Data Layer)"]
    M@{ shape: cyl, label: "Excel文件系统\nExcel File Sys." }
    N@{ shape: cyl, label: "CRM数据库\nCRM DB" }
    O@{ shape: cyl, label: "文件存储系统\nFile Storage" }
    style subGraph5 fill:#efebe9,stroke:#8d6e63,stroke-width:2px
  end

  A --> B
  B --> C
  C --> D
  D --> E
  E --> F & J
  F --> G & H & I
  G --> L
  H --> K
  I --> M
  L --> N
  K --> O
  J --> M

```

### 2. 模块划分

#### 核心模块结构
```
com.facishare.paas.metadata.dataloader.image/
├── decorator/                    # 装饰器模式实现
│   ├── OptimizedImageAwareExcelReaderDecorator.java
│   └── OptimizedImageAwareRowReader.java
├── extractor/                    # 图片提取器
│   └── UnifiedImageExtractor.java
├── model/                        # 数据模型
│   ├── CellPosition.java
│   ├── ImageFieldMapping.java
│   ├── ImageStorageType.java
│   └── ImageUploadResult.java
└── service/                      # 服务层
    ├── ImageUploadService.java
    └── ImageUploadServiceImpl.java
```

## 📊 核心类图设计

### 1. 装饰器模式类图

```mermaid
classDiagram
    class IExcelReader {
        <<interface>>
        +process(String fileName) void
        +setRowReader(IRowReader reader) void
    }
    
    class OptimizedImageAwareExcelReaderDecorator {
        -IExcelReader delegate
        -List~String~ apiNameList
        -User user
        +process(String fileName) void
        +setRowReader(IRowReader reader) void
    }
    
    class OptimizedImageAwareRowReader {
        -IRowReader originalRowReader
        -UnifiedImageExtractor imageExtractor
        -ImageUploadService imageUploadService
        -CrmMetadataService crmMetadataService
        +getRows(int sheetIndex, int curRow, List~String~ rowList) boolean
        -handleHeaderRow(List~String~ rowData, int sheetIndex) void
        -handleDataRow(int curRow, List~String~ rowData, int sheetIndex) void
    }
    
    IExcelReader <|.. OptimizedImageAwareExcelReaderDecorator
    OptimizedImageAwareExcelReaderDecorator --> IExcelReader : delegates to
    OptimizedImageAwareExcelReaderDecorator --> OptimizedImageAwareRowReader : creates
```

## 🔧 接口设计详细说明

### 1. IExcelReader 接口
```java
/**
 * Excel读取器接口
 * 定义Excel文件处理的标准契约
 */
public interface IExcelReader {
    /**
     * 处理Excel文件
     * @param fileName Excel文件路径
     * @throws IOException 文件处理异常
     */
    void process(String fileName) throws IOException;
    
    /**
     * 设置行读取器
     * @param reader 行读取器实例
     */
    void setRowReader(IRowReader reader);
}
```

### 2. ImageUploadService 接口
```java
/**
 * 图片上传服务接口
 * 负责图片文件的上传处理
 */
public interface ImageUploadService {
    /**
     * 上传图片文件
     * @param imageData 图片字节数据
     * @param fileName 文件名
     * @param format 图片格式
     * @param user 用户信息
     * @return 上传结果
     */
    ImageUploadResult uploadImage(byte[] imageData, String fileName, String format, User user);
}
```

## 📋 数据结构详细定义

### 1. CellPosition (单元格位置)
```java
/**
 * Excel单元格位置
 * 用于标识图片在Excel中的具体位置
 */
@Data
@EqualsAndHashCode
public class CellPosition {
    /** 行号（0-based） */
    private final int row;
    
    /** 列号（0-based） */
    private final int col;
    
    /**
     * 构造函数
     * @param row 行号（0-based）
     * @param col 列号（0-based）
     */
    public CellPosition(int row, int col) {
        this.row = row;
        this.col = col;
    }
    
    /**
     * 静态工厂方法
     * @param row 行号
     * @param col 列号
     * @return CellPosition实例
     */
    public static CellPosition of(int row, int col) {
        return new CellPosition(row, col);
    }
    
    /**
     * 获取Excel格式的单元格地址（如A1, B2等）
     * @return Excel格式的地址
     */
    public String getExcelAddress() {
        return getColumnLetter(col) + (row + 1);
    }
}
```

### 2. ImageUploadResult (图片上传结果)
```java
/**
 * 图片上传结果
 * 封装图片上传的完整结果信息
 */
@Data
@Builder
public class ImageUploadResult {
    /** 是否上传成功 */
    @Builder.Default
    private boolean success = false;
    
    /** 上传后的文件路径 */
    private String uploadedPath;
    
    /** 原始文件名 */
    private String originalFileName;
    
    /** 文件大小（字节） */
    @Builder.Default
    private long fileSize = 0;
    
    /** 图片格式 */
    private String format;
    
    /** 上传耗时（毫秒） */
    @Builder.Default
    private long uploadDuration = 0;
    
    /** 错误信息（上传失败时） */
    private String errorMessage;
    
    /**
     * 创建成功的上传结果
     */
    public static ImageUploadResult success(String uploadedPath, String originalFileName, long fileSize) {
        return ImageUploadResult.builder()
            .success(true)
            .uploadedPath(uploadedPath)
            .originalFileName(originalFileName)
            .fileSize(fileSize)
            .build();
    }
    
    /**
     * 创建失败的上传结果
     */
    public static ImageUploadResult failure(String errorMessage) {
        return ImageUploadResult.builder()
            .success(false)
            .errorMessage(errorMessage)
            .build();
    }
}
```

### 3. ImageStorageType (图片存储类型枚举)
```java
/**
 * Excel图片存储类型枚举
 * 用于区分不同软件和版本的图片存储机制
 */
public enum ImageStorageType {
    /** 标准OOXML格式（Microsoft Office / 新版WPS） */
    OFFICE_STANDARD("Standard OOXML format"),
    
    /** WPS DISPIMG函数格式 */
    WPS_DISPIMG("WPS DISPIMG function format"),
    
    /** WPS早期版本格式 */
    WPS_LEGACY("WPS legacy format"),
    
    /** 单元格内嵌图片（Excel 365新功能） */
    EMBEDDED_CELL("Cell embedded image format");
    
    private final String description;
    
    ImageStorageType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return name() + " (" + description + ")";
    }
}
```

## ⚙️ Spring配置和集成

### 1. Bean配置
```xml
<!-- applicationContext.xml 新增配置 -->
<bean id="stoneProxyApi" class="com.facishare.paas.metadata.dataloader.proxy.StoneProxyApi">
    <property name="baseUrl" value="${stone.api.baseUrl}" />
    <property name="timeout" value="${stone.api.timeout:30000}" />
</bean>

<!-- 图片上传服务配置 -->
<bean id="imageUploadService" class="com.facishare.paas.metadata.dataloader.image.service.ImageUploadServiceImpl">
    <property name="stoneProxyApi" ref="stoneProxyApi" />
    <property name="maxFileSize" value="${image.upload.maxFileSize:10485760}" />
</bean>

<!-- 统一图片提取器配置 -->
<bean id="unifiedImageExtractor" class="com.facishare.paas.metadata.dataloader.image.extractor.UnifiedImageExtractor" />

<!-- CRM元数据服务配置 -->
<bean id="crmMetadataService" class="com.facishare.paas.metadata.dataloader.service.CrmMetadataService">
    <property name="exportRestProxy" ref="exportRestProxy" />
</bean>
```

### 2. 注解配置
```java
// 服务层组件注册
@Service
public class ImageUploadServiceImpl implements ImageUploadService {
    @Autowired
    private StoneProxyApi stoneProxyApi;

    @Autowired
    private EIEAConverter eieaConverter;
}

@Component
public class UnifiedImageExtractor {
    // 无状态组件，支持并发访问
}

@Service
public class CrmMetadataService {
    @Autowired
    private ExportRestProxy exportRestProxy;
}
```

## 🔧 核心算法实现

### 1. 图片存储类型检测算法
```java
/**
 * 智能检测Excel中图片的存储类型
 * 支持多种Office软件和版本的图片格式
 */
private ImageStorageType detectImageStorageType(String filePath, CellPosition position, int sheetIndex) {
    try (ZipFile zipFile = new ZipFile(filePath)) {
        // 1. 检查WPS DISPIMG函数格式
        if (hasDISPIMGFunction(zipFile, position, sheetIndex)) {
            return ImageStorageType.WPS_DISPIMG;
        }

        // 2. 检查标准OOXML Drawing格式
        if (hasStandardDrawingDefinition(zipFile, position, sheetIndex)) {
            return ImageStorageType.OFFICE_STANDARD;
        }

        // 3. 检查单元格内嵌图片（Excel 365）
        if (hasCellEmbeddedImage(zipFile, position, sheetIndex)) {
            return ImageStorageType.EMBEDDED_CELL;
        }

        // 4. 检查WPS早期版本格式
        if (hasWPSLegacyFormat(zipFile, position, sheetIndex)) {
            return ImageStorageType.WPS_LEGACY;
        }

        return null; // 未检测到图片
    } catch (Exception e) {
        log.warn("Failed to detect image storage type: {}", e.getMessage());
        return null;
    }
}
```

### 2. CRM字段映射算法
```java
/**
 * 智能识别Excel表头中的图片字段
 * 基于CRM元数据进行精确匹配
 */
private void identifyImageColumns(List<String> headerRow) {
    this.imageFieldMappings = new ArrayList<>();

    // 获取CRM对象的字段描述
    List<IFieldDescribe> imageFields = crmMetadataService.getImageFields(apiNameList.get(0), user);

    for (int colIndex = 0; colIndex < headerRow.size(); colIndex++) {
        String headerName = headerRow.get(colIndex);
        if (StringUtils.isBlank(headerName)) {
            continue;
        }

        // 查找匹配的图片字段
        IFieldDescribe matchedField = findMatchingImageField(headerName, imageFields);

        if (matchedField != null) {
            ImageFieldMapping mapping = ImageFieldMapping.builder()
                .fieldName(matchedField.getName())
                .isImageField(true)
                .primaryColumnIndex(colIndex)
                .allColumnIndexes(Arrays.asList(colIndex))
                .isMultiColumn(false)
                .build();

            imageFieldMappings.add(mapping);
            log.info("Identified image field: {} at column {}", matchedField.getName(), colIndex);
        }
    }

    log.info("Total image fields identified: {}", imageFieldMappings.size());
}
```

## 📊 性能优化设计

### 1. 预筛选优化
```java
/**
 * 预筛选机制避免不必要的图片检测
 * 只对识别出的图片字段进行处理
 */
private void handleDataRow(int curRow, List<String> rowData, int sheetIndex) {
    // 快速跳过：没有图片字段时直接返回
    if (CollectionUtils.isEmpty(imageFieldMappings)) {
        return;
    }

    // 只处理图片字段列
    for (ImageFieldMapping fieldMapping : imageFieldMappings) {
        if (!fieldMapping.isImageField()) {
            continue;
        }

        int colIndex = fieldMapping.getPrimaryColumnIndex();
        if (colIndex < rowData.size()) {
            String originalValue = rowData.get(colIndex);
            String processedValue = processImageCell(originalValue, curRow, colIndex, sheetIndex);

            if (!Objects.equals(originalValue, processedValue)) {
                rowData.set(colIndex, processedValue);
            }
        }
    }
}
```

### 2. 多层降级处理算法
```java
/**
 * 图片单元格处理的多层降级机制
 * 确保任何情况下都不会影响正常导入
 */
private String processImageCell(String cellValue, int row, int col, int sheetIndex) {
    // 第一层：检查是否为文本路径格式（需要跳过）
    if (isTextPathFormat(cellValue)) {
        return cellValue; // 保持原值不变
    }

    try {
        // 第二层：尝试提取图片
        CellPosition position = CellPosition.of(row, col);
        byte[] imageData = imageExtractor.extractImage(fileName, position, sheetIndex);

        if (imageData != null && imageData.length > 0) {
            // 第三层：尝试上传图片
            try {
                String format = detectImageFormat(imageData);
                String fileName = generateImageFileName(row, col, format);

                ImageUploadResult result = imageUploadService.uploadImage(imageData, fileName, format, user);

                if (result.isSuccess()) {
                    return result.getUploadedPath();
                } else {
                    log.warn("Image upload failed: {}", result.getErrorMessage());
                }
            } catch (Exception uploadException) {
                log.warn("Image upload exception: {}", uploadException.getMessage());
            }
        }
    } catch (Exception extractException) {
        log.warn("Image extraction failed: {}", extractException.getMessage());
    }

    // 最终降级：返回原始值
    return cellValue;
}
```
```

### 4. ImageFieldMapping (图片字段映射)
```java
/**
 * 图片字段映射
 * 用于记录Excel表头中图片字段的映射关系
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImageFieldMapping {
    /** 字段名称 */
    private String fieldName;
    
    /** 是否为图片字段 */
    private boolean isImageField;
    
    /** 主列索引 */
    private int primaryColumnIndex;
    
    /** 所有相关列索引 */
    private List<Integer> allColumnIndexes;
    
    /** 多图片分隔符 */
    @Builder.Default
    private String separator = "|";
    
    /** 是否为多列字段 */
    private boolean isMultiColumn;
    
    /**
     * 获取需要清空的列索引
     * @return 需要清空的列索引列表
     */
    public List<Integer> getColumnsToClean() {
        if (!isMultiColumn || allColumnIndexes.size() <= 1) {
            return Collections.emptyList();
        }
        return allColumnIndexes.subList(1, allColumnIndexes.size());
    }
}
```
