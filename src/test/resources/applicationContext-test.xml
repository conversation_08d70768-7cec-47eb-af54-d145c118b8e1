<?xml version="1.0" encoding="UTF-8"?>
<!--suppress ALL -->
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop.xsd
       http://www.springframework.org/schema/task
       http://www.springframework.org/schema/task/spring-task.xsd">

    <context:annotation-config/>

    <context:component-scan base-package="com.facishare.paas.metadata.dataloader,
                                        com.facishare.paas.metadata"/>
    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <!-- 定时执行及线程池配置 -->
    <bean id="monitorAsyncTaskExecutor" class="com.github.trace.executor.MonitorAsyncTaskExecutor"
          c:executor-ref="importExecutor"/>
    <task:annotation-driven executor="monitorAsyncTaskExecutor"/>
    <bean id="importTaskExecutor" class="com.github.trace.executor.MonitorAsyncTaskExecutor"
          c:executor-ref="importExecutor"/>
    <task:executor id="importExecutor" pool-size="5-50" queue-capacity="20" rejection-policy="CALLER_RUNS"/>


    <!-- dubbo config -->
    <bean id="autoConf"
          class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:application.properties"
          p:configName="

        async-job-center-callback
        fs-paas-metadata-dataloader"/>


    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <!--index service-->
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>
    <import resource="classpath:META-INF/spring/fs-fsi-proxy-service.xml"/>
    <!--  &lt;!&ndash; 文件上传配置&ndash;&gt;-->
    <!--  <bean id="fsiServiceProxyFactory" class="com.facishare.fsi.proxy.FsiServiceProxyFactory" init-method="init">-->
    <!--    <property name="configKey" value="fs-qixin-fsi-proxy"/>-->
    <!--  </bean>-->
    <!--  <bean id="globalConfigService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">-->
    <!--    <property name="factory" ref="fsiServiceProxyFactory"/>-->
    <!--    <property name="type" value="com.facishare.fsi.proxy.service.GlobalConfigService"/>-->
    <!--  </bean>-->
    <!--  <bean id="fsiWarehouseProxyFactory" class="com.facishare.fsi.proxy.FsiWarehouseProxyFactory" init-method="init">-->
    <!--    <property name="configKey" value="fs-qixin-fsi-proxy"/>-->
    <!--    <property name="globalConfigService" ref="globalConfigService"/>-->
    <!--  </bean>-->
    <!--  <bean id="aFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">-->
    <!--    <property name="factory" ref="fsiWarehouseProxyFactory"/>-->
    <!--    <property name="type" value="com.facishare.fsi.proxy.service.AFileStorageService"/>-->
    <!--  </bean>-->
    <!--  <bean id="nFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">-->
    <!--    <property name="factory" ref="fsiWarehouseProxyFactory"/>-->
    <!--    <property name="type" value="com.facishare.fsi.proxy.service.NFileStorageService"/>-->
    <!--  </bean>-->

    <!--Rest Proxy配置-->
    <bean id="serviceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-paas-dataloader-rest" init-method="init"/>

    <!--Export REST接口-->
    <bean id="export" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.metadata.dataloader.rest.ExportRestProxy">
        <property name="factory" ref="serviceProxyFactory"/>
    </bean>
</beans>
