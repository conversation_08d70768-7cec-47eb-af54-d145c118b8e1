package com.facishare.paas.metadata.dataloader.util


import com.facishare.paas.metadata.dataloader.model.User
import com.facishare.paas.metadata.dataloader.service.FileService
import com.fasterxml.jackson.dataformat.csv.CsvMapper
import com.fasterxml.jackson.dataformat.csv.CsvSchema
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.junit.Test
import org.junit.runner.RunWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner

/**
 * Created by z<PERSON><PERSON><PERSON> on 2016/12/5.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:applicationContext-test.xml")
class FileServiceTest {
    @Autowired
    private FileService fileService;

    static {
        System.setProperty("process.profile", "fstest");
    }

    @Test
    void testUploadAndDownloadFile() {
        CsvSchema csvSchema = CsvSchema.builder().addColumn("name").addColumn("age").addColumn("shen").build()
        System.out.println(csvSchema);
        CsvMapper csvMapper = CsvMapper.csvBuilder().build();
        List<Map<String, String>> data = Lists.newArrayList();
        Map<String, String> value = Maps.newHashMap();
        value.put("name", "zhangshan");
        value.put("age", null)
        value.put("shen", "1")
        data.add(value)
        Map<String, String> value2 = Maps.newHashMap();
        value2.put("name", "李四")
        value2.put("age", "")
        value2.put("shen", "2")
        data.add(value2)
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream()
        csvMapper.writer(csvSchema).writeValue(outputStream, data)
        List<Map<String, String>> data2 = Lists.newArrayList();
        Map<String, String> value4 = Maps.newHashMap();
        value4.put("name", "121");
        value4.put("age", "1231")
        value4.put("shen", "eqweq")
        data2.add(value4)
        Map<String, String> value3 = Maps.newHashMap();
        value3.put("name", "21321")
        value3.put("age", "")
        value3.put("shen", "eweq")
        data2.add(value3)
        csvMapper.writer(csvSchema).writeValue(outputStream, data2)
        String csv = fileService.uploadFile(User.builder().ea("obj0509").tenantId("78057").userId("1000").build(), outputStream.toByteArray(), "csv");
        System.out.println(csv)
    }

}
