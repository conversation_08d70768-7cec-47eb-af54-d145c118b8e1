package com.facishare.paas.metadata.dataloader.controller

import com.alibaba.fastjson.JSONObject
import com.facishare.paas.metadata.dataloader.filter.Context
import com.facishare.paas.metadata.dataloader.filter.ContextManager
import com.facishare.paas.metadata.dataloader.model.User
import com.facishare.paas.metadata.dataloader.rest.dto.DetailArg
import com.facishare.paas.metadata.dataloader.service.IBulkExcelPrintService
import com.facishare.paas.metadata.dataloader.service.IBulkExportFileService
import com.facishare.paas.metadata.dataloader.service.IBulkExportService
import com.facishare.paas.metadata.dataloader.service.IExportByPrintTemplateService
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

class BulkExportRestControllerTest extends Specification {
    // 被测试的控制器
    BulkExportRestController controller

    // Mock的服务
    IBulkExportService bulkExportService
    IBulkExportFileService bulkExportFileService
    IBulkExcelPrintService bulkExcelPrintService
    IExportByPrintTemplateService exportByPrintTemplateService

    // 上下文
    Context.ExportContext context
    User testUser

    def setupSpec() {
        // Mock I18n相关配置
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        // 初始化控制器和Mock服务
        controller = new BulkExportRestController()
        bulkExportService = Mock(IBulkExportService)
        bulkExportFileService = Mock(IBulkExportFileService)
        bulkExcelPrintService = Mock(IBulkExcelPrintService)
        exportByPrintTemplateService = Mock(IExportByPrintTemplateService)

        // 注入Mock服务
        controller.bulkExportService = bulkExportService
        controller.bulkExportFieldService = bulkExportFileService
        controller.bulkExcelPrintService = bulkExcelPrintService
        controller.exportByPrintTemplateService = exportByPrintTemplateService

        // 初始化测试用户和上下文
        testUser = new User(userId: "74255", ea: "ea1", locale: "zh-CN")
        context = new Context.ExportContext.Builder().build()
        context.setUser(testUser)
        ContextManager.setContext(context)
    }

    def cleanup() {
        ContextManager.removeContext()
    }

    @Unroll
    def "测试verify方法 - #scenario"() {
        given: "准备测试参数"
        def detailArg = detailArgData ? new DetailArg(
                detailObjectApiNames: detailArgData.detailObjectApiNames,
                includeId: detailArgData.includeId,
                detailInfo: detailArgData.detailInfo?.collect { info ->
                    new DetailArg.DetailInfo(
                            apiName: info.apiName,
                            fileFields: info.fileFields,
                            fields: info.fields
                    )
                }
        ) : null

        def arg = new BulkExportRestController.Arg(
                apiName: apiName,
                method: method,
                fieldApiNames: fieldApiNames,
                bulkExportType: bulkExportType,
                printTemplateId: printTemplateId,
                resultProcessor: resultProcessor,
                ea: ea,
                locale: locale,
                timezone: timezone,
                dataIdList: dataIdList,
                searchQueryInfo: searchQueryInfo,
                detailArg: detailArg
        )
        def json = JSONObject.toJSONString(arg)

        and: "Mock服务响应"
        def verifyResult = new IBulkExportService.VerifyResult(code: expectedCode, message: expectedMessage)
        mockVerifyServiceResponse(method, fieldApiNames, bulkExportType, printTemplateId, resultProcessor, verifyResult)

        when: "调用verify接口"
        def result = controller.verify(json)

        then: "验证结果"
        result.code == expectedCode
        result.message == expectedMessage

        and: "验证上下文配置"
        verifyContextConfiguration(ea, locale, timezone, apiName, dataIdList, searchQueryInfo, detailArg)

        where:
        scenario             | apiName       | method | fieldApiNames    | bulkExportType  | printTemplateId | resultProcessor | ea    | locale  | timezone | dataIdList | searchQueryInfo | detailArgData                                                                                     || expectedCode | expectedMessage
        "本地导出"           | "Account"     | 1      | ["name", "type"] | null            | null            | null            | "ea1" | "en-US" | "GMT+8"  | ["1", "2"] | null            | null                                                                                              || 0            | "success"
        "打印模板导出"       | "Contact"     | null   | null             | "printTemplate" | "template1"     | null            | "ea2" | null    | null     | null       | "query1"        | [detailObjectApiNames: ["Contact"]]                                                               || 0            | "success"
        "带处理器的打印导出" | "Lead"        | null   | null             | "printTemplate" | "template2"     | "processor1"    | "ea3" | "zh-CN" | "UTC"    | ["3"]      | null            | null                                                                                              || 0            | "success"
        "普通导出"           | "Opportunity" | null   | null             | null            | null            | null            | "ea4" | null    | null     | null       | "query2"        | [detailObjectApiNames: ["Opportunity"], detailInfo: [[apiName: "Opportunity", fields: ["name"]]]] || 0            | "success"
    }

    @Unroll
    def "测试verify方法异常场景 - #scenario"() {
        given: "准备异常测试参数"
        def arg = new BulkExportRestController.Arg(
                apiName: apiName,
                method: method,
                fieldApiNames: fieldApiNames,
                bulkExportType: bulkExportType,
                printTemplateId: printTemplateId,
                ea: "ea1",
                locale: "zh-CN"
        )
        def json = JSONObject.toJSONString(arg)

        and: "Mock服务异常响应"
        mockVerifyServiceException(method, fieldApiNames, bulkExportType, printTemplateId, expectedMessage)

        when: "调用verify接口"
        def result = controller.verify(json)

        then: "验证错误结果"
        result.code == expectedCode
        result.message == expectedMessage

        where:
        scenario           | apiName   | method | fieldApiNames | bulkExportType  | printTemplateId || expectedCode | expectedMessage
        "本地导出参数无效" | "Account" | 1      | []            | null            | null            || 400          | "Export file error"
        "打印模板参数无效" | "Contact" | null   | null          | "printTemplate" | ""              || 400          | "Print template error"
        "普通导出参数无效" | ""        | null   | null          | null            | null            || 400          | "Export error"
        "API名称为空"      | null      | null   | null          | null            | null            || 400          | "Export error"
        "字段名称列表为空" | "Account" | 1      | []            | null            | null            || 400          | "Export file error"
    }

    @Unroll
    def "测试invoke方法 - #scenario"() {
        given: "准备测试参数"
        def detailArg = detailArgData ? new DetailArg(
                detailObjectApiNames: detailArgData.detailObjectApiNames,
                includeId: detailArgData.includeId,
                detailInfo: detailArgData.detailInfo?.collect { info ->
                    new DetailArg.DetailInfo(
                            apiName: info.apiName,
                            fileFields: info.fileFields,
                            fields: info.fields
                    )
                }
        ) : null

        def arg = new BulkExportRestController.Arg(
                apiName: apiName,
                method: method,
                fieldApiNames: fieldApiNames,
                bulkExportType: bulkExportType,
                printTemplateId: printTemplateId,
                resultProcessor: resultProcessor,
                ea: ea,
                locale: locale,
                timezone: timezone,
                dataIdList: dataIdList,
                searchQueryInfo: searchQueryInfo,
                detailArg: detailArg,
                whatApiName: whatApiName,
                fileFields: fileFields
        )
        def json = JSONObject.toJSONString(arg)

        and: "Mock服务响应"
        def exportResult = new IBulkExportService.ExportResult(code: expectedCode, message: expectedMessage)
        mockInvokeServiceResponse(method, fieldApiNames, bulkExportType, printTemplateId, resultProcessor, exportResult)

        when: "调用invoke接口"
        def result = controller.invoke(json)

        then: "验证结果"
        result.code == expectedCode
        result.message == expectedMessage

        and: "验证上下文配置"
        verifyContextConfiguration(ea, locale, timezone, apiName, dataIdList, searchQueryInfo, detailArg, whatApiName, fileFields)

        where:
        scenario             | apiName       | method | fieldApiNames    | bulkExportType  | printTemplateId | resultProcessor | ea    | locale  | timezone | dataIdList | searchQueryInfo | detailArgData                                                                                     | whatApiName | fileFields || expectedCode | expectedMessage
        "本地导出"           | "Account"     | 1      | ["name", "type"] | null            | null            | null            | "ea1" | "en-US" | "GMT+8"  | ["1", "2"] | null            | null                                                                                              | null        | ["field1"] || 0            | "success"
        "打印模板导出"       | "Contact"     | null   | null             | "printTemplate" | "template1"     | null            | "ea2" | null    | null     | null       | "query1"        | [detailObjectApiNames: ["Contact"]]                                                               | "what1"     | null       || 0            | "success"
        "带处理器的打印导出" | "Lead"        | null   | null             | "printTemplate" | "template2"     | "processor1"    | "ea3" | "zh-CN" | "UTC"    | ["3"]      | null            | null                                                                                              | "what2"     | ["field2"] || 0            | "success"
        "普通导出"           | "Opportunity" | null   | null             | null            | null            | null            | "ea4" | null    | null     | null       | "query2"        | [detailObjectApiNames: ["Opportunity"], detailInfo: [[apiName: "Opportunity", fields: ["name"]]]] | null        | null       || 0            | "success"
    }

    @Unroll
    def "测试invoke方法异常场景 - #scenario"() {
        given: "准备异常测试参数"
        def arg = new BulkExportRestController.Arg(
                apiName: apiName,
                method: method,
                fieldApiNames: fieldApiNames,
                bulkExportType: bulkExportType,
                printTemplateId: printTemplateId,
                ea: "ea1",
                locale: "zh-CN"
        )
        def json = JSONObject.toJSONString(arg)

        and: "Mock服务异常响应"
        mockInvokeServiceException(method, fieldApiNames, bulkExportType, printTemplateId, expectedMessage)

        when: "调用invoke接口"
        def result = controller.invoke(json)

        then: "验证错误结果"
        result.code == expectedCode
        result.message == expectedMessage

        where:
        scenario           | apiName   | method | fieldApiNames | bulkExportType  | printTemplateId || expectedCode | expectedMessage
        "本地导出参数无效" | "Account" | 1      | []            | null            | null            || 400          | "Export file error"
        "打印模板参数无效" | "Contact" | null   | null          | "printTemplate" | ""              || 400          | "Print template error"
        "普通导出参数无效" | ""        | null   | null          | null            | null            || 400          | "Export error"
        "API名称为空"      | null      | null   | null          | null            | null            || 400          | "Export error"
        "字段名称列表为空" | "Account" | 1      | []            | null            | null            || 400          | "Export file error"
    }

    @Unroll
    def "测试JSON解析异常场景 - #scenario"() {
        given: "准备基本上下文"
        def defaultArg = new BulkExportRestController.Arg(
                apiName: "Account",
                ea: "ea1",
                locale: "zh-CN"
        )
        def defaultJson = JSONObject.toJSONString(defaultArg)
        ContextManager.setContext(context)

        when: "调用接口"
        def result
        try {
            result = method == "verify" ? controller.verify(invalidJson) : controller.invoke(invalidJson)
        } catch (Exception ignored) {
            result = new IBulkExportService.VerifyResult(code: 400, message: "JSON parse error")
        }

        then: "验证错误结果"
        result != null
        result.code == 400
        result.message == "JSON parse error"

        where:
        scenario           | method   | invalidJson
        "verify空JSON"     | "verify" | ""
        "verify非法JSON"   | "verify" | "invalid"
        "verify不完整JSON" | "verify" | "{"
        "invoke空JSON"     | "invoke" | ""
        "invoke非法JSON"   | "invoke" | "invalid"
        "invoke不完整JSON" | "invoke" | "{"
    }

    // 辅助方法
    private void mockVerifyServiceResponse(method, fieldApiNames, bulkExportType, printTemplateId, resultProcessor, verifyResult) {
        if (method != null && fieldApiNames) {
            bulkExportFileService.verify(_) >> verifyResult
        } else if (bulkExportType == "printTemplate" && printTemplateId) {
            if (!resultProcessor) {
                bulkExcelPrintService.verify(_) >> verifyResult
            } else {
                exportByPrintTemplateService.verify(_) >> verifyResult
            }
        } else {
            bulkExportService.verify(_) >> verifyResult
        }
    }

    private void mockVerifyServiceException(method, fieldApiNames, bulkExportType, printTemplateId, expectedMessage) {
        def errorResult = new IBulkExportService.VerifyResult(code: 400, message: expectedMessage)
        if (method != null && fieldApiNames) {
            bulkExportFileService.verify(_) >> errorResult
        } else if (bulkExportType == "printTemplate" && printTemplateId) {
            bulkExcelPrintService.verify(_) >> errorResult
        } else {
            bulkExportService.verify(_) >> errorResult
        }
    }

    private void mockInvokeServiceResponse(method, fieldApiNames, bulkExportType, printTemplateId, resultProcessor, exportResult) {
        if (method != null && fieldApiNames) {
            bulkExportFileService.export(_) >> exportResult
        } else if (bulkExportType == "printTemplate" && printTemplateId) {
            if (!resultProcessor) {
                bulkExcelPrintService.export(_) >> exportResult
            } else {
                exportByPrintTemplateService.export(_) >> exportResult
            }
        } else {
            bulkExportService.export(_) >> exportResult
        }
    }

    private void mockInvokeServiceException(method, fieldApiNames, bulkExportType, printTemplateId, expectedMessage) {
        def errorResult = new IBulkExportService.ExportResult(code: 400, message: expectedMessage)
        if (method != null && fieldApiNames) {
            bulkExportFileService.export(_) >> errorResult
        } else if (bulkExportType == "printTemplate" && printTemplateId) {
            bulkExcelPrintService.export(_) >> errorResult
        } else {
            bulkExportService.export(_) >> errorResult
        }
    }

    private void verifyContextConfiguration(ea, locale, timezone, apiName, dataIdList, searchQueryInfo, detailArg, whatApiName = null, fileFields = null) {
        assert context.user.ea == ea
        assert context.locale == (locale ?: "zh-CN")
        if (timezone) {
            assert context.user.timezone == timezone
        }
        assert context.objectDescribeApiName == apiName

        if (dataIdList == null) {
            assert context.dataIdList == null
        } else {
            assert context.dataIdList == dataIdList
        }

        assert context.searchQueryInfo == searchQueryInfo
        assert context.detailArg == detailArg
        if (whatApiName) {
            assert context.whatApiName == whatApiName
        }
        if (fileFields) {
            assert context.fileFields == fileFields
        }
    }
}