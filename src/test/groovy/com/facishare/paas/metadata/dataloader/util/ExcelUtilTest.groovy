package com.facishare.paas.metadata.dataloader.util

import org.junit.Assert
import org.junit.Test

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/12/5.
 */
class ExcelUtilTest {
  @Test
  void test_read_xls_Excel() {
//    def pathXls = this.getClass().getClassLoader().getResource("importFiles/线索导入模板.xls").toURI().getPath()
//    def dataXls = new byte[40960];
//    def isXls = new FileInputStream(new File(pathXls));
//    isXls.read(dataXls)
//    def excelUtil = ExcelUtil.getInstance()
//    def workBook = excelUtil.getWorkBook(dataXls, "xls");
//    Assert.assertNotNull(workBook)
//    def sheet = excelUtil.getSheet(workBook, 0)
//    Assert.assertNotNull(sheet)
//    def row = excelUtil.getRow(sheet, 0)
//    Assert.assertNotNull(row)
//    def cell = excelUtil.getCell(row, 0)
//    Assert.assertNotNull(cell)
//    def content = excelUtil.getCellContent(cell)
//    Assert.assertNotNull(content)
  }

  @Test
  void test_read_xlsx_Excel() {
//    def pathXlsx = this.getClass().getClassLoader().getResource("importFiles/线索导入模板.xlsx").toURI().getPath()
//    def dataXlsx = new byte[40960];
//    def isXlsx = new FileInputStream(new File(pathXlsx));
//    isXlsx.read(dataXlsx)
//    def excelUtil = ExcelUtil.getInstance()
//    def workBook = excelUtil.getWorkBook(dataXlsx, "xlsx");
//    Assert.assertNotNull(workBook)
//    def sheet = excelUtil.getSheet(workBook, 0)
//    Assert.assertNotNull(sheet)
//    def row = excelUtil.getRow(sheet, 0)
//    Assert.assertNotNull(row)
//    def cell = excelUtil.getCell(row, 0)
//    Assert.assertNotNull(cell)
//    def content = excelUtil.getCellContent(cell)
//    Assert.assertNotNull(content)

  }
}
