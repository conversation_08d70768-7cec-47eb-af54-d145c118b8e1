package com.facishare.paas.metadata.dataloader.util

import org.junit.Test

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/12/28.
 */
class Excel2003ReaderTest {
  @Test
  void test(){
//    def path = this.getClass().getClassLoader().getResource("importFiles/NotExcel.xls").toURI().getPath()
//    def reader = new Excel2003Reader();
//    reader.setRowReader(new IRowReader() {
//      @Override
//      boolean getRows(int sheetIndex, int curRow, List<String> rowList) {
//        println sheetIndex
//        println curRow
//        println rowList
//      }
//    })
//    reader.process(path)
  }
}
