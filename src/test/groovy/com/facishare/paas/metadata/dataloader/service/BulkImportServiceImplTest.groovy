package com.facishare.paas.metadata.dataloader.service

import com.facishare.paas.metadata.dataloader.filter.Context
import com.facishare.paas.metadata.dataloader.model.User
import com.fxiaoke.api.IdGenerator
import org.apache.poi.ss.usermodel.WorkbookFactory
import org.junit.Test
import org.junit.runner.RunWith
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/12/5.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:applicationContext.xml")
class BulkImportServiceImplTest {
    static {
        System.setProperty("process.profile", "fstest");
    }

//  @Autowired
//  IBulkImportService bulkImportService
//  @Autowired
//  FileService webFileUtil

    //@Test
    void testValidateImportRequest() {
//    def fileExt = ExcelUtil.EXCEL2003
//    //def filePath = "TN_43beee75cc4e4eb8b4f929208cc66318"
//    def filePath = "TN_3756f779fba94356b50afad29ed5a042"
//    bulkImportService.importData(filePath, fileExt, "salesclue", "7", "7", "1401", false, false, "job1")
    }

    //@Test
    void testImportData() {
//    def fileExt = ExcelUtil.EXCEL2003
//    def path = this.getClass().getClassLoader().getResource("importFiles/线索导入模板.xls").toURI().getPath()
//    //def path = this.getClass().getClassLoader().getResource("importFiles/线索导入模板(3085).xlsx").toURI().getPath()
//    def inputStream = new FileInputStream(path)
//    def filePath = webFileUtil.uploadFile("7", "1401", inputStream, fileExt)
//    println filePath
//    def mockDataService = [
//        bulkInsert: { List<IObjectData> objectDataList, String apiName, String tenantId,
//                      boolean isOverride, boolean isInsertNew, Map context ->
//          def map = Maps.newHashMap()
//          map.put(IBulkImportRestService.VALUE, Maps.newHashMap())
//
//          map
//        }
//    ] as ObjectDataServiceRESTImpl

//    def mockHttpUtil = [
//        "doPost"       : { String url, Map<String, String> param, Map<String, String> header
//          ->
//
//          ""
//        },
//        "parseResponse": { String json
//          ->
//          def jsonObj = new JSONObject()
//          jsonObj.put("code", 0)
//
//          jsonObj
//        }
//    ] as HttpRequestUtil
        //bulkImportService.dataServiceREST = mockDataService
        //bulkImportService.httpRequestUtil = mockHttpUtil

        //   bulkImportService.importData(filePath, fileExt, "salesclue", "2", "2", "1000", false, false, "job1")
    }

    //@Test
    void downFile() {
        def filePath = "TN_5f4d1d38aa67412f9c8f7baa5ddf17c4"
        def path = this.getClass().getClassLoader().getResource("importFiles/线索列表 .xlsx").toURI().getPath()
        def data = webFileUtil.getFileData(filePath, "53712", "1005")
        def fis = new ByteArrayInputStream(data)
        def wb = WorkbookFactory.create(fis)
        def fos = new FileOutputStream(path.substring(0, path.length() - 5) + "result.xlsx")
        wb.write(fos)
        fos.flush()
        fos.close()
        wb.close()
        fis.close()
    }


    //@Test
    void test_heartbeat() {
//    def httpRequestUtil = HttpRequestUtil.getInstance()
//    def param = Maps.newHashMap()
//    param.put("jobId", "")
//    param.put("completeRowCount", 558)
//    param.put("totalRowCount", 3985)
//    def result = httpRequestUtil.doPost("http://172.28.0.78:8080/ajc/ping", param, null)
    }


    //@Test
    void test_importComplete() {
//    def param = Maps.newHashMap()
//    param.put("jobId", "999")
//    param.put("result", null)
//    param.put("code", -1)
//    param.put("message", "没权限")
//    def httpRequestUtil = HttpRequestUtil.getInstance()
//    def response = httpRequestUtil.doPost("http://172.28.0.78:8080/ajc/complete", param, null)
    }

    @Test
    void test_dummy() {}

    @Test
    void test_export() {
        def context = new Context.ExportCommonContext()
        context.setExportBizType("login")
        context.setSearchQuery("")
        context.setJobId(IdGenerator.get())
        context.setTenantId("74255")
        context.setUser(User.builder().userId("1031").tenantId("74255").ea("74255").build())
        ExportCSVExecutor exportAction = new ExportCSVExecutor(context)
        exportAction.executeExport()
    }
}
