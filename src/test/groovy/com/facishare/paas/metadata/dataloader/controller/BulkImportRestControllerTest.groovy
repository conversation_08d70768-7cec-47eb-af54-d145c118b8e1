package com.facishare.paas.metadata.dataloader.controller

import com.alibaba.fastjson.JSON
import com.facishare.paas.metadata.dataloader.filter.Context
import com.facishare.paas.metadata.dataloader.filter.ContextManager
import com.facishare.paas.metadata.dataloader.model.MasterInfo
import com.facishare.paas.metadata.dataloader.model.User
import com.facishare.paas.metadata.dataloader.service.IBulkImportService
import com.facishare.paas.metadata.dataloader.util.ExcelUtil
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

class BulkImportRestControllerTest extends Specification {

    BulkImportRestController controller
    IBulkImportService bulkImportService

    def setupSpec() {
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        bulkImportService = Mock(IBulkImportService)
        controller = new BulkImportRestController(bulkImportService: bulkImportService)

        // 初始化Context
        def user = new User(
                userId: "testUser",
                tenantId: "10001",
                ea: "testEa",
                locale: "zh-CN",
                timezone: "GMT+8"
        )
        def context = new Context.ImportContext.Builder().build()
        context.setUser(user)
        ContextManager.setContext(context)
    }

    def cleanup() {
        ContextManager.removeContext()
    }

    @Unroll
    def "should verify excel file with #scenario"() {
        given: "准备测试数据"
        def verifyArg = new BulkImportRestController.VerifyArg()
        verifyArg.with {
            filePath = path
            fileExt = ext
            apiName = api
            unionImportApiNameList = []
            userId = user
            jobId = "testJobId"
            ea = enterpriseAccount
            ei = tenantId
            innerImportType = 1
            matchingType = 2
            isEmptyValueToUpdate = false
            isNoBatch = false
            isUnionDuplicateChecking = false
            locale = lang
            timezone = "GMT+8"
            isWorkFlowEnabled = false
            isApprovalFlowEnabled = false
            isVerifyEnterprise = false
            isBackFillIndustrialAndCommercialInfo = false
            isBackFillOverwriteOldValue = false
            relatedApiNameList = []
            objectCode = "testObjectCode"
            specifiedField = null
            checkOutOwner = false
            removeOutTeamMember = false
            updateOwner = false
            oldOwnerTeamMember = null
            supportFieldMapping = false
            masterInfo = new MasterInfo()
            detailInfo = []
            extendAttribute = [:]
        }

        and: "Mock依赖"
        bulkImportService.verifyData(_ as IBulkImportService.VerifyDataArg) >> IBulkImportService.VerifyResult.builder()
                .code(expectedCode)
                .message(expectedMessage)
                .build()

        when: "执行测试"
        def result = controller.verify(JSON.toJSONString(verifyArg))

        then: "验证结果"
        result != null
        result.code == expectedCode
        result.message == expectedMessage

        where:
        scenario      | path                | ext    | api      | tenantId | enterpriseAccount | user      | lang    | expectedCode | expectedMessage | verifyCount
        "valid xlsx"  | "/path/to/file.xlsx"| "xlsx" | "testApi"| "10001"  | "testEa"         | "testUser"| "zh-CN" | 0           | "验证成功"      | 1
        "invalid txt" | "/path/to/file.txt" | "txt"  | "testApi"| "10001"  | "testEa"         | "testUser"| "zh-CN" | -1          | "请上传Excel文件"| 0
    }

    @Unroll
    def "should invoke import with #scenario"() {
        given: "准备测试数据"
        def invokeArg = new BulkImportRestController.InvokeArg()
        invokeArg.with {
            filePath = path
            fileExt = ext
            apiName = api
            unionImportApiNameList = []
            userId = user
            jobId = "testJobId"
            ea = enterpriseAccount
            ei = tenantId
            innerImportType = 1
            matchingType = 2
            isEmptyValueToUpdate = false
            isNoBatch = false
            isUnionDuplicateChecking = false
            locale = lang
            timezone = "GMT+8"
            isWorkFlowEnabled = false
            isApprovalFlowEnabled = false
            isVerifyEnterprise = false
            isBackFillIndustrialAndCommercialInfo = false
            isBackFillOverwriteOldValue = false
            relatedApiNameList = []
            objectCode = "testObjectCode"
            specifiedField = null
            checkOutOwner = false
            removeOutTeamMember = false
            updateOwner = false
            oldOwnerTeamMember = null
            supportFieldMapping = false
            masterInfo = new MasterInfo()
            detailInfo = []
            extendAttribute = [:]
        }

        when: "执行测试"
        def result = controller.invoke(JSON.toJSONString(invokeArg))

        then: "验证结果"
        result != null
        result.code == expectedCode
        result.message == expectedMessage
        importCount * bulkImportService.importData(_, _)

        where:
        scenario      | path                | ext    | api      | tenantId | enterpriseAccount | user      | lang    | expectedCode | expectedMessage | importCount
        "valid xlsx"  | "/path/to/file.xlsx"| "xlsx" | "testApi"| "10001"  | "testEa"         | "testUser"| "zh-CN" | 0           | null           | 1
        "invalid txt" | "/path/to/file.txt" | "txt"  | "testApi"| "10001"  | "testEa"         | "testUser"| "zh-CN" | -1          | "请上传Excel文件"| 0
    }

    def "should verify complex data successfully"() {
        given: "准备测试数据"
        def verifyArg = new BulkImportRestController.VerifyArg()
        verifyArg.with {
            filePath = "/path/to/file.xlsx"
            fileExt = "xlsx"
            apiName = "testApi"
            ei = "10001"
            ea = "testEa"
            userId = "testUser"
            locale = "zh-CN"
            timezone = "GMT+8"
            unionImportApiNameList = []
            innerImportType = 1
            matchingType = 2
            isEmptyValueToUpdate = true
            isWorkFlowEnabled = true
            isApprovalFlowEnabled = false
            masterInfo = new MasterInfo()
            detailInfo = []
            extendAttribute = [:]
        }

        def json = JSON.toJSONString(verifyArg)

        and: "Mock依赖"
        assert ExcelUtil.getInstance().isExcelFile("xlsx")
        1 * bulkImportService.verifyData(_ as IBulkImportService.VerifyDataArg) >> {
            IBulkImportService.VerifyResult.builder()
                    .code(0)
                    .message("验证成功")
                    .build()
        }

        when: "执行测试"
        def result = controller.verify(json)

        then: "验证结果"
        result != null
        result.code == 0
        result.message == "验证成功"
    }
} 