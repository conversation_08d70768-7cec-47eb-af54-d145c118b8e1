package com.facishare.paas.metadata.dataloader.fix;

import com.facishare.paas.metadata.dataloader.excel.Excel2003UnionReader;
import com.facishare.paas.metadata.dataloader.excel.Excel2007Reader;
import com.facishare.paas.metadata.dataloader.excel.Excel2007UnionReader;
import com.facishare.paas.metadata.dataloader.image.decorator.OptimizedImageAwareRowReader;
import com.facishare.paas.metadata.dataloader.task.ExcelExecutor;
import com.facishare.paas.metadata.dataloader.task.ExcelRowReader;
import com.facishare.paas.metadata.dataloader.util.IRowReader;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.Arrays;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * 测试 getTotalRowCount() 修复是否有效
 * 验证装饰器模式下联合导入Reader能够正确识别ExcelExecutor
 */
@RunWith(MockitoJUnitRunner.class)
public class GetTotalRowCountFixTest {

    @Mock
    private ExcelExecutor mockExcelExecutor;

    @Mock
    private ExcelRowReader mockExcelRowReader;

    @Mock
    private OptimizedImageAwareRowReader mockImageAwareRowReader;

    @Test
    public void testExcel2007UnionReader_getExcelExecutor_withDirectExcelExecutor() throws Exception {
        // 准备测试数据
        Excel2007UnionReader reader = new Excel2007UnionReader(false);

        // 使用反射调用私有方法
        Method getExcelExecutorMethod = Excel2007UnionReader.class.getDeclaredMethod("getExcelExecutor", IRowReader.class);
        getExcelExecutorMethod.setAccessible(true);

        // 测试直接传入ExcelExecutor的情况
        ExcelExecutor result = (ExcelExecutor) getExcelExecutorMethod.invoke(reader, mockExcelExecutor);

        // 验证结果
        assertNotNull("Should return ExcelExecutor when passed directly", result);
        assertEquals("Should return the same ExcelExecutor instance", mockExcelExecutor, result);
    }

    @Test
    public void testExcel2007UnionReader_getExcelExecutor_withImageAwareRowReader() throws Exception {
        // 准备测试数据
        Excel2007UnionReader reader = new Excel2007UnionReader(false);
        when(mockImageAwareRowReader.getOriginalRowReader()).thenReturn(mockExcelExecutor);

        // 使用反射调用私有方法
        Method getExcelExecutorMethod = Excel2007UnionReader.class.getDeclaredMethod("getExcelExecutor", IRowReader.class);
        getExcelExecutorMethod.setAccessible(true);

        // 测试传入OptimizedImageAwareRowReader的情况
        ExcelExecutor result = (ExcelExecutor) getExcelExecutorMethod.invoke(reader, mockImageAwareRowReader);

        // 验证结果
        assertNotNull("Should return ExcelExecutor when wrapped in OptimizedImageAwareRowReader", result);
        assertEquals("Should return the original ExcelExecutor from decorator", mockExcelExecutor, result);
    }

    @Test
    public void testExcel2007UnionReader_getExcelExecutor_withNullInput() throws Exception {
        // 准备测试数据
        Excel2007UnionReader reader = new Excel2007UnionReader(false);

        // 使用反射调用私有方法
        Method getExcelExecutorMethod = Excel2007UnionReader.class.getDeclaredMethod("getExcelExecutor", IRowReader.class);
        getExcelExecutorMethod.setAccessible(true);

        // 测试传入null的情况
        ExcelExecutor result = (ExcelExecutor) getExcelExecutorMethod.invoke(reader, (IRowReader) null);

        // 验证结果
        assertNull("Should return null when input is null", result);
    }

    @Test
    public void testExcel2003UnionReader_getExcelExecutor_withDirectExcelExecutor() throws Exception {
        // 准备测试数据
        Excel2003UnionReader reader = new Excel2003UnionReader();
        
        // 使用反射调用私有方法
        Method getExcelExecutorMethod = Excel2003UnionReader.class.getDeclaredMethod("getExcelExecutor", IRowReader.class);
        getExcelExecutorMethod.setAccessible(true);
        
        // 测试直接传入ExcelExecutor的情况
        ExcelExecutor result = (ExcelExecutor) getExcelExecutorMethod.invoke(reader, mockExcelExecutor);
        
        // 验证结果
        assertNotNull("Should return ExcelExecutor when passed directly", result);
        assertEquals("Should return the same ExcelExecutor instance", mockExcelExecutor, result);
    }

    @Test
    public void testExcel2003UnionReader_getExcelExecutor_withImageAwareRowReader() throws Exception {
        // 准备测试数据
        Excel2003UnionReader reader = new Excel2003UnionReader();
        when(mockImageAwareRowReader.getOriginalRowReader()).thenReturn(mockExcelExecutor);
        
        // 使用反射调用私有方法
        Method getExcelExecutorMethod = Excel2003UnionReader.class.getDeclaredMethod("getExcelExecutor", IRowReader.class);
        getExcelExecutorMethod.setAccessible(true);
        
        // 测试传入OptimizedImageAwareRowReader的情况
        ExcelExecutor result = (ExcelExecutor) getExcelExecutorMethod.invoke(reader, mockImageAwareRowReader);
        
        // 验证结果
        assertNotNull("Should return ExcelExecutor when wrapped in OptimizedImageAwareRowReader", result);
        assertEquals("Should return the original ExcelExecutor from decorator", mockExcelExecutor, result);
    }

    @Test
    public void testExcel2007Reader_getExcelRowReader_withDirectExcelRowReader() throws Exception {
        // 准备测试数据
        Excel2007Reader reader = new Excel2007Reader();

        // 使用反射调用私有方法
        Method getExcelRowReaderMethod = Excel2007Reader.class.getDeclaredMethod("getExcelRowReader", IRowReader.class);
        getExcelRowReaderMethod.setAccessible(true);

        // 测试直接传入ExcelRowReader的情况
        ExcelRowReader result = (ExcelRowReader) getExcelRowReaderMethod.invoke(reader, mockExcelRowReader);

        // 验证结果
        assertNotNull("Should return ExcelRowReader when passed directly", result);
        assertEquals("Should return the same ExcelRowReader instance", mockExcelRowReader, result);
    }

    @Test
    public void testExcel2007Reader_getExcelRowReader_withImageAwareRowReader() throws Exception {
        // 准备测试数据
        Excel2007Reader reader = new Excel2007Reader();
        when(mockImageAwareRowReader.getOriginalRowReader()).thenReturn(mockExcelRowReader);

        // 使用反射调用私有方法
        Method getExcelRowReaderMethod = Excel2007Reader.class.getDeclaredMethod("getExcelRowReader", IRowReader.class);
        getExcelRowReaderMethod.setAccessible(true);

        // 测试传入OptimizedImageAwareRowReader的情况
        ExcelRowReader result = (ExcelRowReader) getExcelRowReaderMethod.invoke(reader, mockImageAwareRowReader);

        // 验证结果
        assertNotNull("Should return ExcelRowReader when wrapped in OptimizedImageAwareRowReader", result);
        assertEquals("Should return the original ExcelRowReader from decorator", mockExcelRowReader, result);
    }

    @Test
    public void testOptimizedImageAwareRowReader_getOriginalRowReader() {
        // 准备测试数据
        when(mockImageAwareRowReader.getOriginalRowReader()).thenReturn(mockExcelExecutor);

        // 测试getOriginalRowReader方法
        IRowReader result = mockImageAwareRowReader.getOriginalRowReader();

        // 验证结果
        assertNotNull("Should return original row reader", result);
        assertEquals("Should return the ExcelExecutor instance", mockExcelExecutor, result);
    }

    /**
     * 集成测试：验证修复后的完整流程
     */
    @Test
    public void testIntegrationFix_decoratorPattern() throws Exception {
        // 这个测试验证装饰器模式下的完整流程
        // 1. 创建ExcelExecutor
        // 2. 用OptimizedImageAwareRowReader包装
        // 3. 联合导入Reader能够正确识别并获取ExcelExecutor
        
        Excel2007UnionReader reader = new Excel2007UnionReader(false);
        when(mockImageAwareRowReader.getOriginalRowReader()).thenReturn(mockExcelExecutor);
        
        // 使用反射调用私有方法
        Method getExcelExecutorMethod = Excel2007UnionReader.class.getDeclaredMethod("getExcelExecutor", IRowReader.class);
        getExcelExecutorMethod.setAccessible(true);
        
        // 模拟装饰器模式的完整流程
        ExcelExecutor result = (ExcelExecutor) getExcelExecutorMethod.invoke(reader, mockImageAwareRowReader);
        
        // 验证修复是否有效
        assertNotNull("Fix should allow union readers to get ExcelExecutor from decorator", result);
        assertEquals("Should get the original ExcelExecutor instance", mockExcelExecutor, result);
        
        System.out.println("✅ getTotalRowCount() fix verification passed!");
        System.out.println("✅ Union readers can now properly identify ExcelExecutor through decorator pattern");
    }
}
