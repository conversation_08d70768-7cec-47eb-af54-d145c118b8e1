package com.facishare.paas.metadata.dataloader.image.service;

import com.facishare.paas.metadata.dataloader.common.DataLoaderConfig;
import org.junit.Test;

import java.util.Set;

import static org.junit.Assert.*;

/**
 * 图片上传服务配置测试
 * 验证支持的图片格式配置功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class ImageUploadServiceConfigTest {

    @Test
    public void testDefaultSupportedImageFormats() {
        // 获取默认支持的图片格式
        Set<String> supportedFormats = DataLoaderConfig.INSTANCE.getSupportedImageFormats();
        
        // 验证不为空
        assertNotNull("Supported formats should not be null", supportedFormats);
        assertFalse("Supported formats should not be empty", supportedFormats.isEmpty());
        
        // 验证包含 FastImageInfoHelper 支持的所有格式
        assertTrue("Should support gif", supportedFormats.contains("gif"));
        assertTrue("Should support jpg", supportedFormats.contains("jpg"));
        assertTrue("Should support jpeg", supportedFormats.contains("jpeg"));
        assertTrue("Should support png", supportedFormats.contains("png"));
        assertTrue("Should support bmp", supportedFormats.contains("bmp"));
        assertTrue("Should support webp", supportedFormats.contains("webp"));
        assertTrue("Should support tiff", supportedFormats.contains("tiff"));
        assertTrue("Should support heif", supportedFormats.contains("heif"));
        
        // 验证格式数量
        assertEquals("Should support 8 formats", 8, supportedFormats.size());
        
        System.out.println("Default supported image formats: " + supportedFormats);
    }
    
    @Test
    public void testImageFormatValidation() {
        Set<String> supportedFormats = DataLoaderConfig.INSTANCE.getSupportedImageFormats();
        
        // 测试支持的格式
        assertTrue("jpg should be supported", supportedFormats.contains("jpg"));
        assertTrue("png should be supported", supportedFormats.contains("png"));
        assertTrue("gif should be supported", supportedFormats.contains("gif"));
        
        // 测试不支持的格式
        assertFalse("pdf should not be supported", supportedFormats.contains("pdf"));
        assertFalse("txt should not be supported", supportedFormats.contains("txt"));
        assertFalse("doc should not be supported", supportedFormats.contains("doc"));
    }
}
