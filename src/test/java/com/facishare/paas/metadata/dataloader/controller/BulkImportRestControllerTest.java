//package com.facishare.paas.metadata.dataloader.controller;
//
//import com.alibaba.fastjson.JSON;
//import com.facishare.paas.metadata.dataloader.model.MasterInfo;
//import com.facishare.paas.metadata.dataloader.service.IBulkImportService;
//import com.facishare.paas.metadata.dataloader.util.ExcelUtil;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.mockito.junit.MockitoJUnitRunner;
//
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.HashMap;
//
//import static org.junit.Assert.assertEquals;
//import static org.junit.Assert.assertNotNull;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.*;
//
//@RunWith(MockitoJUnitRunner.class)
//public class BulkImportRestControllerTest {
//
//    @InjectMocks
//    private BulkImportRestController controller;
//
//    @Mock
//    private IBulkImportService bulkImportService;
//
//    @Mock
//    private ExcelUtil excelUtil;
//
//    @Before
//    public void setup() {
//        MockitoAnnotations.openMocks(this);
//        ExcelUtil.setInstance(excelUtil);
//    }
//
//    @Test
//    public void testVerify_WithValidExcelFile() throws IOException {
//        // 准备测试数据
//        BulkImportRestController.VerifyArg verifyArg = new BulkImportRestController.VerifyArg();
//        verifyArg.setFilePath("/path/to/file.xlsx");
//        verifyArg.setFileExt("xlsx");
//        verifyArg.setApiName("testApi");
//        verifyArg.setEi("testEi");
//        verifyArg.setEa("testEa");
//        verifyArg.setUserId("testUser");
//        verifyArg.setLocale("zh-CN");
//
//        String json = JSON.toJSONString(verifyArg);
//
//        // Mock依赖
//        when(excelUtil.isExcelFile(anyString())).thenReturn(true);
//        when(bulkImportService.verifyData(any())).thenReturn(
//                IBulkImportService.VerifyResult.builder()
//                        .code(0)
//                        .message("验证成功")
//                        .build()
//        );
//
//        // 执行测试
//        BulkImportRestController.RestResult result = controller.verify(json);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(0, result.getCode());
//
//        // 验证方法调用
//        verify(bulkImportService, times(1)).verifyData(any());
//    }
//
//    @Test
//    public void testVerify_WithInvalidExcelFile() {
//        // 准备测试数据
//        BulkImportRestController.VerifyArg verifyArg = new BulkImportRestController.VerifyArg();
//        verifyArg.setFilePath("/path/to/file.txt");
//        verifyArg.setFileExt("txt");
//
//        String json = JSON.toJSONString(verifyArg);
//
//        // Mock依赖
//        when(excelUtil.isExcelFile(anyString())).thenReturn(false);
//
//        // 执行测试
//        BulkImportRestController.RestResult result = controller.verify(json);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(-1, result.getCode());
//
//        // 验证方法调用
//        verify(bulkImportService, never()).verifyData(any());
//    }
//
//    @Test
//    public void testInvoke_WithValidExcelFile() throws IOException {
//        // 准备测试数据
//        BulkImportRestController.InvokeArg invokeArg = new BulkImportRestController.InvokeArg();
//        invokeArg.setFilePath("/path/to/file.xlsx");
//        invokeArg.setFileExt("xlsx");
//        invokeArg.setApiName("testApi");
//        invokeArg.setEi("testEi");
//        invokeArg.setEa("testEa");
//        invokeArg.setUserId("testUser");
//        invokeArg.setJobId("testJob");
//        invokeArg.setLocale("zh-CN");
//
//        String json = JSON.toJSONString(invokeArg);
//
//        // Mock依赖
//        when(excelUtil.isExcelFile(anyString())).thenReturn(true);
//
//        // 执行测试
//        BulkImportRestController.RestResult result = controller.invoke(json);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(0, result.getCode());
//
//        // 验证方法调用
//        verify(bulkImportService, times(1)).importData(any(), any());
//    }
//
//    @Test
//    public void testInvoke_WithInvalidExcelFile() {
//        // 准备测试数据
//        BulkImportRestController.InvokeArg invokeArg = new BulkImportRestController.InvokeArg();
//        invokeArg.setFilePath("/path/to/file.txt");
//        invokeArg.setFileExt("txt");
//
//        String json = JSON.toJSONString(invokeArg);
//
//        // Mock依赖
//        when(excelUtil.isExcelFile(anyString())).thenReturn(false);
//
//        // 执行测试
//        BulkImportRestController.RestResult result = controller.invoke(json);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(-1, result.getCode());
//
//        // 验证方法调用
//        verify(bulkImportService, never()).importData(any(), any());
//    }
//
//    @Test
//    public void testVerify_WithComplexData() throws IOException {
//        // 准备测试数据
//        BulkImportRestController.VerifyArg verifyArg = new BulkImportRestController.VerifyArg();
//        verifyArg.setFilePath("/path/to/file.xlsx");
//        verifyArg.setFileExt("xlsx");
//        verifyArg.setApiName("testApi");
//        verifyArg.setEi("testEi");
//        verifyArg.setEa("testEa");
//        verifyArg.setUserId("testUser");
//        verifyArg.setLocale("zh-CN");
//        verifyArg.setUnionImportApiNameList(new ArrayList<>());
//        verifyArg.setInnerImportType(1);
//        verifyArg.setMatchingType(2);
//        verifyArg.setIsEmptyValueToUpdate(true);
//        verifyArg.setIsWorkFlowEnabled(true);
//        verifyArg.setIsApprovalFlowEnabled(false);
//        verifyArg.setMasterInfo(new MasterInfo());
//        verifyArg.setDetailInfo(new ArrayList<>());
//        verifyArg.setExtendAttribute(new HashMap<>());
//
//        String json = JSON.toJSONString(verifyArg);
//
//        // Mock依赖
//        when(excelUtil.isExcelFile(anyString())).thenReturn(true);
//        when(bulkImportService.verifyData(any())).thenReturn(
//                IBulkImportService.VerifyResult.builder()
//                        .code(0)
//                        .message("验证成功")
//                        .build()
//        );
//
//        // 执行测试
//        BulkImportRestController.RestResult result = controller.verify(json);
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(0, result.getCode());
//
//        // 验证方法调用
//        verify(bulkImportService, times(1)).verifyData(any());
//    }
//}