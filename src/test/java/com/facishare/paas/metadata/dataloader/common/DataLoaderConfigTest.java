package com.facishare.paas.metadata.dataloader.common;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试 DataLoaderConfig 的初始化问题
 */
public class DataLoaderConfigTest {

    @Test
    public void testSupportedImageFormatsNotEmpty() {
        // 测试 supportedImageFormats 是否正确初始化
        DataLoaderConfig config = DataLoaderConfig.INSTANCE;
        
        System.out.println("supportedImageFormats size: " + config.getSupportedImageFormats().size());
        System.out.println("supportedImageFormats content: " + config.getSupportedImageFormats());
        
        // 验证不为空
        assertNotNull(config.getSupportedImageFormats(), "supportedImageFormats should not be null");
        assertFalse(config.getSupportedImageFormats().isEmpty(), "supportedImageFormats should not be empty");
        
        // 验证包含预期的格式
        assertTrue(config.getSupportedImageFormats().contains("jpg"), "Should contain jpg format");
        assertTrue(config.getSupportedImageFormats().contains("png"), "Should contain png format");
    }
}
