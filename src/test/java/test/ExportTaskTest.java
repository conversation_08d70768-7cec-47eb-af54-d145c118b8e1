package test;

import com.facishare.paas.metadata.dataloader.task.ExportTask;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2019/12/31 10:17 上午
 */
public class ExportTaskTest {

    @Test
    public void testThreadPool() {
        for (int i = 0; i < 10; i++) {
//            ExportTask task = ExportTask.createTask("job" + i, "token"+i,100);
//            new Thread(task).start();
        }
    }


}
