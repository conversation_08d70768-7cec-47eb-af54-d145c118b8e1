package test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.github.autoconf.ConfigFactory;
import org.junit.Test;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-10-21 15:21
 */
public class ConfigTest {
    private Map<String, Integer> waitTimes;

    {
        ConfigFactory.getConfig("fs-paas-metadata-dataloader-rate", config -> {
            JSONObject configJSON = JSON.parseObject(config.getString(), Feature.OrderedField);
            waitTimes = configJSON.toJavaObject(Map.class);
        });
    }

    @Test
    public void config() {
        System.out.println(waitTimes.get("78057"));
    }
}
