package test;

import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.service.IExportExecutor;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:applicationContext.xml")
public class ExportCSVExecutorTest {

    static {
        System.setProperty("process.profile", "fstest");
    }

    @Autowired
    IExportExecutor exportExecutor;

    @Test
    public void testExport() {
        Context.ExportCommonContext exportCommonContext = new Context.ExportCommonContext.Builder().build();
        exportCommonContext.setExportBizType("auditLog");
        exportCommonContext.setFileExt("csv");
        exportCommonContext.setSearchQuery("{\"module\":\"UserDefineObj\"}");
        exportCommonContext.setUser(User.builder().ea("obj0509").tenantId("78057").userId("1000").build());
        exportExecutor.exportData(exportCommonContext);
    }
}
