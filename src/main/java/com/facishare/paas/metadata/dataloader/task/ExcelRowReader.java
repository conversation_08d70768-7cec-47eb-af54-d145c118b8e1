package com.facishare.paas.metadata.dataloader.task;

import com.facishare.paas.I18N;
import com.facishare.paas.metadata.dataloader.excel.IExcelReader;
import com.facishare.paas.metadata.dataloader.exception.ExcelReadColumnIndexExceedException;
import com.facishare.paas.metadata.dataloader.util.ExcelUtil;
import com.facishare.paas.metadata.dataloader.util.I18NKey;
import com.facishare.paas.metadata.dataloader.util.IRowReader;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.UnsupportedFileFormatException;
import org.apache.poi.openxml4j.exceptions.OLE2NotOfficeXmlFileException;
import org.apache.poi.poifs.filesystem.NotOLE2FileException;
import org.apache.poi.poifs.filesystem.OfficeXmlFileException;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.util.List;

@Slf4j
public class ExcelRowReader implements IRowReader {
    private IExcelReader excelReader;
    private RowLoadHandler<Integer, List> handler;
    private final String fileExt;

    @Getter
    private int totalRowCount = 0;

    @Getter
    private int currentSheetIndex;

    @Getter
    private String currentSheetName;


    public ExcelRowReader(boolean multiSheets, String fileExt) {
        this.fileExt = fileExt;
        this.currentSheetIndex = -1;
        this.currentSheetName = null;
        excelReader = ExcelUtil.createReader(fileExt, multiSheets, true);
        excelReader.setRowReader(this);
    }

    /**
     * 处理下一个 sheet,更新 lastApiName 和 currentApiName
     *
     * @param sheetName sheet 名称
     */
    public void nextSheet(String sheetName) {
        currentSheetName = sheetName;
        currentSheetIndex++;
    }

    /**
     * 读取sheet中每行的内容
     *
     * @param fileName 文件
     * @param handler  行处理器
     * @throws IOException exception
     */
    public void reader(String fileName, RowLoadHandler<Integer, List> handler) throws IOException {
        this.handler = handler;
        try {
            try {
                excelReader.process(fileName);
            } catch (OfficeXmlFileException | OLE2NotOfficeXmlFileException e) {
                //参数中的文件类型无法解析，尝试使用另一种文件格式解析excel
                excelReader = ExcelUtil.createReverseReader(fileExt, true);
                excelReader.setRowReader(this);
                excelReader.process(fileName);
            }
        } catch (NotOLE2FileException | UnsupportedFileFormatException e) {
            log.warn("The format of Excel is not valid, filePath:{}, fileExt:{}", fileName, fileExt, e);
        }
    }


    public void processSheet(SheetProcess sheetProcess) throws IOException, ParserConfigurationException, SAXException {
        sheetProcess.process();
    }


    @Override
    public boolean getRows(int sheetIndex, int curRow, List<String> rowInfo) {
        if (ExcelUtil.EXCEL2003.equals(this.fileExt)) {
            throw new ExcelReadColumnIndexExceedException(I18N.text(I18NKey.IMPORT_FILE_EXT_ERROR_MESSAGE));
        }
        this.currentSheetIndex = sheetIndex;
        return handler.handle(curRow, rowInfo);
    }

    @Override
    public void setTotalLineCount(int rowCount) {
        totalRowCount += rowCount;
    }

    @FunctionalInterface
    public interface RowLoadHandler<Integer, List> {
        boolean handle(Integer rowIndex, List rowInfo);
    }

    @FunctionalInterface
    public interface SheetProcess {
        void process() throws IOException, ParserConfigurationException, SAXException;
    }
}
