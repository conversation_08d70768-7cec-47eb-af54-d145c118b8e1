package com.facishare.paas.metadata.dataloader.service;

import com.facishare.fsi.proxy.model.warehouse.n.fileupload.*;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.util.FileUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

/**
 * 上传，下载文件
 * <p>
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/11/9.
 */

@Service
@Slf4j
public class FileService {
    private static final String BUSINESS = "CRM";//"METADATA";
    private static final int CHUNK_SIZE = 1024 * 1024;
    private static int expiredDay = 7;

    @Resource
    private NFileStorageService nFileStorageService;

    static {
        ConfigFactory.getConfig("fs-paas-metadata-dataloader",
                iConfig -> expiredDay = iConfig.getInt("expiredDays"));
    }

    public static Long getFileExpiredTimeWithNow() {
        return System.currentTimeMillis() + expiredDay * 24 * 60 * 60 * 1000L;
    }

    public static int getExpiredDay() {
        return expiredDay;
    }

    public byte[] getFileData(String filePath, User user) {
        NChunkFilePullStart.Arg arg = new NChunkFilePullStart.Arg();
        arg.setEa(user.getEa());
        arg.setSourceUser(user.getUserId());
        arg.setPath(filePath);
        arg.setBusiness(BUSINESS);
        log.info("getFileData filePath:{}, user:{}, arg:{}", filePath, user, arg);
        NChunkFilePullStart.Result result = nFileStorageService.nChunkFilePullStart(arg, user.getEa());
        if (null == result) {
            return null;
        }

        if (result.isChunk() && result.isComplete()) {
            //调用分片获取文件的接口
            List<Chunk> chunksInfo = result.getChunksInfo();
            if (CollectionUtils.isEmpty(chunksInfo)) {
                return null;
            }

            NChunkFilePullData.Arg chunkArg = new NChunkFilePullData.Arg();
            chunkArg.setPath(filePath);
            chunkArg.setSourceUser(user.getUserId());
            chunkArg.setEa(user.getEa());

            byte[] totalData = null;
            for (Chunk chunk : chunksInfo) {
                chunkArg.setChunkIndex(chunk.getIndex());
                NChunkFilePullData.Result chunkResult = nFileStorageService.nChunkFilePullData(chunkArg, user.getEa());
                byte[] data = chunkResult.getData();
                if (ArrayUtils.isEmpty(data)) {
                    log.error("Read Chunk data fail, filePath:{}, chunkIndex:{}", filePath, chunk.getIndex());
                    continue;
                }
                totalData = ArrayUtils.addAll(data, totalData);
            }

            return totalData;
        } else {
            //不分片
            return result.getData();
        }

    }

    public NUploadFileDirect.Result uploadFile(User user, byte[] data, String ext) {
        NUploadFileDirect.Arg arg = new NUploadFileDirect.Arg();
        arg.setEa(user.getEa());
        arg.setSourceUser("E." + user.getUserId());
        arg.setData(data);
        arg.setExpireDay(expiredDay);
        arg.setFileExt(ext);

        NUploadFileDirect.Result result = nFileStorageService.nUploadFileDirect(arg, user.getEa());
        if (null == result || Strings.isNullOrEmpty(result.getFinalNPath())) {
            log.error("Can not upload excel file by calling nTempUploadByChunkStart");
            return null;
        }
        return result;
    }

    public FileUtil.LocalFile uploadFile(User user, InputStream inputStream, String ext) {
        byte[] chunk = new byte[CHUNK_SIZE];
        int eachSize;
        ByteArrayOutputStream answer = new ByteArrayOutputStream();
        try {
            while ((eachSize = inputStream.read(chunk)) != -1) {
                answer.write(chunk, 0, eachSize);
            }
        } catch (IOException e) {
            log.error("Error in write data from outputStream when uploading file");
            return null;
        } finally {
            try {
                answer.close();
                inputStream.close();
            } catch (IOException e) {
                log.error("Error in closing ByteArrayOutputStream when upload file", e);
            }
        }
        byte[] totalData = answer.toByteArray();
        NUploadFileDirect.Result result = uploadFile(user, totalData, ext);
        if (Objects.isNull(result)) {
            return FileUtil.LocalFile.builder().build();
        }
        return FileUtil.LocalFile.builder().path(result.getFinalNPath()).size(result.getFileSize()).build();
    }

    public NGetFileMetaData.Result getFileMetaData(User user, String filePath) {
        NGetFileMetaData.Arg arg = new NGetFileMetaData.Arg(user.getEa(), filePath);
        arg.setNeedSHA256(true);
        try {
            return nFileStorageService.nGetFileMetaData(arg, user.getEa());
        } catch (Exception e) {
            log.warn("getFileMetaData fail, ei:{}, filePath:{}", user.getTenantId(), filePath, e);
            return new NGetFileMetaData.Result();
        }
    }

}
