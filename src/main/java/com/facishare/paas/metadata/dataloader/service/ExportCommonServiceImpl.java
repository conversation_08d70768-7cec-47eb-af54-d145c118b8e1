package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.rest.Common;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportCommonDTO;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportCommonVerify;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ExportCommonServiceImpl implements IExportCommonService {

    @Autowired
    private IExportExecutor exportExecutor;

    @Override
    public VerifyResult verify(Context.ExportCommonContext exportContext) {
        VerifyResult result;
        try {
            ExportCommonVerify.Result restResult = exportExecutor.exportVerify(exportContext);
            // 将错误码统一为0或者-1，方便前端处理
            int code = restResult.getCode() == Common.SUCCESS ? Common.SUCCESS : Common.FAIL;
            result = VerifyResult.builder().code(code).message(restResult.getMessage()).build();
        } catch (Exception e) {
            log.error("Error in export verify, tenantId:{}, ea:{},userId:{}",
                    exportContext.getUser().getTenantId(), exportContext.getUser().getEa(),
                    exportContext.getUser().getUserId(), e);
            return VerifyResult.builder().code(Common.FAIL).message("System Error!").build();
        }
        return result;
    }

    @Override
    public ExportResult export(Context.ExportCommonContext arg) {
        ExportCommonDTO.Result result;
        try {
            result = exportExecutor.exportData(arg);
        } catch (Exception e) {
            log.error("Error in export data, tenantId:{}, ea:{}, userId:{}", arg.getUser().getTenantId(),
                    arg.getUser().getEa(), arg.getUser().getUserId(), e);
            return ExportResult.builder().code(Common.FAIL).message("System Error!").build();
        }
        if (result.getCode() != Common.SUCCESS) {
            log.warn("First invoke export failed, arg:{}, result:{}", arg, result);
            return ExportResult.builder().code(Common.FAIL).message(result.getMessage()).build();
        }
        return ExportResult.builder().code(Common.SUCCESS).message("ok").build();
    }
}
