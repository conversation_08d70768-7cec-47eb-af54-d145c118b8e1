package com.facishare.paas.metadata.dataloader.filter;

import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.rest.Common;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import javax.ws.rs.core.UriInfo;
import javax.ws.rs.ext.Provider;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2019-05-29 11:55
 */
@Provider
@Component
@Slf4j
public class RestRequestFilter implements ContainerRequestFilter {
    private static final String URI_IMPORT = "/bulkimport/";
    private static final String URI_EXPORT = "/bulkExport/";
    private static final String URL_EXPORT_COMMON = "/exportCommon/";
    /**
     * 请求头
     */
    private static final String X_FS_EI = "x-fs-ei";
    private static final String X_FS_USER = "x-fs-userInfo";
    private static final String X_OUT_TENANT_ID = "x-out-tenant-id";
    private static final String X_OUT_USER_ID = "x-out-user-id";


    @Override
    public void filter(ContainerRequestContext requestContext) throws IOException {
        // 解析请求头
        String tenantId = requestContext.getHeaderString(Common.HTTP_HEADER_EI);
        String ea = requestContext.getHeaderString(Common.HTTP_HEADER_EA);
        String userId = requestContext.getHeaderString(Common.HTTP_HEADER_UID);
        String outTenantId = requestContext.getHeaderString(Common.HTTP_HEADER_OUT_EI);
        String outUserId = requestContext.getHeaderString(Common.HTTP_HEADER_OUT_USER);
        String locale = requestContext.getHeaderString(Common.HTTP_HEADER_LOCALE);
        String outAppId = requestContext.getHeaderString(Common.HTTP_HEADER_OUT_APP_ID);
        String identityType = requestContext.getHeaderString(Common.OUT_IDENTITY_TYPE);
        String upstreamOwnerId = requestContext.getHeaderString(Common.HTTP_HEADER_UPSTREAM_OWNER_ID);
        // 生成User
        User user = User.builder()
                .userId(userId)
                .ea(ea)
                .outUserId("0".equals(outUserId) ? null : outUserId)
                .tenantId(tenantId)
                .outTenantId("0".equals(outTenantId) ? null : outTenantId)
                .locale(locale)
                .upstreamOwnerId(upstreamOwnerId)
                .identityType(identityType)
                .outAppId(outAppId).build();

        // 根据请求uri来生成导入或者导出的context
        UriInfo uriInfo = requestContext.getUriInfo();
        String path = uriInfo.getPath();
        int index1 = path.indexOf(URI_IMPORT);
        int index2 = path.indexOf(URI_EXPORT);
        int index3 = path.indexOf(URL_EXPORT_COMMON);
        if (index1 > -1) {
            log.info("import from job center, tenantId:{}, userId:{}, outTenantId:{},outUserId:{},locale:{}",
                    tenantId, userId, outTenantId, outUserId, locale);
            Context.ImportContext context = new Context.ImportContext.Builder().build();
            context.setUser(user);
            ContextManager.setContext(context);
        } else if (index2 > -1) {
            log.info("export from job center, tenantId:{}, userId:{}, outTenantId:{},outUserId:{},locale:{}",
                    tenantId, userId, outTenantId, outUserId, locale);
            Context.ExportContext context = new Context.ExportContext.Builder().build();
            context.setUser(user);
            ContextManager.setContext(context);
        } else if (index3 > -1) {
            log.info("export from job center, tenantId:{}, userId:{}, outTenantId:{},outUserId:{},locale:{}",
                    tenantId, userId, outTenantId, outUserId, locale);
            Context.ExportCommonContext context = new Context.ExportCommonContext.Builder().build();
            context.setUser(user);
            ContextManager.setContext(context);
        } else {
            log.info("export from job center, tenantId:{}, userId:{}, outTenantId:{},outUserId:{},locale:{}",
                    tenantId, userId, outTenantId, outUserId, locale);
            Context.NoUsedContext context = new Context.NoUsedContext.Builder().build();
            context.setUser(user);
            ContextManager.setContext(context);
        }
    }
}
