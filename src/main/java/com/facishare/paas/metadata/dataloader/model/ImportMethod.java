package com.facishare.paas.metadata.dataloader.model;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum ImportMethod {
    NORMAL("normal"),
    ADD_ACTION("addAction");

    private final String method;

    ImportMethod(String method) {
        this.method = method;
    }

    public static ImportMethod of(String importMethod) {
        return Arrays.stream(ImportMethod.values()).filter(x -> Objects.equals(x.getMethod(), importMethod)).findFirst().orElse(NORMAL);
    }

    public static boolean isAddAction(String importMethod, int importType) {
        return ImportMethod.ADD_ACTION.equals(ImportMethod.of(importMethod)) && ImportType.INSERT.getValue() == importType;
    }
}
