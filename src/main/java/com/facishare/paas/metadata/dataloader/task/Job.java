package com.facishare.paas.metadata.dataloader.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.facishare.paas.metadata.dataloader.util.HttpRequestUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.*;

@Data
@Slf4j
@Builder
public class Job {
    public static final String COMPLETE_CANCEL_CODE = "-2";
    public static final String COMPLETE_SUCCESS_CODE = "0";
    public static final String COMPLETE_FAIL_CODE = "-1";


    public static ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(5);

    static {
        ((ScheduledThreadPoolExecutor) scheduledExecutorService).setRemoveOnCancelPolicy(true);
    }

    private ScheduledFuture<?> scheduledFuture;
    private String pingUrl;
    private String importCompleteUrl;
    private String jobId;
    private int heartBeatNotifyFailTime = 0;

    private boolean status;

    public Job init() {
        return this;
    }

    public void create(HeartbeatGetter heartbeatGetter, JobCancelCallback jobCancelCallback, JobAbortCallback jobAbortCallback) {
        if (isCreate()) {
            return;
        }
        createPingTask(heartbeatGetter, jobCancelCallback, jobAbortCallback);
    }

    private boolean isCreate() {
        if (status) {
            return true;
        }
        status = true;
        return false;
    }

    private void ping(InvokeResult invokeResult, JobCancelCallback jobCancelCallback, JobAbortCallback jobAbortCallback) {
        String result = HttpRequestUtil.getInstance().doPost(pingUrl, JSON.toJSONString(invokeResult), null);
        log.info("Heart beat ping Result:[{}]", result);

        try {
            CancelResult cancelResult = JSON.parseObject(result, CancelResult.class);
            if (cancelResult.isJobCancel()) {
                jobCancelCallback.cancelJob();
            } else if (cancelResult.isJobCompleted()) {
                jobAbortCallback.abortJob();
            }

            if (heartBeatNotifyFailTime > 12) {
                log.info("HeartBeat ping fail more than 12 times， stop the timer");
                jobAbortCallback.abortJob();
            }

        } catch (JSONException e) {
            heartBeatNotifyFailTime++;
            log.warn("Can not parse JSON when parse HeartBeat response, result:{}", result);
        }
    }

    public void stopHeartBeats() {
        if (null == scheduledFuture) {
            log.info("scheduledFuture is null when stopping heart beats");
            return;
        }
        if (!scheduledFuture.isCancelled()) {
            boolean success = scheduledFuture.cancel(true);
            log.info("Cancel the schedule, scheduledFuture.cancel() return{}, scheduledFuture isCanceld:{}",
                    success, scheduledFuture.isCancelled());
        }
    }

    private void createPingTask(HeartbeatGetter heartbeatGetter, JobCancelCallback jobCancelCallback, JobAbortCallback jobAbortCallback) {
        scheduledFuture = scheduledExecutorService.scheduleAtFixedRate(() -> {
                    if (!Thread.interrupted()) {
                        ping(heartbeatGetter.getParam(), jobCancelCallback, jobAbortCallback);
                    }
                },
                200,
                5000,
                TimeUnit.MILLISECONDS);
        log.info("Start timer for Import done");
    }

    public void completeJob(InvokeResult invokeResult) {
        log.info("Send complete Request, Result filePath:{}, jobId:{}", invokeResult.getResult(), jobId);
        HttpRequestUtil.getInstance().doPost(importCompleteUrl, JSON.toJSONString(invokeResult), null);
    }

    @FunctionalInterface
    public interface JobCancelCallback {
        void cancelJob();
    }

    @FunctionalInterface
    public interface JobAbortCallback {
        void abortJob();
    }

    @FunctionalInterface
    public interface HeartbeatGetter {
        InvokeResult getParam();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CancelResult {
        private static final int TASK_SUCCESS = 2;
        private static final int TASK_FAIL = 3;
        private static final int TASK_ERROR = 4;
        private static final int TASK_CANCELING = 5;
        private static final int TASK_CANCELED = 6;

        Integer status;

        public boolean isJobCancel() {
            return Objects.equals(status, TASK_CANCELING) || Objects.equals(status, TASK_CANCELED);
        }

        public boolean isJobCompleted() {
            return Objects.equals(status, TASK_SUCCESS) || Objects.equals(status, TASK_ERROR) || Objects.equals(status, TASK_FAIL);
        }

    }

}
