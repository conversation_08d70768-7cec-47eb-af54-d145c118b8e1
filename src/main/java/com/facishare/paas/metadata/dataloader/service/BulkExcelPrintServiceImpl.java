package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.common.Config;
import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.rest.Common;
import com.facishare.paas.metadata.dataloader.rest.ExportRestProxy;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportExcelTemplate;
import com.facishare.paas.metadata.dataloader.task.ExportTask;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


/**
 * 批量excel打印服务
 *
 * <AUTHOR>
 * @date 2021/08/08
 */
@Slf4j
@Service
public class BulkExcelPrintServiceImpl implements IBulkExcelPrintService {
    @Autowired
    private ExportRestProxy exportRestProxy;

    @Override
    public IBulkExportService.VerifyResult verify(Context.ExportContext context) {
        IBulkExportService.VerifyResult result = null;
        try {
            // verify时不需要特殊处理，直接调用导入导出服务的action即可
            ExportExcelTemplate.Arg arg = ExportExcelTemplate.Arg.builder()
                    .describeApiName(context.getObjectDescribeApiName())
                    .dataIdList(context.getDataIdList())
                    .searchQueryInfo(context.getSearchQueryInfo())
                    .searchTemplateId(context.getSearchTemplateId())
                    .isIgnoreSceneFilter(context.getIsIgnoreSceneFilter())
                    .isIgnoreSceneRecordType(context.getIsIgnoreSceneRecordType())
                    .printTemplateId(context.getPrintTemplateId())
                    .build();
            Map<String, String> header = Config.makeHeader(context);
            ExportExcelTemplate.Result restResult = exportRestProxy.exportExcelTemplateVerify(arg, header,
                    context.getObjectDescribeApiName());
            // 将错误码统一为0或者-1，方便前端处理
            int code = restResult.getCode() == Common.SUCCESS ? Common.SUCCESS : Common.FAIL;
            result = IBulkExportService.VerifyResult.builder().code(code).message(restResult.getMessage()).build();
        } catch (Exception e) {
            log.error("Error in export excel template verify, tenantId:{}, ea:{},userId:{}, apiName:{}",
                    context.getUser().getTenantId(),
                    context.getUser().getEa(),
                    context.getUser().getUserId(),
                    context.getObjectDescribeApiName(),
                    e);
            return IBulkExportService.VerifyResult.builder().code(Common.FAIL).message("System Error!").build();
        }
        return result;
    }
    @Override
    public IBulkExportService.ExportResult export(Context.ExportContext context) {
        // 构造导出数据请求参数
        ExportExcelTemplate.Arg arg = ExportExcelTemplate.Arg.builder()
                .describeApiName(context.getObjectDescribeApiName())
                .dataIdList(context.getDataIdList())
                .searchQueryInfo(context.getSearchQueryInfo())
                .searchTemplateId(context.getSearchTemplateId())
                .isIgnoreSceneFilter(context.getIsIgnoreSceneFilter())
                .isIgnoreSceneRecordType(context.getIsIgnoreSceneRecordType())
                .printTemplateId(context.getPrintTemplateId())
                .jobId(context.getJobId())
                .build();
        Map<String, String> header = Config.makeHeader(context);
        // 第一次调用导出Action，获取token和导出数据的总数量
        ExportExcelTemplate.Result result;
        try {
            result = exportRestProxy.exportExcelTemplateData(arg, header, context.getObjectDescribeApiName());
        } catch (Exception e) {
            log.error("Error in export excel template data, tenantId:{}, ea:{},userId:{}, apiName:{}",
                    context.getUser().getTenantId(),
                    context.getUser().getEa(),
                    context.getUser().getUserId(),
                    context.getObjectDescribeApiName(),
                    e);
            return IBulkExportService.ExportResult.builder().code(Common.FAIL).message("System Error!").build();
        }
        if (result.getCode() != Common.SUCCESS) {
            log.warn("First invoke export excel template action failed. arg:{}, result:{}", arg, result);
            return IBulkExportService.ExportResult.builder().code(Common.FAIL).message(result.getMessage()).build();
        }
        if (Strings.isNullOrEmpty(result.getData().getToken())) {
            // 未获取到token，失败
            log.warn("First invoke export excel template action failed. No token, arg:{}, result:{}", arg, result);
            return IBulkExportService.ExportResult.builder().code(Common.FAIL).message(result.getMessage()).build();
        }
        // 创建导出任务 ExportTask 实例。
        // 在新线程中定时通过Rest接口调用action。
        // 导出不需要ping任务中心，导出完成后调用complete接口即可
        // 第一次调用导出才会返回totalCount
        arg.setToken(result.getData().getToken());  // 第一次调用完成后，将返回的token设置入arg中进行之后的调用
        String apiName = context.getObjectDescribeApiName();
        ExportTask.createTask(context.getJobId(), result.getData().getToken(), result.getData().getTotalCount(),
                () -> exportRestProxy.exportExcelTemplateData(arg, header, apiName));
        return IBulkExportService.ExportResult.builder().code(Common.SUCCESS).message("ok").build();
    }
}
