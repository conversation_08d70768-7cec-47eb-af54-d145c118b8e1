package com.facishare.paas.metadata.dataloader.rest.dto;

import lombok.Builder;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/7/8
 */
public interface ExecuteExportTaskHookFunction {

    @Data
    @Builder
    class Arg {
        private String describeApiName;
        private String type;
        private String importType;
        private String filePath;
        private String originalFilename;
        private String fileType;
        private Integer expiredDay;
    }

    @Data
    class Result extends BaseDTO.CrmResult {
        private ExecuteResult data;
    }

    @Data
    class ExecuteResult {
        private String filePath;
    }
}
