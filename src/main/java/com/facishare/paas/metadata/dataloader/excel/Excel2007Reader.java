package com.facishare.paas.metadata.dataloader.excel;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.metadata.dataloader.exception.ExcelReadException;
import com.facishare.paas.metadata.dataloader.exception.StopParseExcelException;
import com.facishare.paas.metadata.dataloader.task.ExcelRowReader;
import com.facishare.paas.metadata.dataloader.util.IRowReader;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.openxml4j.exceptions.OpenXML4JException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.usermodel.BuiltinFormats;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.StylesTable;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.xml.sax.Attributes;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.XMLReader;
import org.xml.sax.helpers.DefaultHandler;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by zhenglei on 2016/12/29.
 */
@Slf4j
public class Excel2007Reader extends DefaultHandler implements IExcelReader {

    enum XSSFDataTypeEnum {
        BOOL, ERROR, FORMULA, INLINESTR, SSTINDEX, NUMBER,
    }

    final static SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    IRowReader rowReader;

    public void setRowReader(IRowReader rowReader) {
        this.rowReader = rowReader;
    }

    int rowCount = 1;
    /**
     * Table with styles
     */
    StylesTable stylesTable;

    /**
     * Table with unique strings
     */
    //private ReadOnlySharedStringsTable sharedStringsTable;
    SharedStringTableReader sharedStringsTable;
    // Set when V start element is seen
    private boolean vIsOpen;

    // Set when cell start element is seen;
    // used when cell close element is seen.
    XSSFDataTypeEnum nextDataType = XSSFDataTypeEnum.NUMBER;

    // Used to format numeric cell values.
    short formatIndex;
    String formatString;
    final DataFormatter formatter = new DataFormatter();

    int thisColumn = -1;
    // The last column printed to the output stream
    int lastColumnNumber = -1;

    // Gathers characters as they are seen.
    StringBuilder value = new StringBuilder();
    List<String> rowList = new ArrayList<>();
    List<String> hiddenSheets = new ArrayList<>();

    //用于判断
    boolean isCellNoValue = true;
    boolean isEnterCell = false;

    boolean isFirstRowEnd = false;
    List<int[]> headerColList = null;
    int[] prevColName = null;
    int[] currentColName = null;

    private int totalRowCount;
    static final String DATE_FORMAT = "yyyy-mm-dd";

    int sheetIndex = -1;

    //新版导入字段映射
    boolean skipHiddenSheet;

    @Override
    public void startElement(String uri, String localName, String name, Attributes attributes) throws SAXException {
        //处理workbook中每个sheet的属性相关
        if ("sheet".equals(name)) {
            String state = attributes.getValue("state");
            if (StringUtils.isNotBlank(state) && ("hidden".equals(state) || "veryHidden".equals(state))) {
                hiddenSheets.add(attributes.getValue("name"));
            }
        }
        //处理sheet内容相关
        if (rowReader instanceof ExcelRowReader) {
            if ("sheetData".equals(name)) {
                //读取sheet数据前，先清理一次数据
                if (headerColList != null) {
                    headerColList.clear();
                    isFirstRowEnd = false;
                }
            }
        }

        if ("row".equals(name)) {
            String r = attributes.getValue("r");
            rowCount = Integer.parseInt(r);
            if (!isFirstRowEnd) {
                headerColList = Lists.newArrayList();
            }
        }
        // <dimension ref="A1"/> 代表 sheet 的起始标签
        if ("dimension".equals(name)) {
            sheetIndex++;
            //获得总行数
            String d = attributes.getValue("ref");
            totalRowCount = getNumber(d.substring(d.indexOf(":") + 1));
            rowReader.setTotalLineCount(totalRowCount - 1); //去掉标题行
        }

        if ("t".equals(name) || "v".equals(name)) {
            vIsOpen = true;
            isCellNoValue = false;
            // Clear contents cache
            value.setLength(0);
        }
        // c => cell 代表一个单元格的开始
        else if ("c".equals(name)) {
            isEnterCell = true;

            // Get the cell reference   <c r="A1" t="inlineStr">
            String r = attributes.getValue("r");
            int firstDigit = -1;
            for (int c = 0; c < r.length(); ++c) {
                if (Character.isDigit(r.charAt(c))) {
                    firstDigit = c;
                    break;
                }
            }
            // Get the column number
            thisColumn = nameToColumn(r.substring(0, firstDigit));
            //处理丢失cell的情况
            handleMissingCellInStartElement(r);

            // Set up defaults.
            this.nextDataType = XSSFDataTypeEnum.NUMBER;
            this.formatIndex = -1;
            this.formatString = null;
            String cellType = attributes.getValue("t");
            String cellStyleStr = attributes.getValue("s");
            if ("b".equals(cellType))
                nextDataType = XSSFDataTypeEnum.BOOL;
            else if ("e".equals(cellType))
                nextDataType = XSSFDataTypeEnum.ERROR;
            else if ("inlineStr".equals(cellType))
                nextDataType = XSSFDataTypeEnum.INLINESTR;
            else if ("s".equals(cellType))
                nextDataType = XSSFDataTypeEnum.SSTINDEX;
            else if ("str".equals(cellType))
                nextDataType = XSSFDataTypeEnum.FORMULA;
            else if (cellStyleStr != null) {
                // It's a number, but almost certainly one
                // with a special style or format
                int styleIndex = Integer.parseInt(cellStyleStr);
                XSSFCellStyle style = stylesTable.getStyleAt(styleIndex);
                this.formatIndex = style.getDataFormat();
                this.formatString = style.getDataFormatString();
                if (this.formatString == null)
                    this.formatString = BuiltinFormats.getBuiltinFormat(this.formatIndex);
            }
        }
    }

    private static int getNumber(String column) {
        String c = column.toUpperCase().replaceAll("[A-Z]", "");
        return Integer.parseInt(c);
    }

    private void handleMissingCellInStartElement(String r) {
        //header的列名称保存
        int[] alphabet = getAlphabetOfCellCol(r);
        if (!isFirstRowEnd) {
            headerColList.add(alphabet);
        } else {
            currentColName = alphabet;

            if (null != prevColName) {
                // 比较当前列和上一列，如果有缺失的cell，则在行数据中插入""
                int delta = getDeltaBetweenTwoCell(prevColName, currentColName);
                fillMissingCellValue(delta);
            } else {
                //如果有，补全前面缺失的cell
                if (CollectionUtils.isEmpty(headerColList)) {
                    return;
                }

                int[] header = headerColList.get(0);
                int delta = getDeltaBetweenTwoCell(header, currentColName);
                fillMissingCellValueLeft(delta);

            }
        }
    }

    private void fillMissingCellValueLeft(int delta) {
        for (int index = 1; index <= delta; index++) {
            rowList.add("");
        }
    }

    private int getDeltaBetweenTwoCell(int[] from, int[] to) {
        int highDelta = to[0] - from[0];
        if (from[0] == 0 && to[0] > 0) {
            highDelta -= 64;
        }
        int lowDelta = to[1] - from[1];
        return highDelta * 26 + lowDelta;
    }

    private void fillMissingCellValue(int delta) {
        if (1 == delta) {
            //没问题
            return;
        }

        for (int index = 1; index <= delta - 1; index++) {
            rowList.add("");
        }
    }

    private int[] getAlphabetOfCellCol(String col) {
        int[] arr = new int[2];
        int first = col.charAt(0);
        int second = col.charAt(1);
        if (second >= 65 && second <= 90) {
            arr[0] = first;
            arr[1] = second;
        } else {
            arr[1] = first;
        }
        return arr;
    }

    @Override
    public void endElement(String uri, String localName, String name) throws SAXException {
        String thisStr = null;
        if ("row".equals(name) && !isFirstRowEnd) {
            isFirstRowEnd = true;
        }

        // v => contents of a cell
        if ("t".equals(name) || "v".equals(name)) {
            // Process the value contents as required.
            // Do now, as characters() may be called more than once
            switch (nextDataType) {
                case BOOL:
                    char first = value.charAt(0);
                    thisStr = first == '0' ? "FALSE" : "TRUE";
                    break;
                case ERROR:
                    thisStr = "ERROR:" + value.toString();
                    break;
                case FORMULA:
                    thisStr = value.toString();
                    break;
                case INLINESTR:
                    XSSFRichTextString rtsi = new XSSFRichTextString(value.toString());
                    thisStr = rtsi.toString();
                    break;
                case SSTINDEX:
                    String sstIndex = value.toString();
                    try {
                        int idx = Integer.parseInt(sstIndex);
                        XSSFRichTextString rtss = new XSSFRichTextString(sharedStringsTable.getEntryAt(idx));
                        thisStr = rtss.toString();
                    } catch (NumberFormatException ex) {
                        log.error("Failed to parse SST index:{}", sstIndex, ex);
                    }
                    break;
                case NUMBER:
                    String n = value.toString();
                    try {
                        if (DateUtil.isADateFormat(formatIndex, formatString)) {
                            Date date = HSSFDateUtil.getJavaDate(Double.parseDouble(n));
                            thisStr = SIMPLE_DATE_FORMAT.format(date);
                        } else {
                            //按默认格式，否则会有如下问题：保存的数字是100.1200000000001,实际显示是100.12，由于在sheet.xml中对应cell中缺少格式导致
                            //因此对于没有格式的数字，按默认格式处理
                            this.formatString = "General";
                            this.formatIndex = 0;
                            thisStr = formatter.formatRawCellContents(Double.parseDouble(n), this.formatIndex, this.formatString);
                            //thisStr = n;
                        }
                    } catch (Exception e) {
                        log.warn("Analyze date format error. ", e);
                        thisStr = n;
                    }
                    break;
                default:
                    log.warn("(Unexpected type:{})", nextDataType);
                    thisStr = "";
                    break;
            }

            if (lastColumnNumber == -1) {
                lastColumnNumber = 0;
            }
            rowList.add(thisStr);
            // Update column
            if (thisColumn > -1) {
                lastColumnNumber = thisColumn;
            }
        } else if ("row".equals(name)) {
            // We're onto a new row
            if (!CollectionUtils.isEmpty(headerColList)) {
                int headerSize = headerColList.size();
                int rowSize = rowList.size();
                if (headerSize > rowSize) {
                    for (int i = 0; i < headerSize - rowSize; i++) {
                        rowList.add("");
                    }
                }
            }

            boolean isContinue = rowReader.getRows(0, rowCount, rowList);
            if (!isContinue) {
                log.info("Excel2007Reader endIndex:{},rowList:{}", rowCount, JSON.toJSONString(rowList));
                throw new StopParseExcelException("Terminate reading excel");
            }
            rowList.clear();
//            rowCount++;
            lastColumnNumber = -1;

            //处理丢失的cell
            if (isFirstRowEnd) {
                prevColName = null;
                currentColName = null;
            }

        } else if ("c".equals(name)) {
            //离开cell时
            if (isEnterCell && isCellNoValue) {
                rowList.add("");
            }
            isEnterCell = false;
            isCellNoValue = true;

            //处理丢失的cell
            if (isFirstRowEnd) {
                prevColName = currentColName;
            }
        } else if ("sheetData".equals(name)) {
            //读取完sheet数据，清理
            if (headerColList != null) {
                headerColList.clear();
            }
        }
    }

    /**
     * Captures characters only if a suitable element is open. Originally
     * was just "v"; extended for inlineStr also.
     */
    @Override
    public void characters(char[] ch, int start, int length) throws SAXException {
        if (vIsOpen) {
            value.append(ch, start, length);
        }
    }

    /**
     * Converts an Excel column name like "C" to a zero-based index.
     *
     * @return Index corresponding to the specified name
     */
    private int nameToColumn(String name) {
        int column = -1;
        for (int i = 0; i < name.length(); ++i) {
            int c = name.charAt(i);
            column = (column + 1) * 26 + c - 'A';
        }
        return column;
    }


    // //////////////////////////////////////////////////////////////////////////////////////

    public Excel2007Reader() {
    }


    public Excel2007Reader(boolean skipHiddenSheet) {
        this.skipHiddenSheet = skipHiddenSheet;
    }

    /**
     * Parses and shows the content of one sheet using the specified styles and
     * shared-strings tables.
     */
    void processSheet(InputStream sheetInputStream) throws IOException, ParserConfigurationException, SAXException {

        InputSource sheetSource = new InputSource(sheetInputStream);
        SAXParserFactory saxFactory = SAXParserFactory.newInstance();
        saxFactory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        saxFactory.setFeature("http://xml.org/sax/features/external-general-entities", false);
        saxFactory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
        saxFactory.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
        SAXParser saxParser = saxFactory.newSAXParser();
        XMLReader sheetParser = saxParser.getXMLReader();
        sheetParser.setContentHandler(this);
        try {
            sheetParser.parse(sheetSource);
        } catch (StopParseExcelException e) {

        }
    }

    /**
     * 初始化excel文件，读取第一个sheet
     */
    public void process(String fileName) {
        InputStream stream = null;
        try {
            OPCPackage opcPackage = OPCPackage.open(fileName);
            sharedStringsTable = new SharedStringTableReader(opcPackage);
            XSSFReader xssfReader = new XSSFReader(opcPackage);
            stylesTable = xssfReader.getStylesTable();
            InputStream workbookData = xssfReader.getWorkbookData();
            processSheet(workbookData);
            XSSFReader.SheetIterator iterator = (XSSFReader.SheetIterator) xssfReader.getSheetsData();
            if (rowReader instanceof ExcelRowReader || skipHiddenSheet) {
                while (iterator.hasNext()) {
                    stream = iterator.next();
                    String sheetName = iterator.getSheetName();
                    if (hiddenSheets.contains(sheetName)) {
                        continue;
                    }
                    processSheet(stream);
                    break;
                }
            } else {
                if (!iterator.hasNext()) {
                    return;
                }
                stream = iterator.next();
                String sheetName = iterator.getSheetName();
                log.debug(sheetName + " [index=0]:");
                processSheet(stream);
            }
        } catch (IOException | SAXException | ParserConfigurationException | OpenXML4JException e) {
            log.error("Can not read excel file, fileName:{}", fileName, e);
            throw new ExcelReadException(e);
        } finally {
            try {
                if (null != stream) {
                    stream.close();
                }
            } catch (IOException e) {
                log.error("Error in closing InputStream, fileName:{} ", fileName, e);
            }
        }
    }
}
