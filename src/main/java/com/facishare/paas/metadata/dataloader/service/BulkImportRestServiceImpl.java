package com.facishare.paas.metadata.dataloader.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.metadata.dataloader.model.*;
import com.facishare.paas.metadata.dataloader.rest.Common;
import com.facishare.paas.metadata.dataloader.rest.ExportRestProxy;
import com.facishare.paas.metadata.dataloader.rest.dto.FindImportConfig;
import com.facishare.paas.metadata.dataloader.util.HttpRequestUtil;
import com.facishare.paas.metadata.dataloader.util.I18NExt;
import com.facishare.paas.metadata.dataloader.util.I18NKey;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 批量导入服务
 * <p>
 * Created by zhenglei on 2016/12/5.
 */
@Slf4j
@Service
public class BulkImportRestServiceImpl implements IBulkImportRestService {

    private static String sfaRestPath;
    private static String defObjPath;

    private static String sfaRestPathGrey;
    private static String defObjPathGrey;
    private static List<String> greyTenantList;

    private static String feedPath;
    private static String personPath;

    private static final String JSON_CONTENT_TYPE = "application/json";
    private HttpRequestUtil util = HttpRequestUtil.getInstance();
    private static RestPath restPath = RestPath.getInstance();

    @Autowired
    private ExportRestProxy exportRestProxy;

    static {
        ConfigFactory.getConfig("fs-paas-metadata-dataloader", iConfig -> {
            sfaRestPath = iConfig.get("bulkImportPath");
            sfaRestPathGrey = iConfig.get("bulkImportPath-grey");
            defObjPath = iConfig.get("defObjPath");
            defObjPathGrey = iConfig.get("defObjPath-grey");
            greyTenantList = parseArray(iConfig, "greyTenant");
            feedPath = iConfig.get("feedPath");
            personPath = iConfig.get("personPath");
            restPath.init(sfaRestPath, defObjPath, sfaRestPathGrey, defObjPathGrey, greyTenantList, feedPath, personPath);
        });
    }

    private static List<String> parseArray(IConfig iConfig, String key) {
        List<String> list = null;
        String greyTenant = iConfig.get(key);
        if (!Strings.isNullOrEmpty(greyTenant)) {
            String[] split = greyTenant.split(",");
            list = Lists.newArrayList(split);
        }
        return list;
    }


    @Override
    public BulkInsertResult bulkInsert(BulkInsertArg arg, int batchTimeOut) {
        if (validateArg(arg)) {
            log.warn("objectDataList is null or empty, bulkInsert returns");
            return BulkInsertResult.emptyResult();
        }

        Map<String, String> header = getRequestHeader(arg.getUser());
        //构造参数
        String path = restPath.getPath(arg.getApiName(),
                OperationType.valueOf(arg.getParameter().getOperationType()),
                arg.getUser().getTenantId(),
                ImportType.parseValue(arg.getImportType()),
                arg.getUnionApiNames());
        //调用服务，执行导入
        String responseBody = util.doPost(path, arg.toJSON(), header, batchTimeOut);
        BulkInsertResult result = parseResultFromResponse(responseBody);

        return Objects.isNull(result) ? getNullResult(arg) : result;
    }

    private boolean validateArg(BulkInsertArg arg) {
        if (ImportMethod.isAddAction(arg.getParameter().getImportMethod(), arg.getImportType())) {
            return Objects.isNull(arg.getMasterInfo());
        }
        return CollectionUtils.isEmpty(arg.getRows());
    }

    @Override
    public boolean hasPreProcessingFunction(User user, String apiName, int importType) {
        Map<String, String> headerMap = getRequestHeader(user);
        FindImportConfig.Arg arg = FindImportConfig.Arg.of(apiName);
        FindImportConfig.Result result = exportRestProxy.findImportConfig(arg, headerMap);
        if (result.hasBusinessError()) {
            log.error("hasPreProcessingFunction fail, user:{}, apiName:{}, errorMessage:{}", user, apiName, result.getMessage());
            return false;
        }

        return Optional.ofNullable(result.getData())
                .map(it -> it.preProcessingFuncIsEnableByImportType(ImportType.parseValue(importType)))
                .orElse(false);
    }


    public String findImportMethod(User user, String apiName) {
        Map<String, String> headerMap = getRequestHeader(user);
        FindImportConfig.Arg arg = FindImportConfig.Arg.of(apiName);
        FindImportConfig.Result result = exportRestProxy.findImportConfig(arg, headerMap);
        if (Objects.isNull(result) || result.hasBusinessError() || Objects.isNull(result.getInsertImport())) {
            log.error("findImportMethod failed,user:{},apiName:{},errorMessage:{}", user, apiName, result.getMessage());
            return ImportMethod.NORMAL.getMethod();
        }
        return result.getInsertImport().getImportMethod();
    }


    private BulkInsertResult getNullResult(BulkInsertArg arg) {
        return BulkInsertResult.builder()
                .success(true)
                .message("")
                .errorCode(521)
                .value(BulkInsertResult.Value.builder()
                        .importSucceedCount(0)
                        .rowErrorList(BulkInsertResult.Value.initUnexpectedErrorList(arg))
                        .build())
                .build();
    }

    public static Map<String, String> getRequestHeader(User user) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("content-type", JSON_CONTENT_TYPE);
        headerMap.put("x-fs-ei", user.getTenantId());
        headerMap.put("x-fs-userInfo", user.getUserId());
        headerMap.put("X-fs-Enterprise-Id", user.getTenantId());
        headerMap.put("X-fs-Employee-Id", user.getUserId());
        headerMap.put("X-fs-Locale", user.getLocale());
        headerMap.put("x-out-tenant-id", user.getOutTenantId());
        headerMap.put("x-out-user-id", user.getOutUserId());
        headerMap.put("x-app-id", user.getOutAppId());
        headerMap.put(Common.HTTP_HEADER_TIME_ZONE, user.getTimezone());
        headerMap.put(Common.HTTP_HEADER_UPSTREAM_OWNER_ID, user.getUpstreamOwnerId());
        headerMap.put(Common.HTTP_HEADER_TRACE_ID, TraceContext.get().getTraceId());
        headerMap.put(Common.OUT_IDENTITY_TYPE, user.getIdentityType());
        return headerMap;
    }

    private String getUserInfoFromContext(Map context) {
        if (null != context && context.get("x-fs-userInfo") != null) {
            return context.get("x-fs-userInfo").toString();
        }
        return null;
    }

    private BulkInsertResult parseResultFromResponse(String jsonResponse) {
        if (Strings.isNullOrEmpty(jsonResponse)) {
            return BulkInsertResult.builder()
                    .success(false)
                    .message(I18NExt.getOrDefault(I18NKey.SYSTEM_ERROR, "服务器繁忙"))//ignoreI18n
                    .build();
        }
        try {
            BulkInsertResultExt resultExt = JSON.parseObject(jsonResponse, BulkInsertResultExt.class);
            return resultExt.getResult();
        } catch (Exception ex) {
            log.error("Parse json from body failed, response string is:{}. ", jsonResponse, ex);
        }

        return BulkInsertResult.builder()
                .success(false)
                .message(jsonResponse)
                .build();
    }


}
