package com.facishare.paas.metadata.dataloader.model;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class DataItem implements Map {
    @Delegate
    private Map data;

    public static final String ROW_NO = "rowNo";
    public static final String MASTER_ID = "masterId";

    private DataItem() {
        data = Maps.newHashMap();
    }

    private DataItem(Map map) {
        data = map;
    }

    public static DataItem newDataItem() {
        return new DataItem();
    }

    public static DataItem newDataItem(Map map) {
        return new DataItem(map);
    }

    public Integer getRowNo() {
        return Integer.valueOf(String.valueOf(data.get(ROW_NO)));
    }

    public void setRowNo(int rowNo) {
        data.put(ROW_NO, rowNo);
    }

    public String getMasterId() {
        return (String) data.get(MASTER_ID);
    }

    public void setMasterId(String masterId) {
        if (StringUtils.isBlank(masterId)) {
            return;
        }
        data.put(MASTER_ID, masterId);
    }

    public String toJsonString() {
        return JSON.toJSONString(data);
    }

    public static DataItem fromJsonString(String data) {
        return new DataItem(JSON.parseObject(data, Map.class));
    }

    public void removeRowNo() {
        data.remove(ROW_NO);
    }

    public void removeMasterId() {
        data.remove(MASTER_ID);
    }
}
