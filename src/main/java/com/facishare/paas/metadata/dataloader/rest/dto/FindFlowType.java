package com.facishare.paas.metadata.dataloader.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * Created by zhouwr on 2022/12/9.
 */
public interface FindFlowType {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Arg {
        private String entityId;
    }

    @Data
    class Result extends BaseDTO.CrmResult {
        private ResultData data;

        public boolean openFreeApproval() {
            return Objects.nonNull(data) && "freeApprovalflow".equals(data.getFlowType());
        }
    }

    @Data
    class ResultData {
        private String flowType;
    }

}
