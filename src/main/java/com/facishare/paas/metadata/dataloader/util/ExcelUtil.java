package com.facishare.paas.metadata.dataloader.util;

import com.facishare.paas.metadata.dataloader.excel.*;
import com.facishare.paas.metadata.dataloader.image.decorator.OptimizedImageAwareExcelReaderDecorator;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.task.VerifyTask;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * 操作excel工具
 * <p>
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/11/8.
 */
@Slf4j
public class ExcelUtil {
    public static final String EXCEL2007 = "xlsx";
    public static final String EXCEL2003 = "xls";

    private ExcelUtil() {

    }

    public static ExcelUtil getInstance() {
        return ExcelUtilHolder.INSTANCE.getExcelUtil();
    }

    private enum ExcelUtilHolder {
        INSTANCE;

        private ExcelUtil excelUtil;

        ExcelUtilHolder() {
            excelUtil = new ExcelUtil();
        }

        public ExcelUtil getExcelUtil() {
            return excelUtil;
        }
    }

    public static Workbook createResultWorkBook(String fileExt) {
        return ExcelUtil.EXCEL2007.equalsIgnoreCase(fileExt) ? new SXSSFWorkbook(500) : new HSSFWorkbook();
    }

    public boolean isExcelFile(String fileExt) {
        return ExcelUtil.EXCEL2007.equalsIgnoreCase(fileExt) || ExcelUtil.EXCEL2003.equalsIgnoreCase(fileExt);
    }

    public static IExcelReader createReader(String fileExt, boolean isUnionImport, boolean skipHiddenSheet) {
        if (ExcelUtil.EXCEL2007.equalsIgnoreCase(fileExt)) {
            return isUnionImport ? new Excel2007UnionReader(skipHiddenSheet) : new Excel2007Reader(skipHiddenSheet);
        } else {
            return isUnionImport ? new Excel2003UnionReader() : new Excel2003Reader();
        }
    }

    /**
     * 创建图片感知的Excel读取器
     * 如果需要图片处理，则返回装饰器包装的读取器；否则返回基础读取器
     *
     * @param fileExt 文件扩展名
     * @param isUnionImport 是否联合导入
     * @param skipHiddenSheet 是否跳过隐藏工作表
     * @param apiName API名称
     * @param user 用户信息
     * @return Excel读取器实例
     */
    public static IExcelReader createImageAwareReader(User user, String fileExt,
                                                      boolean skipHiddenSheet, List<String> apiNameList,
                                                      boolean isUnionImport, boolean isTextureImportEnabled) {
        // 1. 创建基础读取器
        IExcelReader baseReader = createReader(fileExt, isUnionImport, skipHiddenSheet);

        // 2. 检查是否需要图片处理
        if (ExcelUtil.EXCEL2007.equalsIgnoreCase(fileExt) && isTextureImportEnabled) {
            // 3. 包装为图片感知的读取器
            log.info("Image processing enabled for apiName: {}, wrapping with decorator", apiNameList);
            return new OptimizedImageAwareExcelReaderDecorator(baseReader, apiNameList, user);
        }

        log.debug("Image processing not needed for apiName: {}", apiNameList);
        return baseReader;
    }

    /**
     * 检查是否需要图片处理
     *
     * @param apiName API名称
     * @param user 用户信息
     * @return 如果需要图片处理返回true

    private static boolean needsImageProcessing(List<String> apiNameList, User user) {
        try {
            // 获取OptimizedImageProcessor Bean来检查图片处理需求
            OptimizedImageProcessor imageProcessor = SpringContextUtil.getBean(OptimizedImageProcessor.class);
            if (imageProcessor != null) {
                boolean needed = imageProcessor.needsImageProcessing(apiName, user);
                log.debug("Image processing needed for apiName {}: {}", apiName, needed);
                return needed;
            } else {
                log.warn("OptimizedImageProcessor bean not found, skip image processing");
                return false;
            }

        } catch (Exception e) {
            log.warn("Error checking image fields for apiName: {}, skip image processing", apiName, e);
            return false;
        }
    }*/

    public static IExcelReader createReverseReader(String fileExt, boolean skipHiddenSheet) {
        return ExcelUtil.EXCEL2007.equalsIgnoreCase(fileExt) ? new Excel2003Reader() : new Excel2007Reader(skipHiddenSheet);
    }

    public static CellStyle getFailLineStyle(Workbook workbookResult) {
        CellStyle cellStyle = workbookResult.createCellStyle();
        //底色为红色
        if (workbookResult instanceof XSSFWorkbook) {
            cellStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
        } else {
            cellStyle.setFillForegroundColor(HSSFColor.RED.index);
        }
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return cellStyle;
    }

    public static CellStyle getNotImportLineStyle(Workbook workbookResult) {
        CellStyle cellStyle = workbookResult.createCellStyle();
        //底色为蓝色
        if (workbookResult instanceof XSSFWorkbook) {
            cellStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        } else {
            cellStyle.setFillForegroundColor(HSSFColor.LIGHT_BLUE.index);
        }
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return cellStyle;
    }

    public static CellStyle getSuccessLineStyle(Workbook workbookResult) {
        CellStyle cellStyle = workbookResult.createCellStyle();
        if (workbookResult instanceof XSSFWorkbook) {
            cellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        } else {
            cellStyle.setFillForegroundColor(HSSFColor.WHITE.index);
        }
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return cellStyle;
    }
}
