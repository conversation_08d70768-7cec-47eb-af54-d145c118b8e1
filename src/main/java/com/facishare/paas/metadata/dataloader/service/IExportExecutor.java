package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportCommonDTO;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportCommonVerify;

public interface IExportExecutor {

    ExportCommonVerify.Result exportVerify(Context.ExportCommonContext context);

    ExportCommonDTO.Result exportData(Context.ExportCommonContext context);
}
