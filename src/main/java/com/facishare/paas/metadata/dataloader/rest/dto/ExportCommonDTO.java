package com.facishare.paas.metadata.dataloader.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Delegate;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public interface ExportCommonDTO {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        private String jobId;
        private String searchQuery;
        private String exportBizType;
        private int pageSize;
        private Map<String, Object> callBackData;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Result extends BaseDTO.CrmResult implements ExportResult {
        @Delegate
        private ExportCommon data;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ExportHeaderResult extends BaseDTO.CrmResult {
        private ExportHeader data;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ExportDataResult extends BaseDTO.CrmResult {
        private ExportData data;
    }

    @Data
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    class ExportCommon {
        private String jobId;
        private String token;
        private int totalCount;
        private String ext;
        private String path;
        private int currentCount;
        private String exportType;
        private String fileName;
        private long size;
        private Long fileExpiredTime;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ExportData {
        private String jobId;
        private String token;
        private int totalCount;
        private List<Map<String, Object>> dataList;
        private List<GroupData> groupDataList;
        private Map<String, Object> callBackData;
        private Boolean end;
    }

    @Data
    class GroupData {
        String index;
        List<Map<String, Object>> dataList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class ExportHeader {
        private String jobId;
        private String token;
        private int totalCount;
        private List<Header> exportHeaders;
        private List<GroupHeader> groupHeaders;
    }

    @Data
    class GroupHeader {
        private Group group;
        private List<Header> headers;
    }

    @Data
    class Header {
        //模板表头对应的唯一标识
        private String key;
        //模板表头的名称
        private String name;
        //模板表头列的类型
        private String type;
    }

    @Data
    class Group {
        //分组,一样的标识会放到一个sheet
        private String index;
        //分组名字,标识的名称
        private String name;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof Group)) return false;
            Group group = (Group) o;
            return Objects.equals(getIndex(), group.getIndex()) && Objects.equals(getName(), group.getName());
        }

        @Override
        public int hashCode() {
            return Objects.hash(getIndex(), getName());
        }
    }
}
