package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.model.SheetContent;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.task.ExcelParseTask;
import com.facishare.paas.metadata.dataloader.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

@Slf4j
@Component
public class ExcelParseServiceImpl implements ExcelParseService {

    @Autowired
    private FileService fileService;

    @Override
    public List<SheetContent> parse(User user, String filePath, boolean multiSheets) {
        ExcelParseTask excelParseTask = ExcelParseTask.builder()
                .excelFilePath(filePath)
                .multiSheets(multiSheets)
                .fileExt(ExcelUtil.EXCEL2007)
                .fileService(fileService)
                .user(user)
                .build()
                .init();
        try {
            return excelParseTask.parse();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
