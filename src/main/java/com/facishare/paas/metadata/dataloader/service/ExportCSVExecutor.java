package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.exception.ExportBusinessException;
import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportCommonDTO;
import com.facishare.paas.metadata.dataloader.util.FileUtil;
import com.facishare.paas.token.model.TokenInfo;
import com.fasterxml.jackson.databind.SequenceWriter;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class ExportCSVExecutor extends AbstractExportExecutor {

    public ExportCSVExecutor(Context.ExportCommonContext context, Arg arg) {
        super(context, arg);
    }

    protected void doExport() throws IOException {
        writeToCsv();
    }

    @Override
    protected FileUtil.LocalFile uploadFile() throws IOException {
        FileUtil.LocalFile result = FileUtil.getInstance().uploadFile(context.getUser(), fileName, arg.getFileExt(), fileService);
        if (Objects.isNull(result) || Strings.isNullOrEmpty(result.getPath())) {
            log.error("upload file failed,jobId:{},tenantId:{}", context.getJobId(), context.getTenantId());
            throw new ExportBusinessException("upload csv failed");
        }
        return result;
    }

    private void writeToCsv() throws IOException {
        log.info("writeToCsv begin,jodId:{},tenantId:{}", context.getJobId(), context.getTenantId());
        File file = new File(fileName);
        OutputStream outputStream = Files.newOutputStream(file.toPath());
        log.info("writeToCsv fileFormat:{}", context.getFileFormat());
        OutputStreamWriter outputStreamWriter = new OutputStreamWriter(outputStream, context.getFileFormat());
        CsvSchema csvSchema = generateCsvSchema(exportHeaders);
        CsvMapper csvMapper = CsvMapper.builder().build();
        SequenceWriter writer = null;
        try {
            writer = csvMapper.writer(csvSchema).writeValues(outputStreamWriter);
            Map<String, String> headerMap = exportHeaders.stream()
                    .collect(Collectors.toMap(ExportCommonDTO.Header::getKey, ExportCommonDTO.Header::getName));
            writer.write(headerMap);
            boolean running = true;
            //多查一次数据，但有可能数据据结果为空，需要判空
            int loopNumber = totalCount % dataBatchSize == 0 ? totalCount / dataBatchSize : totalCount / dataBatchSize + 1;
            int loopCount = 1;//计数器
            ExportCommonDTO.Arg dataArg = ExportCommonDTO.Arg.builder()
                    .jobId(context.getJobId())
                    .pageSize(dataBatchSize)
                    .searchQuery(arg.getSearchQuery())
                    .exportBizType(arg.getExportBizType())
                    .build();
            do {
                ExportCommonDTO.ExportData result = exportDataService.findExportData(dataArg, context);
                if (Objects.nonNull(result) && Objects.nonNull(result.getDataList())) {
                    List<Map<String, Object>> dataList = handleDataMatchHeader(headerMap.keySet(), result.getDataList());
                    writer.write(dataList);
                    dataArg.setCallBackData(result.getCallBackData());
                    currentCount += result.getDataList().size();
                    TokenInfo tokenInfo = TokenInfo.builder().id(token).progress(currentCount).build();
                    tokenService.update(tokenInfo, tokenExpireSeconds);
                    if (BooleanUtils.isTrue(result.getEnd())) {
                        running = false;
                    }
                } else {
                    running = false;
                }
            } while (running && loopCount++ < loopNumber);
            log.info("write csv end,jobId:{},totalLoop:{},loopCount:{}", context.getJobId(), loopNumber, loopCount);
        } catch (IOException e) {
            log.error("Error in write data to csv file", e);
        } finally {
            try {
                outputStreamWriter.close();
                if (writer != null) {
                    writer.close();
                }
            } catch (IOException e) {
                log.error("Error in closing csv when write file", e);
            }
        }
    }

    private List<Map<String, Object>> handleDataMatchHeader(Set<String> headers, List<Map<String, Object>> dataList) {
        if (CollectionUtils.isEmpty(headers) || CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        for (Map<String, Object> data : dataList) {
            for (String key : data.keySet()) {
                if (!headers.contains(key)) {
                    data.remove(key);
                }
            }
        }
        return dataList;
    }

    public CsvSchema generateCsvSchema(List<ExportCommonDTO.Header> exportHeaders) {
        if (CollectionUtils.isEmpty(exportHeaders)) {
            return CsvSchema.emptySchema();
        }
        CsvSchema.Builder csvSchema = CsvSchema.builder();
        for (ExportCommonDTO.Header exportHeader : exportHeaders) {
            csvSchema.addColumn(exportHeader.getKey());
        }
        return csvSchema.build();
    }
}
