package com.facishare.paas.metadata.dataloader.util;

import com.facishare.paas.I18N;
import com.google.common.base.Strings;

import java.text.MessageFormat;

public class I18NExt {
    public static String getOrDefault(String key, String defaultValue) {
        if (Strings.isNullOrEmpty(key)) {
            return defaultValue;
        }
        try {
            String result = I18N.text(key);
            if (Strings.isNullOrEmpty(result)) {
                return defaultValue;
            }
            return result;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static String getOrDefault(String key, String defaultValue, Object... placeHolder) {
        try {
            String result = I18N.text(key, placeHolder);
            if (Strings.isNullOrEmpty(result)) {
                return MessageFormat.format(defaultValue, placeHolder);
            }
            return result;
        } catch (Exception e) {
            return MessageFormat.format(defaultValue, placeHolder);
        }
    }
}
