package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.I18N;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.rest.ExportRestProxy;
import com.facishare.paas.metadata.dataloader.rest.dto.ObjectDescribeDocument;
import com.facishare.paas.metadata.dataloader.util.I18NKey;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * CRM元数据服务
 * 用于调用CRM服务获取对象字段描述信息
 *
 * <AUTHOR> Assistant
 * @since 2025-01-10
 */
@Service
@Slf4j
public class CrmMetadataService {


    @Autowired
    private ExportRestProxy exportRestProxy;


    /**
     * 获取对象描述信息
     *
     * @param apiName 对象API名称
     * @param user 用户信息
     * @return 对象描述信息
     */
    public IObjectDescribe getObjectDescribe(String apiName, User user) {
        try {
            log.info("Calling CRM API for apiName: {}", apiName);
            Map<String, String> headers = buildRequestHeaders(user);

            // 构建请求参数
            ObjectDescribeDocument.Arg arg = new ObjectDescribeDocument.Arg();
            arg.setDescribeApiName(apiName);

            ObjectDescribeDocument.Result response = exportRestProxy.findDescribeByApiName(arg, headers);

            // 检查响应状态
            if (Objects.nonNull(response) && !response.hasBusinessError() && response.getData() != null) {
                Map<String, Object> objectDescribe = response.getData().getObjectDescribe();
                return new ObjectDescribe(objectDescribe);
            }

            // 如果API调用失败，记录错误并降级到模拟实现
            if (response != null && response.hasBusinessError()) {
                log.error("CRM API returned business error: {} for apiName: {}", response.getMessage(), apiName);
            } else {
                log.error("CRM API returned null or invalid response for apiName: {}", apiName);
            }

            // 临时使用增强的模拟实现（模拟真实API的行为）
            log.info("Falling back to simulated API call for apiName: {}", apiName);
            return null;

        } catch (Exception e) {
            log.error("Failed to call real CRM API for apiName: {}", apiName, e);
            // 降级到模拟实现
            throw new RuntimeException();
        }
    }

    /**
     * 获取字段描述列表
     *
     * @param apiName 对象API名称
     * @param user 用户信息
     * @return 字段描述列表
     */
    public List<IFieldDescribe> getFieldDescriptions(String apiName, User user) {
        IObjectDescribe objectDescribe = getObjectDescribe(apiName, user);
        if (objectDescribe != null && objectDescribe.getFieldDescribes() != null) {
            return objectDescribe.getFieldDescribes();
        }
        return Collections.emptyList(); // 返回空列表而不是null
    }
    /**
     * 获取对象中的所有图片字段
     *
     * @param apiName 对象API名称
     * @param user 用户信息
     * @return 图片字段列表
     */
    public List<IFieldDescribe> getImageFields(String apiName, User user) {
        List<IFieldDescribe> allFields = getFieldDescriptions(apiName, user);
        if (allFields == null || allFields.isEmpty()) {
            return Collections.emptyList();
        }
        return allFields.stream()
                .filter(field -> IFieldType.IMAGE.equals(field.getType()))
                .collect(Collectors.toList());
    }

    /**
     * 构建请求头
     *
     * @param user 用户信息
     * @return 请求头
     */
    private Map<String, String> buildRequestHeaders(User user) {
        Map<String, String> headers = new HashMap<>();
        headers.put("x-fs-ei", user.getTenantId());
        headers.put("x-fs-userInfo", user.getUserId());
        return headers;
    }
}
