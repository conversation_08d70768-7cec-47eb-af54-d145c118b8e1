package com.facishare.paas.metadata.dataloader.excel;

import com.facishare.paas.metadata.dataloader.exception.ExcelReadException;
import com.facishare.paas.metadata.dataloader.exception.NoNeedParseExcelException;
import com.facishare.paas.metadata.dataloader.task.ExcelExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.eventusermodel.FormatTrackingHSSFListener;
import org.apache.poi.hssf.eventusermodel.HSSFEventFactory;
import org.apache.poi.hssf.eventusermodel.HSSFRequest;
import org.apache.poi.hssf.eventusermodel.MissingRecordAwareHSSFListener;
import org.apache.poi.hssf.eventusermodel.dummyrecord.LastCellOfRowDummyRecord;
import org.apache.poi.hssf.eventusermodel.dummyrecord.MissingCellDummyRecord;
import org.apache.poi.hssf.record.*;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2019-04-12 15:50
 */
@Slf4j
public class Excel2003UnionReader extends Excel2003Reader {
    private boolean nextSheet = false;

    @Override
    public void process(String fileName) throws IOException {
        POIFSFileSystem fs = null;
        try {
            fs = new POIFSFileSystem(new FileInputStream(fileName));
            MissingRecordAwareHSSFListener listener = new MissingRecordAwareHSSFListener(this);
            formatListener = new FormatTrackingHSSFListener(listener);
            HSSFEventFactory factory = new HSSFEventFactory();
            HSSFRequest request = new HSSFRequest();
            request.addListenerForAllRecords(formatListener);
            factory.processWorkbookEvents(request, fs);
        } catch (NoNeedParseExcelException e) {
            //正常停止解析
        } finally {
            if (fs != null) {
                try {
                    fs.close();
                } catch (IOException e) {
                    log.error("Error in closing POIFSFileSystem, fileName:{}", fileName, e);
                }
            }
        }
    }

    @Override
    public void processRecord(Record record) {
        int thisRow = -1;
        int thisColumn = -1;
        String thisStr = null;
        String value = null;
        if (sheetIndex > 1) {
            //只处理前两个sheet
            return;
        }
        if (nextSheet && record.getSid() != BOFRecord.sid) {
            return;
        }
        switch (record.getSid()) {
            case BoundSheetRecord.sid:
                boundSheetRecords.add(record);
                break;
            case BOFRecord.sid:
                BOFRecord br = (BOFRecord) record;
                if (br.getType() == BOFRecord.TYPE_WORKSHEET) {
                    sheetIndex++;
                    curRow = 0;
                    // 清空容器
                    rowList.clear();
                    nextSheet = false;
                    if (orderedBSRs == null) {
                        orderedBSRs = BoundSheetRecord
                                .orderByBofPosition(boundSheetRecords);
                    }
                    sheetName = orderedBSRs[sheetIndex].getSheetname();
                    if (sheetIndex <= 1) {
                        ((ExcelExecutor) rowReader).nextSheet(sheetName);
                    }
                }
                break;

            case SSTRecord.sid:
                sstRecord = (SSTRecord) record;
                break;

            case BlankRecord.sid:
                BlankRecord brec = (BlankRecord) record;
                thisRow = brec.getRow();
                thisColumn = brec.getColumn();
                thisStr = "";
                rowList.add(thisColumn, thisStr);
                break;
            case BoolErrRecord.sid: //单元格为布尔类型
                BoolErrRecord berec = (BoolErrRecord) record;
                thisRow = berec.getRow();
                thisColumn = berec.getColumn();
                thisStr = berec.getBooleanValue() + "";
                rowList.add(thisColumn, thisStr);
                break;

            case FormulaRecord.sid: //单元格为公式类型
                FormulaRecord frec = (FormulaRecord) record;
                thisRow = frec.getRow();
                thisColumn = frec.getColumn();
                thisStr = "";
                rowList.add(thisColumn, thisStr);
                break;
            case StringRecord.sid://单元格中公式的字符串
                break;
            case LabelRecord.sid:
                LabelRecord lrec = (LabelRecord) record;
                curRow = thisRow = lrec.getRow();
                thisColumn = lrec.getColumn();
                value = lrec.getValue().trim();
                value = value.equals("") ? " " : value;
                this.rowList.add(thisColumn, value);
                break;
            case LabelSSTRecord.sid:  //单元格为字符串类型
                LabelSSTRecord lsrec = (LabelSSTRecord) record;
                curRow = thisRow = lsrec.getRow();
                thisColumn = lsrec.getColumn();
                if (sstRecord == null) {
                    rowList.add(thisColumn, " ");
                } else {
                    value = sstRecord
                            .getString(lsrec.getSSTIndex()).toString();
                    rowList.add(thisColumn, value);
                }
                break;
            case NumberRecord.sid:  //单元格为数字类型
                NumberRecord numrec = (NumberRecord) record;
                curRow = thisRow = numrec.getRow();
                thisColumn = numrec.getColumn();

                int formatIndex = formatListener.getFormatIndex(numrec);
                if (14 == formatIndex) {
                    value = dataFormatter.formatRawCellContents(numrec.getValue(), 14, DATE_FORMAT);
                } else {
                    value = formatListener.formatNumberDateCell(numrec).trim();
                }
                value = value.equals("") ? " " : value;
                // 向容器加入列值
                rowList.add(thisColumn, value);
                break;
            default:
                break;
        }

        // 遇到新行的操作
        if (thisRow != -1 && thisRow != lastRowNumber) {
            lastColumnNumber = -1;
        }

        // 空值的操作
        if (record instanceof MissingCellDummyRecord) {
            MissingCellDummyRecord mc = (MissingCellDummyRecord) record;
            curRow = thisRow = mc.getRow();
            thisColumn = mc.getColumn();
            if (rowList.size() >= thisColumn) {
                rowList.add(thisColumn, " ");
            } else {
                int limit = thisColumn - rowList.size();
                for (int i = 0; i <= limit; i++) {
                    rowList.add(" ");
                }
            }
        }

        // 更新行和列的值
        if (thisRow > -1)
            lastRowNumber = thisRow;
        if (thisColumn > -1)
            lastColumnNumber = thisColumn;

        // 行结束时的操作
        if (record instanceof LastCellOfRowDummyRecord) {
            if (minColumns > 0) {
                // 列值重新置空
                if (lastColumnNumber == -1) {
                    lastColumnNumber = 0;
                }
            }
            lastColumnNumber = -1;
            // 每行结束时， 调用getRows() 方法
            try {
                ((ExcelExecutor) rowReader).processSheetIfSupportImportPreProcessing(() -> rowReader.getRows(sheetIndex, curRow + 1, rowList));
            } catch (IOException | ParserConfigurationException | SAXException e) {
                log.error("Can not read excel file", e);
                throw new ExcelReadException(e);
            } finally {
                // 清空容器
                rowList.clear();
            }
        }
    }

}
