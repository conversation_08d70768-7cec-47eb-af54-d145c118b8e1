package com.facishare.paas.metadata.dataloader.task;

import com.facishare.paas.I18N;
import com.facishare.paas.metadata.dataloader.excel.IExcelReader;
import com.facishare.paas.metadata.dataloader.exception.ExcelReadColumnIndexExceedException;
import com.facishare.paas.metadata.dataloader.model.*;
import com.facishare.paas.metadata.dataloader.service.IBulkImportRestService;
import com.facishare.paas.metadata.dataloader.util.ExcelUtil;
import com.facishare.paas.metadata.dataloader.util.I18NExt;
import com.facishare.paas.metadata.dataloader.util.I18NKey;
import com.facishare.paas.metadata.dataloader.util.IRowReader;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.UnsupportedFileFormatException;
import org.apache.poi.openxml4j.exceptions.OLE2NotOfficeXmlFileException;
import org.apache.poi.poifs.filesystem.NotOLE2FileException;
import org.apache.poi.poifs.filesystem.OfficeXmlFileException;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.*;
import org.springframework.util.CollectionUtils;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Builder
public class ExcelExecutor implements IRowReader {
    private IExcelReader reader;
    private int sheetIndex;
    private String fileExt;
    private RowLoadHandler<Integer, List> handler;
    private User user;
    //只包含有内容的表头，会忽略空列
    @Getter
    @Builder.Default
    private List<String> headerRow = Lists.newArrayList();
    //所有excel列
    @Getter
    private SheetContent masterHeaderSheet;
    @Getter
    @Builder.Default
    private List<SheetContent> detailHeaderSheet = Lists.newArrayList();
    @Getter
    private int totalRowCount;

    /**
     * 联合导入列表头部
     */
    @Getter
    @Builder.Default
    private List<String> masterHeaderRow = Lists.newArrayList();
    @Getter
    @Builder.Default
    private List<List<String>> detailHeaderRowList = Lists.newArrayList();

    private final String IMPORT_RESULT_LABEL = I18NExt.getOrDefault(I18NKey.IMPORT_RESULT, "导入结果");//ignoreI18n
    private final String CANCEL_STATE = I18NExt.getOrDefault(I18NKey.IMPORT_NO_IMPORT, "未导入");//ignoreI18n
    private final String SUCCESS_STATE = I18NExt.getOrDefault(I18NKey.IMPORT_SUCCESS, "成功");//ignoreI18n
    private final String RELATED_MARK_MUST = I18NExt.getOrDefault(I18NKey.RELATED_MARK, "关联标识") + I18NExt.getOrDefault(I18NKey.MUST_FILL_IN, "（必填）");//ignoreI18n
    private final String RELATED_MARK = I18NExt.getOrDefault(I18NKey.RELATED_MARK, "关联标识");//ignoreI18n

    //导入结果
    private Sheet failSheet = null;
    private Sheet newSheet = null;
    private Workbook workbookResult = null;
    private CellStyle failLineStyle;
    private CellStyle successStyle;
    private CellStyle notImportStyle;

    //联合导入:导入结果
    private Sheet masterFailSheet = null;
    private List<Sheet> detailFailSheets = null;

    @Getter
    private boolean isExcelFormatWrong;

    private int currentSheetIndex;
    /**
     * 上一个处理的对象
     */
    private String lastApiName;
    @Setter
    @Getter
    /** 当前正在处理的对象 */
    private String currentApiName;
    @Setter
    @Getter
    private String importObjectApiName;
    @Setter
    @Getter
    /** 从对象列表*/
    private List<String> unionImportApiNameList;
    @Setter
    @Getter
    /** 当前导入的sheet*/
    private String currentSheetName;

    @Getter
    @Setter
    private boolean importPreProcessing;
    @Setter
    @Getter
    private int importType;
    @Setter
    private IBulkImportRestService importRestService;
    @Setter
    private boolean supportFieldMapping;
    @Setter
    private boolean isTextureImportEnabled;
    @Setter
    @Getter
    private String importMethod;
    @Setter
    @Getter
    private String locale;

    public ExcelExecutor init() {
        log.info("tenantId:{},locale:{},IMPORT_RESULT_LABEL:{},RELATED_MARK_MUST:{},RELATED_MARK:{}",
                user.getTenantId(), locale, IMPORT_RESULT_LABEL, RELATED_MARK_MUST, RELATED_MARK);

        //创建图片感知的ExcelReader
        reader = ExcelUtil.createImageAwareReader(
                user,
                fileExt,
                supportFieldMapping,
                CollectionUtils.isEmpty(unionImportApiNameList) ? Lists.newArrayList(importObjectApiName) : unionImportApiNameList,
                isUnionImport(),
                isTextureImportEnabled
        );
        reader.setRowReader(this);
        resetCurrentSheetAndApiName();
        if (isImportPreProcessing()) {
            return this;
        }

        workbookResult = ExcelUtil.createResultWorkBook(fileExt);
        if (!isUnionImport()) {
            //非联合导入保留全部结果和失败结果sheet
            failSheet = workbookResult.createSheet(I18NExt.getOrDefault(I18NKey.IMPORT_FAIL_RESULT, "失败结果"));//ignoreI18n
            newSheet = workbookResult.createSheet(I18NExt.getOrDefault(I18NKey.IMPORT_ALL_RESULT, "全部结果"));//ignoreI18n
        }
        failLineStyle = ExcelUtil.getFailLineStyle(workbookResult);
        successStyle = ExcelUtil.getSuccessLineStyle(workbookResult);
        notImportStyle = ExcelUtil.getNotImportLineStyle(workbookResult);
        if (isUnionImport()) {
            masterRedisFailMap = Maps.newHashMap();
        }
        return this;
    }

    private void resetCurrentSheetAndApiName() {
        currentSheetIndex = 0;
        lastApiName = null;
        currentSheetName = null;
        currentApiName = null;
    }

    /**
     * 处理下一个 sheet,更新 lastApiName 和 currentApiName
     *
     * @param sheetName sheet 名称
     */
    public void nextSheet(String sheetName) {
        setCurrentSheetName(sheetName);
        if (CollectionUtils.isEmpty(unionImportApiNameList)) {
            return;
        }
        this.lastApiName = this.currentApiName;
        setCurrentApiName(unionImportApiNameList.get(currentSheetIndex));
        currentSheetIndex++;
    }

    public String getLastApiName() {
        if (Strings.isNullOrEmpty(lastApiName)) {
            return getCurrentApiName();
        }
        return lastApiName;
    }

    private boolean isUnionImport() {
        return !CollectionUtils.isEmpty(unionImportApiNameList);
    }

    private boolean isSupportFieldMapping() {
        return supportFieldMapping;
    }

    private boolean supportImportPreProcessing() {
        if (!isImportPreProcessing()) {
            return false;
        }
        return !importRestService.hasPreProcessingFunction(user, getCurrentApiName(), importType);
    }

    public List<ExcelCol> getHeaderExcelCols() {
        if (!isSupportFieldMapping()) {
            return Lists.newArrayList();
        }
        List<SheetContent> sheetContents = Lists.newArrayList();
        sheetContents.add(masterHeaderSheet);
        sheetContents.addAll(detailHeaderSheet);
        List<ExcelCol> excelCols = sheetContents.stream()
                .filter(x -> Objects.equals(String.valueOf(sheetIndex), x.getSheetIndex()))
                .map(x -> x.getExcelCols())
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        return excelCols;
    }

    /**
     * 预处理时,没有配置预处理函数
     * <p>
     * 不执行读excel逻辑,只执行 handle 方法,处理上一批没处理完的数据
     *
     * @param sheetProcess 处理逻辑
     * @throws IOException
     * @throws ParserConfigurationException
     * @throws SAXException
     */
    public void processSheetIfSupportImportPreProcessing(SheetProcess sheetProcess) throws IOException, ParserConfigurationException, SAXException {
        if (supportImportPreProcessing()) {
            log.warn("import pre processing no has function, sheetName:{}, apiName:{}", getCurrentSheetName(), getCurrentApiName());
            handler.handle(1, Collections.emptyList());
            return;
        }
        sheetProcess.process();
    }

    /**
     * 生成导入失败结果sheet
     */
    public Sheet createUnionImportFailSheet(int index, String sheetName) {
        Sheet currentSheet = workbookResult.createSheet(sheetName + I18NExt.getOrDefault(I18NKey.IMPORT_FAIL_RESULT, "失败结果"));//ignoreI18n
        if (index == 0) {
            masterFailSheet = currentSheet;
        } else {
            detailFailSheets = (detailFailSheets == null) ? Lists.newArrayList() : detailFailSheets;
            detailFailSheets.add(currentSheet);
        }
        return currentSheet;
    }

    public void execute(String fileName, RowLoadHandler<Integer, List> handler) throws IOException {
        this.handler = handler;

        try {
            try {
                reader.process(fileName);
            } catch (OfficeXmlFileException | OLE2NotOfficeXmlFileException e) {
                //参数中的文件类型无法解析，尝试使用另一种文件格式解析excel
                reader = ExcelUtil.createReverseReader(fileExt, supportFieldMapping);
                reader.setRowReader(this);
                reader.process(fileName);
            }
        } catch (NotOLE2FileException | UnsupportedFileFormatException e) {
            isExcelFormatWrong = true;
            log.warn("The format of Excel is not valid,  user:{}, filePath:{}, fileExt:{}", user, fileName, fileExt, e);
        }
    }

    public void createAllDataHeaderRow(List<String> headerRow) {
        createHeaderRow(headerRow, newSheet);
    }

    public void createFailDataHeaderRow(List<String> headerRow) {
        createHeaderRow(headerRow, failSheet);
    }

    public void createUnionFailDataHeaderRow(List<String> headerRow, int sheetIndex) {
        //先生成失败结果sheet
        Sheet currentSheet = createUnionImportFailSheet(sheetIndex, currentSheetName);
        createHeaderRow(headerRow, currentSheet);
    }

    private void createHeaderRow(List<String> headerRow, Sheet sheet) {
        if (isImportPreProcessing()) {
            return;
        }
        Row newTitleRow = sheet.createRow(0);
        newTitleRow.createCell(0).setCellValue(IMPORT_RESULT_LABEL);
        copyHeaderRowContent(headerRow, newTitleRow);
    }

    private void copyHeaderRowContent(List<String> rowList, Row toRow) {
        if (CollectionUtils.isEmpty(rowList)) {
            return;
        }
        int index = 1;
        for (String value : rowList) {
            if (IMPORT_RESULT_LABEL.equals(value)) {
                //如果导入数据中有 导入结果 这一列，则跳过
                continue;
            }
            if (index > SpreadsheetVersion.EXCEL2007.getLastColumnIndex()) {
                log.error("readRow fail! index:{}, max:{}", index, SpreadsheetVersion.EXCEL2007.getLastColumnIndex());
                throw new ExcelReadColumnIndexExceedException(I18NExt.getOrDefault(I18NKey.IMPORT_COL_EXCEED, "Excel 模板列超过了能支持的列的个数"));//ignoreI18n
            }
            toRow.createCell(index++).setCellValue(value);
        }
    }

    private void copyRowContent(DataItem data, Row toRow, int startIndex, int sheetIndex) {
        if (data == null) {
            return;
        }
        int index = startIndex;
        if (!isUnionImport()) {
            if (isSupportFieldMapping()) {
                for (ExcelCol excelCol : masterHeaderSheet.getExcelCols()) {
                    if (IMPORT_RESULT_LABEL.equals(excelCol.getColName())) {
                        continue;
                    }
                    String value = String.valueOf(data.get(excelCol.getColIndex()));
                    toRow.createCell(index++).setCellValue(value);
                }
                return;
            }
            // 非联合导入
            for (String header : headerRow) {
                if (IMPORT_RESULT_LABEL.equals(header)) {
                    continue;
                }
                String value = String.valueOf(data.get(header));
                toRow.createCell(index++).setCellValue(value);
            }
        } else {
            if (isSupportFieldMapping()) {
                List<ExcelCol> currentHeaderRow = sheetIndex == 0 ? masterHeaderSheet.getExcelCols() : detailHeaderSheet.get(sheetIndex - 1).getExcelCols();
                for (ExcelCol excelCol : currentHeaderRow) {
                    if (IMPORT_RESULT_LABEL.equals(excelCol.getColName())) {
                        continue;
                    }
                    String value = String.valueOf(data.get(excelCol.getColIndex()));
                    toRow.createCell(index++).setCellValue(value);
                }
                return;
            }
            // 联合导入
            List<String> currentHeaderRow = sheetIndex == 0 ? masterHeaderRow : detailHeaderRowList.get(sheetIndex - 1);
            for (String header : currentHeaderRow) {
                if (IMPORT_RESULT_LABEL.equals(header)) {
                    continue;
                }
                String value = String.valueOf(data.get(header));
                toRow.createCell(index++).setCellValue(value);
            }
        }
    }

    private void copyRowContent(DataItem data, Row toRow) {
        copyRowContent(data, toRow, 1, -1);
    }

    public void copyDataToUnionFailRow(List<String> rowInfo, String apiName) {
        Sheet sheet = null;
        if (Objects.nonNull(apiName)) {
            if (apiName.equals(unionImportApiNameList.get(0))) {
                sheet = masterFailSheet;
            } else if (unionImportApiNameList.contains(apiName)) {
                sheet = detailFailSheets.get(unionImportApiNameList.indexOf(apiName) - 1);
            }
        }
        if (Objects.nonNull(sheet)) {
            Row failRow = sheet.createRow(sheet.getLastRowNum() + 1);
            Cell cell = failRow.createCell(0);
            cell.setCellValue(CANCEL_STATE);
            cell.setCellStyle(notImportStyle);
            copyHeaderRowContent(rowInfo, failRow);
        }
    }

    public void copyDataToFailRow(List<String> rowInfo) {
        Row failSheetRow = failSheet.createRow(failSheet.getLastRowNum() + 1);
        Cell cell = failSheetRow.createCell(0);
        cell.setCellValue(CANCEL_STATE);
        cell.setCellStyle(notImportStyle);
        copyHeaderRowContent(rowInfo, failSheetRow);
    }

    public void copyDataToAllRow(List<String> rowInfo, int rowIndex) {
        Row newRow = newSheet.createRow(rowIndex);
        Cell cell = newRow.createCell(0);
        cell.setCellValue(CANCEL_STATE);
        cell.setCellStyle(notImportStyle);
        copyHeaderRowContent(rowInfo, newRow);
    }

    public void updateLastResultWhenCancel(List<DataItem> dataItems, String apiName) {
        if (CollectionUtils.isEmpty(dataItems)) {
            //空行，跳过
            return;
        }
        if (CollectionUtils.isEmpty(unionImportApiNameList)) {
            //非联合导入逻辑
            copyDataItemsToAllRow(dataItems);
            copyDataItemsToFailRow(dataItems);
        } else {
            //联合导入
            copyDataItemsToUnionFailRow(dataItems, apiName);
        }
    }

    public void copyDataItemsToAllRow(List<DataItem> dataItems) {
        if (CollectionUtils.isEmpty(dataItems)) {
            return;
        }
        for (DataItem dataItem : dataItems) {
            Row newRow = newSheet.createRow(dataItem.getRowNo());
            Cell cell = newRow.createCell(0);
            cell.setCellValue(CANCEL_STATE);
            cell.setCellStyle(notImportStyle);
            copyRowContent(dataItem, newRow);
        }
    }

    public void copyDataItemsToFailRow(List<DataItem> dataItems) {
        if (CollectionUtils.isEmpty(dataItems)) {
            return;
        }
        for (DataItem dataItem : dataItems) {
            Row failSheetRow = failSheet.createRow(failSheet.getLastRowNum() + 1);
            Cell cell = failSheetRow.createCell(0);
            cell.setCellValue(CANCEL_STATE);
            cell.setCellStyle(notImportStyle);
            copyRowContent(dataItem, failSheetRow);
        }
    }

    public void copyDataItemsToUnionFailRow(List<DataItem> dataItems, String apiName) {
        if (Objects.nonNull(apiName) && !CollectionUtils.isEmpty(unionImportApiNameList)) {
            Sheet sheet = null;
            if (apiName.equals(unionImportApiNameList.get(0))) {
                sheet = masterFailSheet;
            } else if (unionImportApiNameList.contains(apiName)) {
                sheet = detailFailSheets.get(unionImportApiNameList.indexOf(apiName) - 1);
            }
            int index = unionImportApiNameList.get(0).equalsIgnoreCase(apiName) ? 0 : (unionImportApiNameList.indexOf(apiName));
            if (Objects.nonNull(sheet)) {
                for (DataItem dataItem : dataItems) {
                    Row failRow = sheet.createRow(sheet.getLastRowNum() + 1);
                    Cell cell = failRow.createCell(0);
                    cell.setCellValue(CANCEL_STATE);
                    cell.setCellStyle(notImportStyle);
                    copyRowContent(dataItem, failRow, 1, index);
                }
            }
        }
    }

    //主对象存入redis失败，标识:ID
    public volatile Map<String, String> masterRedisFailMap;

    /**
     * 生成联合导入结果
     */
    public UpdateExcelResult updateUnionResult(List<DataItem> dataList, IBulkImportRestService.BulkInsertResult result, String importApiName) {
        log.info("updateUnionResult start,bulkInsert result:{}", result.isSuccess());
        String errorMsg = null;
        boolean success = result.isSuccess();
        boolean noError = false;
        Map<Integer, String> errorMap = Maps.newHashMap();
        Map<Integer, String> masterIdMap = Maps.newHashMap();
        if (!success) {
            errorMsg = result.getMessage();
        } else {
            IBulkImportRestService.BulkInsertResult.Value value = result.getValue();
            List<IBulkImportRestService.BulkInsertResult.RowError> rowErrorList;
            if (isAddAction()) {
                rowErrorList = value.getRowErrorList().stream()
                        .filter(x -> Objects.equals(x.getObjectApiName(), importApiName))
                        .collect(Collectors.toList());
            } else {
                rowErrorList = value.getRowErrorList();
            }
            noError = CollectionUtils.isEmpty(rowErrorList);

            errorMap = rowErrorList.stream()
                    .collect(Collectors.toMap(IBulkImportRestService.BulkInsertResult.RowError::getRowNo,
                            IBulkImportRestService.BulkInsertResult.RowError::getErrorMessage, (k1, k2) -> k2));

            masterIdMap = rowErrorList.stream()
                    .collect(Collectors.toMap(IBulkImportRestService.BulkInsertResult.RowError::getRowNo,
                            v -> Objects.isNull(v.getMasterId()) ? "" : v.getMasterId(), (k1, k2) -> k2));
        }

        //导入对象对应的结果sheet
        int index = unionImportApiNameList.get(0).equalsIgnoreCase(importApiName) ? 0 : (unionImportApiNameList.indexOf(importApiName));
        int failCount = 0;
        int successCount = 0;
        String message;
        Row newFailRow;
        Cell failStatusCell;
        if (index > -1) {
            if (index == 0 && success) {    // success为false，表示本批次均失败，result.getValue() == null
                // 主对象失败结果，将返回的ID存入内存masterRedisFailMap
                result.getValue().getRowErrorList().forEach(error -> {
                    if (!Strings.isNullOrEmpty(error.getMasterId())) {
                        masterRedisFailMap.put(error.getAssociateMark(), error.getMasterId());
                    }
                });
            }
            //需要写的sheet
            Sheet sheet = index == 0 ? masterFailSheet : detailFailSheets.get(index - 1);
            //遍历全部数据
            for (DataItem item : dataList) {
                int rowNo = item.getRowNo();
                //如果是空行，则跳过
                if (isEmptyLineData(item)) {
                    continue;
                }
                if (success && (noError || !errorMap.containsKey(rowNo))) {
                    successCount++;
                    //暂时不写全部结果
                } else {
                    failCount++;
                    message = success ? errorMap.get(rowNo) : errorMsg;
                    //复制出错的行到错误行对应的sheet
                    newFailRow = sheet.createRow(sheet.getLastRowNum() + 1);
                    failStatusCell = newFailRow.createCell(0);
                    failStatusCell.setCellValue(message);
                    failStatusCell.setCellStyle(failLineStyle);
                    if (index > 0) {
                        //从对象数据导入失败，将返回的主对象ID写入Excel
                        String associateObjectId = I18N.text(I18NKey.ASSOCIATE_OBJECT_ID);
                        item.put(associateObjectId, masterIdMap.getOrDefault(rowNo, ""));
                    }
                    copyRowContent(item, newFailRow, 1, index);
                }
            }
        }
        return UpdateExcelResult.builder().successCount(successCount).failCount(failCount).build();
    }

    private boolean isAddAction() {
        return ImportMethod.isAddAction(importMethod, importType);
    }

    /**
     * 生成单个对象导入结果
     */
    public UpdateExcelResult updateResultCommon(List<DataItem> dataList, IBulkImportRestService.BulkInsertResult result) {
        log.info("updateResultCommon start,bulkInsert result:{}", result.isSuccess());
        String errorMsg = null;
        boolean success = result.isSuccess();
        boolean noError = false;
        Map<Integer, String> errorMap = Maps.newHashMap();
        if (!success) {
            errorMsg = result.getMessage();
        } else {
            IBulkImportRestService.BulkInsertResult.Value value = result.getValue();
            List<IBulkImportRestService.BulkInsertResult.RowError> rowErrorList = value.getRowErrorList();
            noError = CollectionUtils.isEmpty(rowErrorList);
            errorMap = rowErrorList.stream()
                    .collect(Collectors.toMap(IBulkImportRestService.BulkInsertResult.RowError::getRowNo,
                            IBulkImportRestService.BulkInsertResult.RowError::getErrorMessage, (k1, k2) -> k2));
        }

        //复制内容
        Row newRow;
        Row newFailRow;
        Cell statusCell;
        String message;
        Cell failStatusCell;
        int successCount = 0;
        int failCount = 0;
        for (DataItem data : dataList) {
            int index = data.getRowNo();
            newRow = newSheet.createRow(index - 1);
            statusCell = newRow.createCell(0);
            //如果是空行，则跳过
            if (isEmptyLineData(data)) {
                continue;
            }

            if (success && (noError || !errorMap.containsKey(index))) {
                successCount++;
                statusCell.setCellValue(SUCCESS_STATE);
                statusCell.setCellStyle(successStyle);
            } else {
                failCount++;
                message = success ? errorMap.get(index) : errorMsg;
                // 防止message过长
                if (message.length() > 1000) {
                    log.warn("Message is too long : {} ", message);
                    message = message.substring(0, 1000) + "...";
                }
                statusCell.setCellValue(message);

                //出错的行设置底色
                statusCell.setCellStyle(failLineStyle);
                //复制出错的行到错误行对应的sheet
                newFailRow = getNewFailRow();
                failStatusCell = newFailRow.createCell(0);
                failStatusCell.setCellValue(message);
                failStatusCell.setCellStyle(failLineStyle);
                copyRowContent(data, newFailRow);
            }
            copyRowContent(data, newRow);
        }
        return UpdateExcelResult.builder().successCount(successCount).failCount(failCount).build();
    }

    public void writeToLocalFile(String path) {
        if (isImportPreProcessing()) {
            return;
        }
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(path);
            workbookResult.write(outputStream);
            outputStream.flush();
        } catch (IOException e) {
            log.error("Error in write result file to disk", e);
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                log.error("Error in closing stream when uploading excel", e);
            }
        }
    }

    public Optional<DataItem> toObjectData(List<String> row, int rowIndex, boolean skipEmptyLine) {
        if (skipEmptyLine && isEmptyLine(row)) {
            //如果是空行，则跳过
            return Optional.empty();
        }
        DataItem data = DataItem.newDataItem();
        data.setRowNo(rowIndex);
        if (isSupportFieldMapping()) {
            int index = 0;
            String markColIndex = getMarkColIndex();
            for (String value : row) {
                if (Objects.equals(String.valueOf(index), markColIndex)) {
                    data.setMasterId(value);
                }
                data.put(String.valueOf(index++), value);
            }
            return Optional.of(data);
        }
        int dataRowRange = row.size() - 1;
        if (!isUnionImport()) {
            // 非联合导入
            for (int i = 0; i < headerRow.size(); i++) {
                String label = headerRow.get(i);
                if (IMPORT_RESULT_LABEL.equals(label)) {
                    //忽略导入结果列
                    continue;
                }
                String value = dataRowRange >= i ? row.get(i) : null;
                data.put(label, value);
            }
        } else {
            // 联合导入
            List<String> currentHeaderRow = sheetIndex == 0 ? masterHeaderRow : detailHeaderRowList.get(sheetIndex - 1);
            for (int i = 0; i < currentHeaderRow.size(); i++) {
                String label = currentHeaderRow.get(i);
                if (IMPORT_RESULT_LABEL.equals(label)) {
                    //忽略导入结果列
                    continue;
                }
                String value = dataRowRange >= i ? row.get(i) : null;
                if (RELATED_MARK_MUST.equals(label) || RELATED_MARK.equals(label)) {
                    data.setMasterId(value);
                }
                data.put(label, value);
            }
        }

        return Optional.of(data);
    }

    private String getMarkColIndex() {
        String markColIndex = null;
        if (sheetIndex == 0) {
            markColIndex = masterHeaderSheet.getExcelCols().stream()
                    .filter(x -> Objects.equals(x.getColName(), RELATED_MARK_MUST) || Objects.equals(x.getColName(), RELATED_MARK))
                    .map(ExcelCol::getColIndex)
                    .findFirst()
                    .orElse(null);
        } else {
            Optional<List<ExcelCol>> detailSheet = detailHeaderSheet.stream()
                    .filter(x -> Objects.equals(Integer.parseInt(x.getSheetIndex()), sheetIndex))
                    .map(SheetContent::getExcelCols).findFirst();
            if (detailSheet.isPresent()) {
                List<ExcelCol> excelCols = detailSheet.get();
                markColIndex = excelCols.stream()
                        .filter(x -> Objects.equals(x.getColName(), RELATED_MARK_MUST) || Objects.equals(x.getColName(), RELATED_MARK))
                        .map(ExcelCol::getColIndex)
                        .findFirst()
                        .orElse(null);
            }
        }
        return markColIndex;
    }

    private Row getNewFailRow() {
        return failSheet.createRow(failSheet.getLastRowNum() + 1);
    }

    private boolean isEmptyLineData(DataItem data) {
        data.removeRowNo();
        data.removeMasterId();
        return data.isEmpty();
    }

    public boolean isEmptyLine(List<String> row) {
        if (CollectionUtils.isEmpty(row)) {
            return true;
        }

        for (String value : row) {
            if (!Strings.isNullOrEmpty(value)) {
                return false;
            }
        }

        return true;
    }

    private void initHeaderRow(int curRow, List<String> rowInfo, int sheetIndex) {
        if (curRow != 1) {
            return;
        }
        if (!isUnionImport()) {
            if (isSupportFieldMapping()) {
                List<ExcelCol> excelCols = buildExcelCols(rowInfo);
                masterHeaderSheet = SheetContent.builder().excelCols(excelCols).sheetIndex(String.valueOf(sheetIndex)).build();
            }
            headerRow = Lists.newArrayList();
            for (String value : rowInfo) {
                if (StringUtils.isEmpty(value)) {
                    continue;
                }
                headerRow.add(new String(value.toCharArray()));
            }
            //非联合导入
            if (!isImportPreProcessing()) {
                createAllDataHeaderRow(rowInfo);
                createFailDataHeaderRow(rowInfo);
            }
        } else {
            if (sheetIndex == 0) {
                if (isSupportFieldMapping()) {
                    List<ExcelCol> excelCols = buildExcelCols(rowInfo);
                    masterHeaderSheet = SheetContent.builder().excelCols(excelCols).sheetIndex(String.valueOf(sheetIndex)).build();
                }
                // 主对象
                masterHeaderRow = Lists.newArrayList();
                for (String value : rowInfo) {
                    if (StringUtils.isEmpty(value)) {
                        continue;
                    }
                    masterHeaderRow.add(new String(value.toCharArray()));
                }
            } else if (sheetIndex > 0) {
                if (isSupportFieldMapping()) {
                    List<ExcelCol> excelCols = buildExcelCols(rowInfo);
                    SheetContent detailSheet = SheetContent.builder().excelCols(excelCols).sheetIndex(String.valueOf(sheetIndex)).build();
                    detailHeaderSheet.add(detailSheet);
                }
                // 从对象
                List<String> detailHeaderRow = Lists.newArrayList();
                for (String value : rowInfo) {
                    if (StringUtils.isEmpty(value)) {
                        continue;
                    }
                    detailHeaderRow.add(new String(value.toCharArray()));
                }
                detailHeaderRowList.add(detailHeaderRow);
            }
            //联合导入
            if (!isImportPreProcessing()) {
                createUnionFailDataHeaderRow(rowInfo, sheetIndex);
            }
        }
    }

    private List<ExcelCol> buildExcelCols(List<String> rowInfo) {
        List<ExcelCol> excelCols = Lists.newArrayList();
        int index = 0;
        for (String value : rowInfo) {
            if (StringUtils.isBlank(value)) {
                value = "";
            }
            ExcelCol excelCol = ExcelCol.builder()
                    .colIndex(String.valueOf(index++))
                    .colName(new String(value.toCharArray()))
                    .build();
            excelCols.add(excelCol);
        }
        return excelCols;
    }

    public boolean checkTitleRepeat() {
        if (CollectionUtils.isEmpty(headerRow)) {
            return true;
        }

        List<String> tmpList = Lists.newArrayList();
        for (String title : headerRow) {
            if (!Strings.isNullOrEmpty(title) && tmpList.contains(title)) {
                return false;
            }
            tmpList.add(title);
        }

        return true;
    }

    @Override
    public boolean getRows(int sheetIndex, int curRow, List<String> rowInfo) {
        if (ExcelUtil.EXCEL2003.equals(this.fileExt) && rowInfo.size() > 255) {
            throw new ExcelReadColumnIndexExceedException(I18NExt.getOrDefault(I18NKey.IMPORT_FILE_EXT_ERROR_MESSAGE, "文件格式不正确，请更换为xlsx格式（Excel2007年及之后的版本）重新导入文件"));//ignoreI18n
        }
        this.sheetIndex = sheetIndex;
        initHeaderRow(curRow, rowInfo, sheetIndex);
        fillColumnIfNecessary(curRow, rowInfo, sheetIndex);
        return handler.handle(curRow, rowInfo);
    }

    private void fillColumnIfNecessary(int curRow, List<String> rowInfo, int sheetIndex) {
        if (curRow == 1) {
            return;
        }

        if (!isUnionImport()) {
            listRightPadding(rowInfo, headerRow);
        } else {
            if (sheetIndex == 0) {
                // 主对象
                listRightPadding(rowInfo, masterHeaderRow);
            } else if (sheetIndex > 0) {
                // 从对象
                listRightPadding(rowInfo, detailHeaderRowList.get(sheetIndex - 1));
            }
        }
    }

    private void listRightPadding(List<String> rowInfo, List<String> headerRow) {
        if (Objects.equals(rowInfo.size(), headerRow.size())) {
            return;
        }

        int count = headerRow.size() - rowInfo.size();
        for (int i = 0; i < count; i++) {
            rowInfo.add("");
        }
    }

    @Override
    public void setTotalLineCount(int rowCount) {
        totalRowCount += rowCount;
    }

    @FunctionalInterface
    public interface RowLoadHandler<Integer, List> {
        boolean handle(Integer rowIndex, List rowInfo);
    }

    @FunctionalInterface
    public interface SheetProcess {
        void process() throws IOException, ParserConfigurationException, SAXException;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UpdateExcelResult {
        private int successCount = 0;
        private int failCount = 0;
        private int notImportCount = 0;
    }
}
