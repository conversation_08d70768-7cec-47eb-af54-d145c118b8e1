package com.facishare.paas.metadata.dataloader.task;

import com.alibaba.fastjson.JSON;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.paas.metadata.dataloader.common.Config;
import com.facishare.paas.metadata.dataloader.common.DataLoaderConfig;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportResult;
import com.facishare.paas.metadata.dataloader.util.HttpRequestUtil;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import static java.lang.Thread.sleep;

/**
 * create by zhaoju on 2021/07/23
 */
@Slf4j
@Data
public abstract class AbstractExportTask implements Runnable {

    private final ParallelUtils.ParallelTask parallelTask;
    private ScheduledFuture<?> scheduledFuture;

    /**
     * 任务ID
     */
    protected final String jobId;
    /**
     * 导出任务redis生成的token
     */
    protected final String token;

    /**
     * 导出数据总数量
     */
    protected volatile Integer totalCount;
    /**
     * 当前导出数量
     */
    protected volatile Integer currentCount;

    /**
     * 定时调用export action
     */
    protected final ExportTask.ExportActionInvoker invoker;

    /**
     * 轮询最大次数
     */
    protected final int MAX_TRY_TIMES;
    /**
     * 每次轮询间隔,ms毫秒
     */
    protected final int INTERVAL;
    /**
     * 完成次数
     */
    protected final int MAX_FINISHED_COUNT;


    /**
     * 是否完成导出任务
     */
    private volatile boolean isComplete = false;
    private volatile String errorMessage = "system error";


    protected AbstractExportTask(String jobId, String token, Integer totalCount, ExportTask.ExportActionInvoker invoker) {
        this.jobId = jobId;
        this.token = token;
        this.totalCount = totalCount;
        this.invoker = invoker;

        this.MAX_TRY_TIMES = DataLoaderConfig.INSTANCE.getMaxTryTimesFromConfig();
        this.INTERVAL = DataLoaderConfig.INSTANCE.getIntervalFromConfig();
        this.MAX_FINISHED_COUNT = DataLoaderConfig.INSTANCE.getMaxFinishedCount();

        this.parallelTask = ParallelUtils.createParallelTask();
    }

    protected void init() {
        parallelTask.submit(this).run();
        // 定时ping任务中心
        scheduledFuture = Job.scheduledExecutorService.scheduleAtFixedRate(
                this::startHeartBeat,
                200,
                5000,
                TimeUnit.MILLISECONDS);
    }

    protected abstract void handleInvokeResult(ExportResult result);

    protected abstract ExportTask.ExportComplete buildPingArg();

    protected abstract ExportTask.ExportComplete buildExportCompleteArg(String code, String message);

    @Override
    public final void run() {
        try {
            execute();
        } finally {
            // 导出完成动作
            exportComplete();
        }
    }

    /**
     * 与任务中心保持心跳
     */
    protected final void startHeartBeat() {
        if (!Thread.interrupted()) {
            ExportTask.ExportComplete pingArg = buildPingArg();
            log.info("Heart beat ping start:[{}]", pingArg);
            String result = HttpRequestUtil.getInstance().doPost(Config.pingUrl,
                    JSON.toJSONString(pingArg), null);
            log.info("Heart beat ping Result:[{}]", result);
        }
    }

    protected final void execute() {
        ExportResult result;
        int exceptCount = 0;
        // 连续10次导出数量不变，就认为导出服务出现了异常，停止导出
        Integer[] finishedCount = new Integer[MAX_FINISHED_COUNT];
        int index = 0;
        for (int i = 0; i < MAX_TRY_TIMES; i++) {
            try {
                if (index >= finishedCount.length) {
                    // 连续10次导出数量不变，认为服务器可能挂了
                    log.error("export error, reason:{},jobId:{},currentCount:{}", "check fs-crm-import server", jobId, currentCount);
                    return;
                }

                result = invoker.invoke();
                if (result.hasBusinessError()) {
                    errorMessage = result.getMessage();
                    log.error("export error, reason:{}, jobId:{}", errorMessage, jobId);
                    return;
                }
                currentCount = result.getCurrentCount();
                if (index == 0 || Objects.equals(currentCount, finishedCount[index - 1])) {
                    // 如果本次轮询导出的数量和上次相同，就追加到数组中
                    finishedCount[index++] = currentCount;
                } else {
                    // 否则，就清空数组，旧对象可被gc
                    for (int j = 0; j < finishedCount.length; j++) {
                        finishedCount[j] = null;
                    }
                    index = 0;
                }
                updateCompleteStatus(result);
                handleInvokeResult(result);
                if (isComplete) {
                    return;
                }
                sleep(INTERVAL);
                exceptCount = 0;
            } catch (Exception e) {
                // 异常继续轮询，连续超过三次异常就结束，防止等待过长时间。
                log.error("invoke export fail. jobId:{}", jobId, e);
                if (++exceptCount > 3) {
                    log.error("invoke export exception exceed 3 times. jobId:{}", jobId);
                    return;
                }
                try {
                    sleep(INTERVAL);
                } catch (InterruptedException ex) {
                    log.error("interrupting in sleeping. jobId:{}", jobId, e);
                    return;
                }
            }
        }
        // 超过最大连接次数后，仍然没有导出完毕
        if (!isComplete) {
            log.error("Export jodId:{} retry exceed max times : {}", jobId, MAX_TRY_TIMES);
        }
        totalCount = currentCount;
    }

    private void updateCompleteStatus(ExportResult result) {
        isComplete = !Strings.isNullOrEmpty(result.getPath());
    }

    /**
     * 完成导出任务
     */
    protected final void exportComplete() {
        // 中断心跳
        stopHeartBeat();
        if (isComplete()) {
            exportCompleteSuccess();
        } else {
            exportCompleteError();
        }
    }

    /**
     * 停止心跳
     */
    protected final void stopHeartBeat() {
        if (null == scheduledFuture) {
            log.warn("scheduledFuture is null when stopping heart beats, jobId:{}", jobId);
            return;
        }
        while (!scheduledFuture.isDone()) {
            boolean success = scheduledFuture.cancel(true);
            log.info("Stop export heartbeat success: {}, jobId:{}", success, jobId);
        }
    }

    protected void exportCompleteError() {
        exportComplete(Job.COMPLETE_FAIL_CODE, errorMessage);
    }

    protected void exportCompleteSuccess() {
        exportComplete(Job.COMPLETE_SUCCESS_CODE, null);
    }

    /**
     * 完成导出任务
     */
    private final void exportComplete(String code, String message) {
        ExportTask.ExportComplete arg = buildExportCompleteArg(code, message);
        String json = JSON.toJSONString(arg);
        String result = HttpRequestUtil.getInstance().doPost(Config.completeUrl, json, null);
        log.info("Export complete, jobId:{}, isComplete:{}, errorMessage:{}, result:{}, arg:{}", jobId, isComplete,
                message, result, json);
    }

}
