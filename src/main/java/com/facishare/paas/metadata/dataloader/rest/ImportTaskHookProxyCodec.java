package com.facishare.paas.metadata.dataloader.rest;

import com.facishare.rest.core.codec.AbstractRestCodeC;
import com.facishare.rest.core.util.JacksonUtil;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/7/9
 */
public class ImportTaskHookProxyCodec extends AbstractRestCodeC {
    @Override
    public <T> byte[] encodeArg(T obj) {
        if (Objects.isNull(obj)) {
            return null;
        }

        if (obj instanceof String) {
            return ((String) obj).getBytes(UTF_8);
        }
        return JacksonUtil.toJson(obj).getBytes(UTF_8);
    }

    @Override
    public <T> T decodeResult(int i, Map<String, List<String>> map, byte[] bytes, Class<T> clazz) {
        String bodyString = new String(bytes, UTF_8);
        if (clazz == String.class) {
            return (T) bodyString;
        }

        T ret = JacksonUtil.fromJson(new String(bytes, UTF_8), clazz);
        return ret;
    }
}
