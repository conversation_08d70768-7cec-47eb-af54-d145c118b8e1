package com.facishare.paas.metadata.dataloader.task;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportResult;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2019-06-10 19:27
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class ExportTask extends AbstractExportTask {
    /**
     * 导出完成后excel路径
     */
    private volatile String excelPath = null;
    /**
     * 导出文件名
     */
    private volatile String fileName = null;
    /**
     * 文件大小
     */
    private volatile Long size;
    /**
     * 文件扩展名
     */
    private volatile String ext;
    /**
     * 导出类型
     */
    private volatile String exportType;

    private Long fileExpiredTime;

    private ExportTask(String jobId, String token, Integer totalCount, ExportActionInvoker invoker) {
        super(jobId, token, totalCount, invoker);
    }

    /**
     * 一个线程，保证只有一个ExportTask运行。
     *
     * @param token
     * @return 导出任务
     */

    public static void createTask(String jobId, String token, int totalCount, ExportActionInvoker invoker) {
        ExportTask task = new ExportTask(jobId, token, totalCount, invoker);
        task.init();
    }

    @Override
    protected void handleInvokeResult(ExportResult result) {
        excelPath = result.getPath();
        fileName = result.getFileName();
        size = result.getSize();
        ext = result.getExt();
        exportType = result.getExportType();
        fileExpiredTime = result.getFileExpiredTime();
    }

    @Override
    protected ExportComplete buildPingArg() {
        return ExportComplete.builder()
                .jobId(jobId)
                .totalRowCount((long) totalCount)
                .completeRowCount((long) currentCount).build();
    }

    @Override
    protected ExportComplete buildExportCompleteArg(String code, String message) {
        return ExportComplete.builder()
                .code(code)
                .result(excelPath)
                .fileName(fileName)
                .jobId(jobId)
                .title(fileName)
                .size(size)
                .totalRowCount((long) totalCount)
                .message(message)
                .fileExt(ext)
                .exportType(exportType)
                .fileExpiredTime(fileExpiredTime)
                .build();
    }

    @Override
    protected void exportCompleteSuccess() {
        log.warn("export complete,result:{}", JSON.toJSONString(this));
        if (!ExportType.export_file.name().equals(exportType)) {
            super.exportCompleteSuccess();
        }
    }

    enum ExportType {
        export_data,
        export_file;
    }

    @FunctionalInterface
    public interface ExportActionInvoker {
        ExportResult invoke();
    }

    @Data
    @Builder
    public static class ExportComplete {

        private String jobId;
        private String code;
        private String result;
        private String fileName;
        private String title;
        private String fileExt;
        private String exportType;
        private Long size;
        private String message; // 失败时原因
        private Long totalRowCount;
        private Long successRowCount;
        private Long completeRowCount;  // ping时用
        private Long fileExpiredTime; // 文件过期时间
    }

}
