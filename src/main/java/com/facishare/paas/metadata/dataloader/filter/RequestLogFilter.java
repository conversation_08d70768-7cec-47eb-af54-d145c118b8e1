package com.facishare.paas.metadata.dataloader.filter;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Charsets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Priority;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;
import javax.ws.rs.ext.Provider;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * create by zhaoju on 2020/10/21
 */
@Slf4j
@Priority(0)
@Provider
@Component
public class RequestLogFilter implements ContainerRequestFilter {
    @Override
    public void filter(ContainerRequestContext containerRequestContext) throws IOException {
        //打印参数
        String body = null;
        if (containerRequestContext.hasEntity()) {
            InputStream inputStream = containerRequestContext.getEntityStream();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            IOUtils.copy(inputStream, baos);
            byte[] bytes = baos.toByteArray();
            containerRequestContext.setEntityStream(new ByteArrayInputStream(bytes));
            body = new String(bytes, Charsets.UTF_8);
        }

        log.info("uri:{},method:{},queryParameters:{},headers:{},body:{}", containerRequestContext.getUriInfo().getPath(),
                containerRequestContext.getMethod(), JSON.toJSONString(containerRequestContext.getUriInfo().getQueryParameters()),
                containerRequestContext.getHeaders(), body);
    }
}
