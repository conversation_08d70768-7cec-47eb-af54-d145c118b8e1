package com.facishare.paas.metadata.dataloader.common;

import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/12/02
 */
public enum UdobjGrayConfig {
    INSTANCE,
    ;
    private final FsGrayReleaseBiz UDOBJ_GRAY = FsGrayRelease.getInstance(UdobjGrayConfigKey.UDOBJ);

    public static boolean isAllow(String rule, String euid) {
        return INSTANCE.UDOBJ_GRAY.isAllow(rule, euid);
    }

}
