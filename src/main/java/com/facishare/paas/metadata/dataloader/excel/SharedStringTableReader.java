package com.facishare.paas.metadata.dataloader.excel;

import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.xml.sax.Attributes;
import org.xml.sax.SAXException;

import java.io.IOException;

/**
 * Created by tidus on 2017/7/3.
 */
public class SharedStringTableReader extends ReadOnlySharedStringsTable {
    private boolean rPhIsOpen;

    public SharedStringTableReader(OPCPackage pkg) throws IOException, SAXException {
        super(pkg);
    }

    @Override
    public void startElement(String uri, String localName, String name, Attributes attributes) throws SAXException {
        super.startElement(uri, localName, name, attributes);
        if ("rPh".equals(name)) {
            rPhIsOpen = true;
        }
    }

    @Override
    public void endElement(String uri, String localName, String name) throws SAXException {
        super.endElement(uri, localName, name);
        if ("rPh".equals(name)) {
            rPhIsOpen = false;
        }
    }

    @Override
    public void characters(char[] ch, int start, int length) throws SAXException {
        if (!rPhIsOpen) {
            super.characters(ch, start, length);
        }
    }


}
