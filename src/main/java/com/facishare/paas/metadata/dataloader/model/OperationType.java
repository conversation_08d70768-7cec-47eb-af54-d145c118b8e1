package com.facishare.paas.metadata.dataloader.model;

import com.google.common.collect.Maps;

import java.util.Map;

public enum OperationType {
    VERIFY("verify"),
    INVOKE("invoke"),
    UNION_VERIFY("unionVerify"),
    UNION_INVOKE("unionInvoke"),
    ADD_INVOKE("addInvoke"),
    ;

    private String operation;

    public String getOperation() {
        return operation;
    }

    private static Map<Boolean, OperationType> map = Maps.newHashMap();

    OperationType(String oper) {
        operation = oper;
    }

    static {
        map.put(true, VERIFY);
        map.put(false, INVOKE);
    }

    public static OperationType parseFrom(boolean isVerify) {
        return map.get(isVerify);
    }

}
