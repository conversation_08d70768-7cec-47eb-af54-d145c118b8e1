package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.exception.ExportBusinessException;
import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportCommonDTO;
import com.facishare.paas.metadata.dataloader.util.FileUtil;
import com.facishare.paas.token.model.TokenInfo;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class ExportExcelExecutor extends AbstractExportExecutor {


    private final Map<String, Sheet> sheetMap = Maps.newLinkedHashMap();
    private final Map<String, List<String>> headerMap = Maps.newLinkedHashMap();
    private final Map<String, Integer> indexMap = Maps.newLinkedHashMap();
    private final int FIELD_LENGTH = 32767;
    private Workbook workbook;

    public ExportExcelExecutor(Context.ExportCommonContext context, Arg arg) {
        super(context, arg);
    }

    @Override
    protected void handleExportHeader() {
        super.handleExportHeader();
    }

    @Override
    protected void doExport() {
        workbook = new SXSSFWorkbook(dataBatchSize);
        initSheet(workbook);
        initHeader();
        boolean running = true;
        //多查一次数据，但有可能数据据结果为空，需要判空
        int loopNumber = totalCount % dataBatchSize == 0 ? totalCount / dataBatchSize : totalCount / dataBatchSize + 1;
        int loopCount = 1;//计数器
        ExportCommonDTO.Arg dataArg = ExportCommonDTO.Arg.builder()
                .jobId(context.getJobId())
                .pageSize(dataBatchSize)
                .searchQuery(context.getSearchQuery())
                .exportBizType(context.getExportBizType())
                .build();
        do {
            ExportCommonDTO.ExportData result = exportDataService.findExportData(dataArg, context);
            if (Objects.nonNull(result) && Objects.nonNull(result.getGroupDataList())) {
                dataArg.setCallBackData(result.getCallBackData());
                currentCount += result.getGroupDataList().stream().map(ExportCommonDTO.GroupData::getDataList).mapToLong(Collection::size).sum();
                writeToExcel(result.getGroupDataList());
                TokenInfo tokenInfo = TokenInfo.builder().id(token).progress(currentCount).build();
                tokenService.update(tokenInfo, tokenExpireSeconds);
                if (BooleanUtils.isTrue(result.getEnd())) {
                    running = false;
                }
            } else {
                running = false;
            }
        } while (running && loopCount++ < loopNumber);
        log.info("write csv end,jobId:{},loopCount:{}", context.getJobId(), loopCount);

    }

    private void initHeader() {
        for (ExportCommonDTO.GroupHeader groupHeader : groupHeaders) {
            ExportCommonDTO.Group group = groupHeader.getGroup();
            List<ExportCommonDTO.Header> headers = groupHeader.getHeaders();
            Sheet sheet = sheetMap.get(group.getIndex());
            Integer index = indexMap.get(group.getIndex());
            boolean hasTitle = false;
            Row titleRow = sheet.createRow(0);
            for (int i = 0; i < headers.size(); i++) {
                sheet.setColumnWidth(i, Math.max(sheet.getColumnWidth(i), (excelColumnWidth * 256)));
                String name = headers.get(i).getName();
                if (StringUtils.isBlank(name)) {
                    continue;
                }
                if (name.length() > FIELD_LENGTH) {
                    name = StringUtils.substring(name, 0, FIELD_LENGTH);
                }
                titleRow.createCell(i).setCellValue(name);
                hasTitle = true;
            }
            if (hasTitle) {
                indexMap.put(group.getIndex(), index + 1);
            }
        }
    }

    private void writeToExcel(List<ExportCommonDTO.GroupData> groupDataList) {
        if (CollectionUtils.isEmpty(groupDataList)) {
            return;
        }
        Map<String, List<List<Map<String, Object>>>> indexAndDataList = groupDataList.stream()
                .collect(Collectors.groupingBy(ExportCommonDTO.GroupData::getIndex,
                        Collectors.mapping(ExportCommonDTO.GroupData::getDataList, Collectors.toList())));
        indexAndDataList.forEach((groupIndex, groupDatas) -> {
            Sheet sheet = sheetMap.get(groupIndex);
            Integer rowIndex = indexMap.get(groupIndex);
            List<String> headers = headerMap.get(groupIndex);
            List<Map<String, Object>> dataList = groupDatas.stream().flatMap(Collection::stream).collect(Collectors.toList());
            int index = 1;
            for (Map<String, Object> data : dataList) {
                Row row = sheet.createRow(rowIndex + (index++));
                if (Objects.isNull(data)) {
                    continue;
                }
                for (int col = 0; col < headers.size(); col++) {
                    String headerKey = headers.get(col);
                    Object value = data.get(headerKey);
                    if (Objects.isNull(value)) {
                        continue;
                    }
                    String cellValue = String.valueOf(value);
                    if (cellValue.length() > FIELD_LENGTH) {
                        cellValue = StringUtils.substring(cellValue, 0, FIELD_LENGTH);
                    }
                    row.createCell(col).setCellValue(cellValue);
                }
            }
            indexMap.put(groupIndex, dataList.size() + rowIndex);
        });
    }

    @Override
    protected FileUtil.LocalFile uploadFile() throws IOException {
        FileUtil.LocalFile result = FileUtil.getInstance().uploadExcel(context.getUser(), fileName, arg.getFileExt(), fileService, workbook);
        if (Objects.isNull(result) || Strings.isNullOrEmpty(result.getPath())) {
            log.error("upload file failed,jobId:{},tenantId:{}", context.getJobId(), context.getTenantId());
            throw new ExportBusinessException("upload excel failed");
        }
        return result;
    }

    private void initSheet(Workbook workbook) {
        for (ExportCommonDTO.GroupHeader groupHeader : groupHeaders) {
            ExportCommonDTO.Group group = groupHeader.getGroup();
            List<ExportCommonDTO.Header> headers = groupHeader.getHeaders();
            String name = getName(group.getName());
            Sheet sheet = workbook.createSheet(name + "(" + group.getIndex() + ")");
            sheetMap.put(group.getIndex(), sheet);
            headerMap.put(group.getIndex(), headers.stream().map(ExportCommonDTO.Header::getKey).collect(Collectors.toList()));
            indexMap.put(group.getIndex(), -1);
        }
    }

    public static String getName(String name) {
        Matcher matcher = pattern.matcher(name);
        if (matcher.find()) {
            return matcher.replaceAll("");
        }
        return name;
    }

    /**
     * 表示匹配一个或多个连续的特殊字符（冒号、方括号、星号、问号、斜杠和反斜杠）。它可以匹配连续出现的这些特殊字符。
     */
    public static final String regex = "[:：\\[\\]\\*\\?？/\\\\]+";

    public static final Pattern pattern = Pattern.compile(regex);
}
