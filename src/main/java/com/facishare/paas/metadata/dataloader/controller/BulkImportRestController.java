package com.facishare.paas.metadata.dataloader.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.metadata.dataloader.filter.ContextManager;
import com.facishare.paas.metadata.dataloader.model.DetailInfo;
import com.facishare.paas.metadata.dataloader.model.MasterInfo;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.rest.dto.OldOwnerTeamMember;
import com.facishare.paas.metadata.dataloader.service.IBulkImportService;
import com.facishare.paas.metadata.dataloader.util.ExcelUtil;
import com.facishare.paas.metadata.dataloader.util.I18NExt;
import com.facishare.paas.metadata.dataloader.util.I18NKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.openxml4j.exceptions.NotOfficeXmlFileException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by zhenglei on 2016/11/22.
 */

@Component
@Path("/bulkimport")
@Slf4j
@Consumes({"application/json"})
@Produces({"application/json"})
public class BulkImportRestController {
    @Autowired
    private IBulkImportService bulkImportService;

    @POST
    @Path("/verify")
    public RestResult verify(String json) {
        log.info("Start verify import task in controller, paramJson:{}", json);
        VerifyArg arg = JSONObject.parseObject(json, VerifyArg.class);
        initContext(arg);
        if (!ExcelUtil.getInstance().isExcelFile(arg.getFileExt())) {
            return getResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_UPLOAD_EXCEL, "请上传Excel文件"));//ignoreI18n
        }
        IBulkImportService.VerifyDataArg verifyArg = IBulkImportService.VerifyDataArg.builder()
                .excelFilePath(arg.getFilePath())
                .fileExt(arg.getFileExt())
                .importObjectApiName(arg.getApiName())
                .unionImportApiNameList(arg.getUnionImportApiNameList())
                .tenantId(arg.getEi())
                .ea(arg.getEa())
                .user(arg.getUserId())
                .isEmptyValueToUpdate(Objects.equals(arg.getIsEmptyValueToUpdate(), Boolean.TRUE))
                .importType(arg.getInnerImportType())
                .isWorkFlowEnabled(arg.isWorkFlowEnabled)
                .isApprovalFlowEnabled(arg.isApprovalFlowEnabled)
                .isTextureImportEnabled(BooleanUtils.isTrue(arg.getIsTextureImportEnabled()))
                .matchingType(arg.getMatchingType())
                .isNoBatch(Objects.equals(arg.getIsNoBatch(), Boolean.TRUE))
                .isUnionDuplicateChecking(Objects.equals(arg.getIsUnionDuplicateChecking(), Boolean.TRUE))
                .locale(arg.getLocale())
                .objectCode(arg.getObjectCode())
                .relatedApiNameList(arg.getRelatedApiNameList())
                .specifiedField(arg.getSpecifiedField())
                .updateOwner(BooleanUtils.isTrue(arg.getUpdateOwner()))
                .supportFieldMapping(BooleanUtils.isTrue(arg.getSupportFieldMapping()))
                .masterInfo(arg.getMasterInfo())
                .detailInfo(arg.getDetailInfo())
                .extendAttribute(arg.getExtendAttribute())
                .build();

        IBulkImportService.VerifyResult verifyResult = null;

        try {
            verifyResult = bulkImportService.verifyData(verifyArg);
            return buildRestResult(verifyResult);
        } catch (IOException e) {
            log.error("IO Error in Verify in Controller", e);
            return getResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_NETWORK_ERROR, "网络问题，请稍后再试"));//ignoreI18n
        } catch (EncryptedDocumentException e) {
            log.warn("Excel file has password, can not read", e);
            return getResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_ENCRYPT_NO_READ, "Excel文件被加密，无法读取"));//ignoreI18n
        } catch (NotOfficeXmlFileException e) {
            log.warn("File is not an OOXML (Office Open XML) file,", e);
            return getResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_ENCRYPT_OR_NOT_EXCEL_FILE, "文件被加密，或不是 Excel 文件"));//ignoreI18n
        } catch (Exception e) {
            log.error("Error in Verify in Controller, verify result:{}", verifyResult, e);
            return getResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_SYSTEM_ERROR, "系统异常，请稍后重试或联系管理员解决"));//ignoreI18n
        }
    }

    private RestResult buildRestResult(IBulkImportService.VerifyResult verifyResult) {
        if (verifyResult == null) {
            log.warn("verifyResult is empty");
            return getResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_READ_FILE_FAILED, "读取文件失败，请检查文件或者稍后重试"));//ignoreI18n
        }
        return RestResult.from(verifyResult);
    }

    @POST
    @Path("/invoke")
    public RestResult invoke(String json) {
        log.info("Import Data Invoke in controller, paramJson:{}", json);
        InvokeArg arg = JSONObject.parseObject(json, InvokeArg.class);
        initContext(arg);
        if (!ExcelUtil.getInstance().isExcelFile(arg.getFileExt())) {
            return getResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_UPLOAD_EXCEL, "请上传Excel文件"));//ignoreI18n
        }

        IBulkImportService.ImportDataArg importArg = IBulkImportService.ImportDataArg.builder()
                .excelFilePath(arg.getFilePath())
                .fileExt(arg.getFileExt())
                .importObjectApiName(arg.getApiName())
                .unionImportApiNameList(arg.getUnionImportApiNameList())
                .tenantId(arg.getEi())
                .ea(arg.getEa())
                .user(arg.getUserId())
                .isEmptyValueToUpdate(Objects.equals(arg.getIsEmptyValueToUpdate(), Boolean.TRUE))
                .importType(arg.getInnerImportType())
                .matchingType(arg.getMatchingType())
                .jobId(arg.getJobId())
                .isNoBatch(Objects.equals(arg.getIsNoBatch(), Boolean.TRUE))
                .isWorkFlowEnabled(Objects.equals(arg.getIsWorkFlowEnabled(), Boolean.TRUE))
                .isApprovalFlowEnabled(Objects.equals(arg.getIsApprovalFlowEnabled(), Boolean.TRUE))
                .isTextureImportEnabled(BooleanUtils.isTrue(arg.getIsTextureImportEnabled()))
                .isUnionDuplicateChecking(Objects.equals(arg.getIsUnionDuplicateChecking(), Boolean.TRUE))
                .isVerifyEnterprise(BooleanUtils.isTrue(arg.getIsVerifyEnterprise()))
                .isBackFillIndustrialAndCommercialInfo(BooleanUtils.isTrue(arg.getIsBackFillIndustrialAndCommercialInfo()))
                .isBackFillOverwriteOldValue(BooleanUtils.isTrue(arg.getIsBackFillOverwriteOldValue()))
                .relatedApiNameList(arg.getRelatedApiNameList())
                .objectCode(arg.getObjectCode())
                .locale(arg.getLocale())
                .specifiedField(arg.getSpecifiedField())
                .checkOutOwner(BooleanUtils.isTrue(arg.getCheckOutOwner()))
                .removeOutTeamMember(BooleanUtils.isTrue(arg.getRemoveOutTeamMember()))
                .updateOwner(BooleanUtils.isTrue(arg.getUpdateOwner()))
                .oldOwnerTeamMember(arg.getOldOwnerTeamMember())
                .masterInfo(arg.getMasterInfo())
                .detailInfo(arg.getDetailInfo())
                .supportFieldMapping(BooleanUtils.isTrue(arg.getSupportFieldMapping()))
                .extendAttribute(arg.getExtendAttribute())
                .build();
        try {
            bulkImportService.importData(importArg, ContextManager.getContext());
        } catch (IOException e) {
            log.error("IO Error in bulkImportService.importData. jobId:{}", arg.getJobId(), e);
        } catch (Exception e) {
            log.error("Error in importData in Controller", e);
            return getResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_SYSTEM_ERROR, "系统异常，请稍后重试或联系管理员解决"));//ignoreI18n
        }

        return getResult(0, null);
    }

    private void initContext(VerifyArg arg) {
        I18N.setContext(arg.getEi(), arg.getLocale());
        User user = ContextManager.getContext().getUser();
        user.setEa(arg.getEa());
        if (StringUtils.isNotBlank(arg.getTimezone())) {
            user.setTimezone(arg.getTimezone());
        }
    }

    private RestResult getResult(int code, String message) {
        return RestResult.builder()
                .code(code)
                .message(message)
                .build();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class RestResult {
        private int code;
        private String message;

        public static RestResult from(IBulkImportService.VerifyResult verifyResult) {
            return RestResult.builder()
                    .code(verifyResult.getCode())
                    .message(verifyResult.getMessage())
                    .build();
        }
    }

    @Data
    public static class VerifyArg {
        @JSONField(name = "_filePath")
        private String filePath;

        @JSONField(name = "_fileExt")
        private String fileExt;
        @JSONField(name = "apiName")
        private String apiName;

        @JSONField(name = "_unionImportApiNameList")
        private List<String> unionImportApiNameList;

        @JSONField(name = "userId")
        private String userId;

        @JSONField(name = "jobId")
        private String jobId;

        @JSONField(name = "ea")
        private String ea;
        @JSONField(name = "ei")
        private String ei;

        @JSONField(name = "_ImportType")
        private Integer innerImportType;

        @JSONField(name = "_MatchingType")
        private Integer matchingType = 2;

        @JSONField(name = "_IsEmptyValueToUpdate")
        private Boolean isEmptyValueToUpdate;

        @JSONField(name = "_isNoBatch")
        private Boolean isNoBatch;

        @JSONField(name = "_IsUnionDuplicateChecking")
        private Boolean isUnionDuplicateChecking;

        @JSONField(name = "_X-fs-Locale")
        private String locale = "zh-CN";

        @JSONField(name = "_timezone")
        private String timezone;

        @JSONField(name = "_IsWorkFlowEnabled")
        private Boolean isWorkFlowEnabled;

        @JSONField(name = "_IsApprovalFlowEnabled")
        private Boolean isApprovalFlowEnabled = false;

        @JSONField(name = "_IsTextureImportEnabled")
        private Boolean IsTextureImportEnabled;

        @JSONField(name = "_IsVerifyEnterprise")
        private Boolean isVerifyEnterprise = false;

        @JSONField(name = "_IsBackFillIndustrialAndCommercialInfo")
        private Boolean isBackFillIndustrialAndCommercialInfo = false;

        @JSONField(name = "_IsBackFillOverwriteOldValue")
        private Boolean isBackFillOverwriteOldValue = false;

        @JSONField(name = "_relatedApiNameList")
        private List<String> relatedApiNameList;

        @JSONField(name = "_objectCode")
        private String objectCode;

        @JSONField(name = "_specifiedField")
        private String specifiedField; //指定字段

        @JSONField(name = "_IsCheckOutOwner")
        private Boolean checkOutOwner;

        @JSONField(name = "_IsRemoveOutTeamMember")
        private Boolean removeOutTeamMember;

        @JSONField(name = "_IsUpdateOwner")
        private Boolean updateOwner;

        @JSONField(name = "_oldOwnerTeamMember")
        private OldOwnerTeamMember oldOwnerTeamMember;

        @JSONField(name = "_supportFieldMapping")
        private Boolean supportFieldMapping;

        @JSONField(name = "_masterInfo")
        private MasterInfo masterInfo;

        @JSONField(name = "_detailInfo")
        private List<DetailInfo> detailInfo;

        @JSONField(name = "_extendAttribute")
        private Map<String, Object> extendAttribute;
    }

    @Data
    public static class InvokeArg extends VerifyArg {
    }
}
