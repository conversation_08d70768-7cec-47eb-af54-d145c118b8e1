package com.facishare.paas.metadata.dataloader.task;

import com.facishare.paas.metadata.dataloader.rest.dto.ExportResult;
import com.facishare.paas.metadata.dataloader.task.ExportTask.ExportComplete;
import lombok.extern.slf4j.Slf4j;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/07/23
 */
@Slf4j
public class ExportFilesTask extends AbstractExportTask {
    private ExportFilesTask(String jobId, String token, int totalCount, ExportTask.ExportActionInvoker invoker) {
        super(jobId, token, totalCount, invoker);
    }

    public static void createTaskAndInvoker(String jobId, String token, int totalCount,
                                            ExportTask.ExportActionInvoker invoker) {
        ExportFilesTask task = new ExportFilesTask(jobId, token, totalCount, invoker);
        task.init();
    }

    @Override
    protected void handleInvokeResult(ExportResult result) {

    }

    @Override
    protected ExportTask.ExportComplete buildPingArg() {
        return ExportComplete.builder().jobId(jobId)
                .totalRowCount(totalCount == null ? null : totalCount.longValue())
                .completeRowCount(currentCount == null ? null : currentCount.longValue())
                .build();
    }

    @Override
    protected ExportTask.ExportComplete buildExportCompleteArg(String code, String message) {
        return ExportTask.ExportComplete.builder()
                .code(code)
                .jobId(jobId)
                .totalRowCount(null)
                .message(message)
                .build();
    }

    @Override
    protected void exportCompleteSuccess() {

    }
}
