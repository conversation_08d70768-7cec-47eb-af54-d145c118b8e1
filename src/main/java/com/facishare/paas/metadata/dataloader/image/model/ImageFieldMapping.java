package com.facishare.paas.metadata.dataloader.image.model;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 图片字段映射信息
 * 用于处理合并单元格的图片字段，支持一个字段对应多个列
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
public class ImageFieldMapping {

    /**
     * 对象apiName
     */
    private String apiName;

    /**
     * 字段名称（来自合并单元格的主标题）
     */
    private String fieldName;
    
    /**
     * 该字段对应的所有列索引
     * 例如：["头像"] -> [1, 2, 3] 表示头像字段跨越第1、2、3列
     */
    private List<Integer> columnIndexes;
    
    /**
     * 字段描述信息（来自CRM元数据）
     */
    private IFieldDescribe fieldDescribe;
    
    /**
     * 是否为图片字段
     */
    private boolean isImageField;
    
    /**
     * 图片路径分隔符（默认为"|"）
     */
    @Builder.Default
    private String separator = "|";
    
    /**
     * 主列索引（通常是第一列，用于存放合并后的结果）
     */
    public int getPrimaryColumnIndex() {
        return columnIndexes != null && !columnIndexes.isEmpty() ? columnIndexes.get(0) : -1;
    }
    
    /**
     * 获取需要清空的列索引（除主列外的其他列）
     */
    public List<Integer> getColumnsToClean() {
        if (columnIndexes == null || columnIndexes.size() <= 1) {
            return Lists.newArrayList();
        }
        return columnIndexes.subList(1, columnIndexes.size());
    }
    
    /**
     * 检查是否为多列字段
     */
    public boolean isMultiColumn() {
        return columnIndexes != null && columnIndexes.size() > 1;
    }
    
    /**
     * 获取列数量
     */
    public int getColumnCount() {
        return columnIndexes != null ? columnIndexes.size() : 0;
    }
    
    /**
     * 检查指定列索引是否属于此字段
     */
    public boolean containsColumn(int columnIndex) {
        return columnIndexes != null && columnIndexes.contains(columnIndex);
    }
}
