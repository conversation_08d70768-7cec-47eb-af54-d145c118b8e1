package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.model.ImportTaskHook;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.rest.ExportRestProxy;
import com.facishare.paas.metadata.dataloader.rest.dto.ExecuteExportTaskHookFunction;
import com.facishare.rest.core.util.JacksonUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/7/4
 */
@Slf4j
@Service
public class ImportTaskHookService implements IImportTaskHookService {

    public static final String IMPORT_TYPE_IMPORT = "import";
    public static final String TYPE_BEFORE = "before";
    public static final String TYPE_AFTER = "after";
    private final ExportRestProxy exportRestProxy;
    private final FileService fileService;
    private List<Config> configs = Lists.newArrayList();

    public ImportTaskHookService(ExportRestProxy exportRestProxy, FileService fileService) {
        this.exportRestProxy = exportRestProxy;
        this.fileService = fileService;
        ConfigFactory.getConfig("fs-paas-metadata-dataloader", config -> {
            configs = JacksonUtil.fromJson(config.get("export_task_function_conf"), new TypeReference<List<Config>>() {
            });
        });
    }

    @Override
    public ImportTaskHook.Result before(User user, ImportTaskHook.Arg arg) {
        String functionName = getImportTaskHookFunction(user, arg.getImportObjectApiName());
        if (Strings.isNullOrEmpty(functionName)) {
            return ImportTaskHook.Result.builder()
                    .filePath(arg.getFilePath())
                    .build();
        }
        ExecuteExportTaskHookFunction.Arg exeArg = ExecuteExportTaskHookFunction.Arg.builder()
                .describeApiName(arg.getImportObjectApiName())
                .importType(IMPORT_TYPE_IMPORT)
                .type(TYPE_BEFORE)
                .filePath(arg.getFilePath())
                .fileType(arg.getFileExt())
                .originalFilename(arg.getOriginalFilename())
                .expiredDay(FileService.getExpiredDay())
                .build();
        Map<String, String> requestHeader = BulkImportRestServiceImpl.getRequestHeader(user);
        ExecuteExportTaskHookFunction.Result result = exportRestProxy.executeExportTaskHookFunction(exeArg, requestHeader);
        return Optional.ofNullable(result)
                .filter(it -> !it.hasBusinessError())
                .map(ExecuteExportTaskHookFunction.Result::getData)
                .map(ExecuteExportTaskHookFunction.ExecuteResult::getFilePath)
                .map(path -> ImportTaskHook.Result.builder()
                        .filePath(path)
                        .build())
                .orElseGet(() -> ImportTaskHook.Result.builder()
                        .filePath(arg.getFilePath())
                        .build());
    }

    @Override
    public ImportTaskHook.Result after(User user, ImportTaskHook.Arg arg) {
        String functionName = getImportTaskHookFunction(user, arg.getImportObjectApiName());
        if (Strings.isNullOrEmpty(functionName)) {
            return ImportTaskHook.Result.builder()
                    .filePath(arg.getFilePath())
                    .build();
        }
        ExecuteExportTaskHookFunction.Arg exeArg = ExecuteExportTaskHookFunction.Arg.builder()
                .describeApiName(arg.getImportObjectApiName())
                .importType(IMPORT_TYPE_IMPORT)
                .type(TYPE_AFTER)
                .filePath(arg.getFilePath())
                .fileType(arg.getFileExt())
                .originalFilename(arg.getOriginalFilename())
                .expiredDay(FileService.getExpiredDay())
                .build();
        Map<String, String> requestHeader = BulkImportRestServiceImpl.getRequestHeader(user);
        ExecuteExportTaskHookFunction.Result result = exportRestProxy.executeExportTaskHookFunction(exeArg, requestHeader);
        return Optional.ofNullable(result)
                .filter(it -> !it.hasBusinessError())
                .map(ExecuteExportTaskHookFunction.Result::getData)
                .map(ExecuteExportTaskHookFunction.ExecuteResult::getFilePath)
                .map(path -> ImportTaskHook.Result.builder()
                        .filePath(path)
                        .build())
                .orElseGet(() -> ImportTaskHook.Result.builder()
                        .filePath(arg.getFilePath())
                        .build());
    }

    private String getImportTaskHookFunction(User user, String describeApiName) {
        if (CollectionUtils.isEmpty(configs)) {
            return null;
        }
        return configs.stream()
                .map(it -> it.getFunctionName(user.getTenantId(), describeApiName, "import"))
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    private static class Config {
        private final GrayRule tenantGray;
        private final GrayRule describeApiNameGray;
        private final String functionName;
        private final String type;

        @JsonCreator
        private Config(@JsonProperty("ei") String ei,
                       @JsonProperty("describeApiName") String describeApiName,
                       @JsonProperty("functionName") String functionName,
                       @JsonProperty("type") String type) {
            this.tenantGray = new GrayRule(ei);
            this.describeApiNameGray = new GrayRule(describeApiName);
            this.functionName = functionName;
            this.type = type;
        }

        public String getFunctionName(String tenantId, String describeApiName, String type) {
            if (tenantGray.isAllow(tenantId) && describeApiNameGray.isAllow(describeApiName) && Objects.equals(this.type, type)) {
                return functionName;
            }
            return null;
        }
    }
}
