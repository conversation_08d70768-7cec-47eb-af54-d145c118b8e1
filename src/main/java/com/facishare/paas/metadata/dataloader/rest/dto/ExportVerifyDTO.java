package com.facishare.paas.metadata.dataloader.rest.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-06-10 11:50
 */
public interface ExportVerifyDTO {
    @Data
    class Result extends BaseDTO.CrmResult {
        @SerializedName("data")
        private VerifyData data;

        @Data
        class VerifyData {
            @SerializedName("success")
            private boolean success;
        }
    }


    @Data
    @Builder
    class Arg {
        @SerializedName("object_describe_api_name")
        private String apiName;
        @SerializedName("dataIdList")
        private List<String> dataIdList;
        @SerializedName("recordType_apiName")
        private String recordTypeApiName;
        @SerializedName("search_template_id")
        private String searchTemplateId;
        @SerializedName("search_query_info")
        private String searchQueryInfo;
        @SerializedName("other_name_list")
        List<String> otherNameList;
        @SerializedName("file_fields")
        private List<String> fileFields;
        @SerializedName("detail_arg")
        private DetailArg detailArg;
        @SerializedName("what_api_name")
        private String whatApiName;
    }

}
