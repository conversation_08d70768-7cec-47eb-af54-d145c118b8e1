package com.facishare.paas.metadata.dataloader.rest.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class DetailArg {
    @SerializedName(value = "detail_object_api_names")
    @JSONField(name = "detail_object_api_names", alternateNames = "_detail_object_api_names")
    private List<String> detailObjectApiNames;
    @SerializedName(value = "include_id")
    @JSONField(name = "includeId", alternateNames = "_include_id")
    private Boolean includeId;
    @SerializedName(value = "detail_info")
    @JSONField(name = "detail_info", alternateNames = "_detail_info")
    private List<DetailInfo> detailInfo;


    @Data
    public static class DetailInfo {
        @SerializedName(value = "apiName")
        @JSONField(name = "apiName", alternateNames = "_apiName")
        String apiName;

        @SerializedName(value = "file_fields")
        @JSONField(name = "file_fields", alternateNames = "_file_fields")
        List<String> fileFields;

        @SerializedName(value = "fields")
        @JSONField(name = "fields", alternateNames = "_fields")
        List<String> fields;
    }
}
