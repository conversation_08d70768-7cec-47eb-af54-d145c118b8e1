package com.facishare.paas.metadata.dataloader.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class User {
    private String tenantId;
    private String ea;
    private String userId;
    private String outTenantId;
    private String outUserId;
    private String locale;
    private String upstreamOwnerId;
    private String outAppId;
    private String identityType;
    private String timezone;
}
