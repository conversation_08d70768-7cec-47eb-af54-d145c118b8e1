package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.I18N;
import com.facishare.paas.metadata.dataloader.common.Config;
import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.filter.ContextManager;
import com.facishare.paas.metadata.dataloader.model.ImportMethod;
import com.facishare.paas.metadata.dataloader.model.ImportType;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.mongo.dao.ImportDataDao;
import com.facishare.paas.metadata.dataloader.mq.ImportEventRocketMqProducer;
import com.facishare.paas.metadata.dataloader.rest.ApprovalFlowRestProxy;
import com.facishare.paas.metadata.dataloader.rest.dto.FindFlowType;
import com.facishare.paas.metadata.dataloader.task.ImportTask;
import com.facishare.paas.metadata.dataloader.task.MDImportTask;
import com.facishare.paas.metadata.dataloader.task.VerifyTask;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * 批量导入服务实现
 * <p>
 * Created by zhenglei on 2016/11/8.
 */
@Service
@Slf4j
public class BulkImportServiceImpl implements IBulkImportService {
    @Autowired
    private IBulkImportRestService importRestService;
    @Autowired
    private FileService fileService;
    @Autowired
    private ImportEventRocketMqProducer importEventRocketMqProducer;
    @Autowired
    private ApprovalFlowRestProxy approvalFlowRestProxy;
    @Autowired
    private ImportDataDao importDataDao;
    @Autowired
    private IImportTaskHookService importTaskHookService;

    private static int batchCount;
    private static int noBatchCount;
    private static int timeOut;
    private static int noBatchTimeOut;

    static {
        ConfigFactory.getConfig("fs-paas-metadata-dataloader", iConfig -> {
            batchCount = iConfig.getInt("batchCount", 50);
            noBatchCount = iConfig.getInt("noBatchCount", 3000);

            timeOut = iConfig.getInt("bulkImportTimeOut", 30);
            noBatchTimeOut = iConfig.getInt("noBatchTimeOut", 1800);

        });
    }

    @Override
    public VerifyResult verifyData(VerifyDataArg arg) throws IOException {
        User user = ContextManager.getContext().getUser();
        VerifyTask verifyTask = VerifyTask.builder()
                .excelFilePath(arg.getExcelFilePath())
                .importType(arg.getImportType())
                .matchingType(arg.getMatchingType())
                .importRestService(importRestService)
                .importTaskHookService(importTaskHookService)
                .fileExt(arg.getFileExt())
                .fileService(fileService)
                .user(user)
                .importObjectApiName(arg.getImportObjectApiName())
                .unionImportApiNameList(arg.getUnionImportApiNameList())
                .isEmptyValueToUpdate(arg.isEmptyValueToUpdate())
                .isNoBatch(arg.isNoBatch())
                .noBatchCount(noBatchCount)
                .batchTimeOut(timeOut)
                .isWorkFlowEnabled(arg.isWorkFlowEnabled())
                .isApprovalFlowEnabled(arg.isApprovalFlowEnabled())
                .isTextureImportEnabled(arg.isTextureImportEnabled())
                .isUnionDuplicateChecking(arg.isUnionDuplicateChecking())
                .locale(arg.getLocale())
                .objectCode(arg.getObjectCode())
                .relatedApiNameList(arg.getRelatedApiNameList())
                .specifiedField(arg.getSpecifiedField())
                .updateOwner(arg.isUpdateOwner())
                .supportFieldMapping(arg.isSupportFieldMapping())
                .masterInfo(arg.getMasterInfo())
                .detailInfo(arg.getDetailInfo())
                .extendAttribute(arg.getExtendAttribute())
                .build()
                .init();

        return verifyTask.verify();
    }

    @SuppressWarnings("unchecked")
    @Async("importTaskExecutor")
    @Override
    public void importData(ImportDataArg arg, Context context) throws IOException {
        try {
            initI18Context(context.getUser());
            boolean isApprovalEnabled = arg.isApprovalFlowEnabled();
            if (isApprovalEnabled) {
                isApprovalEnabled = isApprovalFlowEnabled(context.getTenantId(), getObjectApiName(arg));
                if (!isApprovalEnabled) {
                    log.warn("isApprovalEnabled changed:true->false,tenantId:{},objectApiName:{}", context.getTenantId(), getObjectApiName(arg));
                }
            }
            String importMethod = findImportMethod(context.getUser(), getObjectApiName(arg), arg.getImportType());
            int batchHandleCount = arg.isNoBatch() ? noBatchCount : batchCount;
            if (ImportMethod.isAddAction(importMethod, arg.getImportType())) {
                batchHandleCount = 1;
            }
            if (ImportMethod.isAddAction(importMethod, arg.getImportType()) && isMasterDetailImport(arg)) {
                MDImportTask mdImportTask = MDImportTask.builder()
                        .excelFilePath(arg.getExcelFilePath())
                        .importObjectApiName(getObjectApiName(arg))
                        .unionImportApiNameList(arg.getUnionImportApiNameList())
                        .isEmptyValueToUpdate(arg.isEmptyValueToUpdate())
                        .importRestService(importRestService)
                        .importTaskHookService(importTaskHookService)
                        .fileExt(arg.getFileExt())
                        .jobId(arg.getJobId())
                        .importType(arg.getImportType())
                        .matchingType(arg.getMatchingType())
                        .fileService(fileService)
                        .user(context.getUser())
                        .importCompleteUrl(Config.completeUrl)
                        .pingUrl(Config.pingUrl)
                        .batchHandleCount(batchHandleCount)
                        .isWorkFlowEnabled(arg.isWorkFlowEnabled())
                        .isApprovalFlowEnabled(isApprovalEnabled)
                        .isTextureImportEnabled(arg.isTextureImportEnabled())
                        .batchTimeOut(arg.isNoBatch() ? noBatchTimeOut : timeOut)
                        .isUnionDuplicateChecking(arg.isUnionDuplicateChecking())
                        .relatedApiNameList(arg.getRelatedApiNameList())
                        .locale(arg.getLocale())
                        .objectCode(arg.getObjectCode())
                        .specifiedField(arg.getSpecifiedField())
                        .checkOutOwner(arg.isCheckOutOwner())
                        .removeOutTeamMember(arg.isRemoveOutTeamMember())
                        .updateOwner(arg.isUpdateOwner())
                        .oldOwnerTeamMember(arg.getOldOwnerTeamMember())
                        .masterInfo(arg.getMasterInfo())
                        .detailInfo(arg.getDetailInfo())
                        .supportFieldMapping(arg.isSupportFieldMapping())
                        .extendAttribute(arg.getExtendAttribute())
                        .importDataDao(importDataDao)
                        .importMethod(importMethod)
                        .build()
                        .init();

                mdImportTask.invoke();
                return;
            }

            ImportTask importTask = ImportTask.builder()
                    .excelFilePath(arg.getExcelFilePath())
                    .importObjectApiName(getObjectApiName(arg))
                    .unionImportApiNameList(arg.getUnionImportApiNameList())
                    .isEmptyValueToUpdate(arg.isEmptyValueToUpdate())
                    .importRestService(importRestService)
                    .importTaskHookService(importTaskHookService)
                    .fileExt(arg.getFileExt())
                    .jobId(arg.getJobId())
                    .importType(arg.getImportType())
                    .matchingType(arg.getMatchingType())
                    .fileService(fileService)
                    .user(context.getUser())
                    .importCompleteUrl(Config.completeUrl)
                    .pingUrl(Config.pingUrl)
                    .batchHandleCount(batchHandleCount)
                    .isWorkFlowEnabled(arg.isWorkFlowEnabled())
                    .isApprovalFlowEnabled(isApprovalEnabled)
                    .isTextureImportEnabled(arg.isTextureImportEnabled())
                    .batchTimeOut(arg.isNoBatch() ? noBatchTimeOut : timeOut)
                    .isUnionDuplicateChecking(arg.isUnionDuplicateChecking())
                    .relatedApiNameList(arg.getRelatedApiNameList())
                    .locale(arg.getLocale())
                    .objectCode(arg.getObjectCode())
                    .producer(importEventRocketMqProducer)
                    .specifiedField(arg.getSpecifiedField())
                    .checkOutOwner(arg.isCheckOutOwner())
                    .removeOutTeamMember(arg.isRemoveOutTeamMember())
                    .updateOwner(arg.isUpdateOwner())
                    .oldOwnerTeamMember(arg.getOldOwnerTeamMember())
                    .masterInfo(arg.getMasterInfo())
                    .detailInfo(arg.getDetailInfo())
                    .supportFieldMapping(arg.isSupportFieldMapping())
                    .extendAttribute(arg.getExtendAttribute())
                    .importMethod(importMethod)
                    .build()
                    .init();

            importTask.invoke();
        } finally {
            I18N.clearContext();
        }
    }

    private void initI18Context(User user) {
        I18N.setContext(user.getTenantId(), user.getLocale());
    }

    private String getObjectApiName(ImportDataArg arg) {
        if (isMasterDetailImport(arg)) {
            if (arg.isSupportFieldMapping() && Objects.nonNull(arg.getMasterInfo())) {
                return arg.getMasterInfo().getApiName();
            }
            if (!arg.isSupportFieldMapping() && CollectionUtils.isNotEmpty(arg.getUnionImportApiNameList())) {
                return arg.getUnionImportApiNameList().get(0);
            }
        }
        return arg.getImportObjectApiName();
    }

    private boolean isMasterDetailImport(ImportDataArg arg) {
        return (CollectionUtils.isNotEmpty(arg.getUnionImportApiNameList()) && !arg.isSupportFieldMapping())
                || (arg.isSupportFieldMapping() && CollectionUtils.isNotEmpty(arg.getDetailInfo()));
    }

    private boolean isApprovalFlowEnabled(String tenantId, String objectApiName) {
        FindFlowType.Arg flowTypeArg = FindFlowType.Arg.builder().entityId(objectApiName).build();
        Map<String, String> headers = ImmutableMap.of("x-fs-ei", tenantId);
        FindFlowType.Result flowTypeResult = approvalFlowRestProxy.findFlowType(flowTypeArg, headers);
        return !flowTypeResult.openFreeApproval();
    }

    private String findImportMethod(User user, String importObjectApiName, int importType) {
        if (ImportType.INSERT.getValue() != importType) {
            return ImportMethod.NORMAL.getMethod();
        }
        return importRestService.findImportMethod(user, importObjectApiName);
    }
}
