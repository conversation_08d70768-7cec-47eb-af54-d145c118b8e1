package com.facishare.paas.metadata.dataloader.rest.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;

import java.util.Map;

/**
 * CRM对象描述文档DTO
 * 用于CRM服务API调用的请求和响应数据结构
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-15
 */
public interface ObjectDescribeDocument {

    /**
     * 请求参数
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        @JsonProperty("describe_apiname")
        @SerializedName("describe_apiname")
        private String describeApiName;
    }

    /**
     * 响应结果
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = false)
    class Result extends BaseDTO.CrmResult {
        private objectDescribe data;

        @Data
        public static class objectDescribe {
            Map<String, Object> objectDescribe;
        }
    }

}
