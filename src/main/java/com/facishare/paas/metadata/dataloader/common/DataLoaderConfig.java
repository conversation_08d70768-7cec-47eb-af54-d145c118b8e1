package com.facishare.paas.metadata.dataloader.common;

import com.alibaba.fastjson.JSON;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.Map;
import java.util.Set;

/**
 * create by zhaoju on 2021/07/23
 */

@Getter
@Slf4j
public enum DataLoaderConfig {
    INSTANCE(),
    ;

    DataLoaderConfig() {
        ConfigFactory.getConfig("fs-paas-metadata-dataloader", iConfig -> {
            this.maxTryTimesFromConfig = iConfig.getInt("maxRetryTimes", 1800);
            this.intervalFromConfig = iConfig.getInt("interval", 4000);
            this.maxFinishedCount = iConfig.getInt("maxFinishedCount", 10);
            exportMaxCount = iConfig.getInt("exportMaxCount", 10000);
            dataBatchSize = iConfig.getInt("dataBatchSize", 200);
            bizBatchSize = parseMapFromConfig(iConfig, "bizBatchSize");
            excelColumnWidth = iConfig.getInt("excelColumnWidth", 20);
            importDataExpireTimeOffset = iConfig.getInt("importDataExpireTimeOffset", 0);
            supportedImageFormats = getSetFromConfig(iConfig, "supportedImageFormats", "gif,jpg,jpeg,png,bmp,webp,tiff,heif");
        });
    }

    private volatile int maxTryTimesFromConfig = 1800;
    private volatile int intervalFromConfig = 4000;
    private volatile int maxFinishedCount = 10;
    public volatile int exportMaxCount = 10000;
    public volatile int dataBatchSize = 200;
    public volatile int excelColumnWidth = 20;
    public int importDataExpireTimeOffset = 0;
    public volatile Map<String, Integer> bizBatchSize = Maps.newConcurrentMap();
    /**
     * 支持的图片格式配置
     * 默认支持 FastImageInfoHelper 能解析的所有格式
     */
    public volatile Set<String> supportedImageFormats = Sets.newHashSet();


    private static <T> Map<String, T> parseMapFromConfig(IConfig config, String key) {
        String data = config.get(key);
        if (Strings.isNullOrEmpty(data)) {
            return Collections.unmodifiableMap(Maps.newHashMap());
        }
        try {
            Map map = JSON.parseObject(data, Map.class);
            return Collections.unmodifiableMap(map);
        } catch (Exception e) {
            log.error("parseMapFromConfig failed,config:{},key:{},data:{}", config.getName(), key, data, e);
            return Collections.unmodifiableMap(Maps.newHashMap());
        }
    }

    public int getExportBizBatchSize(String biz) {
        return bizBatchSize.getOrDefault(biz, 200);
    }

    private static Set<String> getSetFromConfig(IConfig config, String key, String defaultValueSet) {
        // 避免枚举初始化时序问题：直接在方法内创建 Splitter，不依赖静态字段
        Splitter configSplitter = Splitter.on(",").omitEmptyStrings().trimResults();
        return Sets.newHashSet(configSplitter.split(config.get(key, defaultValueSet)));
    }
}
