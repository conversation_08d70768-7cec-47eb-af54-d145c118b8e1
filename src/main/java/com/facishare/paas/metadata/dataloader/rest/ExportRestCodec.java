package com.facishare.paas.metadata.dataloader.rest;

import com.facishare.rest.core.codec.AbstractRestCodeC;
import com.facishare.rest.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * <AUTHOR>
 * @date 2019-11-04 10:21
 */
@Slf4j
public class ExportRestCodec extends AbstractRestCodeC {

    @Override
    public <T> byte[] encodeArg(T obj) {

        if(Objects.isNull(obj)){
            return null;
        }

        if(obj instanceof String){
            return ((String) obj).getBytes(UTF_8);
        }
        return JsonUtil.toJsonWithNull(obj).getBytes(UTF_8);
    }

    @Override
    public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
        String bodyString = new String(bytes, UTF_8);
        if(clazz==String.class){
            return (T)bodyString;
        }

        T ret = JsonUtil.fromJson(new String(bytes, UTF_8), clazz);
        return ret;
    }
}
