package com.facishare.paas.metadata.dataloader.image.decorator;

import com.facishare.paas.I18N;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.dataloader.image.extractor.UnifiedImageExtractor;
import com.facishare.paas.metadata.dataloader.image.model.CellPosition;
import com.facishare.paas.metadata.dataloader.image.model.ImageFieldMapping;
import com.facishare.paas.metadata.dataloader.image.model.ImageUploadResult;
import com.facishare.paas.metadata.dataloader.image.service.ImageUploadService;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.service.CrmMetadataService;
import com.facishare.paas.metadata.dataloader.util.FastImageInfoHelper;
import com.facishare.paas.metadata.dataloader.util.I18NKey;
import com.facishare.paas.metadata.dataloader.util.IRowReader;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.util.CollectionUtils;

import java.io.FileInputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 优化的图片感知行读取器
 * 实现方案A：在readRow中处理表头识别和图片处理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class OptimizedImageAwareRowReader implements IRowReader {

    @Getter
    private final IRowReader originalRowReader;
    private final String fileName;
    private final List<String> apiNameList;
    private final User user;

    private final UnifiedImageExtractor imageExtractor;
    private final ImageUploadService imageUploadService;
    private final CrmMetadataService crmMetadataService;

    private List<String> headerRow;
    private final List<ImageFieldMapping> imageFieldMappings = Lists.newArrayList(); // 新的多列映射

    /**
     * 构造函数
     *
     * @param originalRowReader  原始的行读取器
     * @param imageExtractor     统一图片提取器
     * @param imageUploadService 图片上传服务
     * @param fileName           Excel文件路径
     * @param apiNameList        API名称
     * @param user               用户信息
     */
    @Builder
    public OptimizedImageAwareRowReader(IRowReader originalRowReader,
                                        UnifiedImageExtractor imageExtractor,
                                        ImageUploadService imageUploadService,
                                        CrmMetadataService crmMetadataService,
                                        String fileName, List<String> apiNameList, User user) {
        this.originalRowReader = originalRowReader;
        this.imageExtractor = imageExtractor;
        this.imageUploadService = imageUploadService;
        this.fileName = fileName;
        this.apiNameList = apiNameList;
        this.user = user;

        this.crmMetadataService = crmMetadataService;
    }

    @Override
    public boolean getRows(int sheetIndex, int curRow, List<String> rowList) {
        if (curRow == 1) {
            // 第一行：表头处理
            handleHeaderRow(rowList, sheetIndex);
        } else {
            // 数据行：图片处理（逐行架构优化模式）
            handleDataRow(curRow, rowList, sheetIndex);
        }

        // 调用原始RowReader
        return originalRowReader.getRows(sheetIndex, curRow, rowList);
    }

    /**
     * 处理表头行（第一行）
     *
     * @param rowData 表头行数据
     */
    private void handleHeaderRow(List<String> rowData, int sheetIndex) {
        Map<String, List<Integer>> fieldColumnMapping = extractMergedCellMapping(fileName, sheetIndex);
        this.headerRow = new ArrayList<>(rowData);
        try {
            if (CollectionUtils.isEmpty(fieldColumnMapping)) {
                return;
            }
            log.info("Using pre-processed field mappings");
            String apiName = apiNameList.get(sheetIndex);
            List<IFieldDescribe> currentApiFields = crmMetadataService.getImageFields(apiName, user);
            for (IFieldDescribe fieldDescribe : currentApiFields) {
                String label = fieldDescribe.isRequired() ? fieldDescribe.getLabel() + I18N.text(I18NKey.MUST_FILL_IN) : fieldDescribe.getLabel();
                List<Integer> mappedColumnIndexes = fieldColumnMapping.get(label);
                if (mappedColumnIndexes != null && !mappedColumnIndexes.isEmpty()) {
                    ImageFieldMapping imageMapping = ImageFieldMapping.builder()
                            .apiName(apiName)
                            .fieldName(label)
                            .columnIndexes(mappedColumnIndexes)
                            .fieldDescribe(fieldDescribe) // 设置字段描述信息
                            .isImageField(true)
                            .separator("|")
                            .build();
                    this.imageFieldMappings.add(imageMapping);
                    log.debug("Built image field mapping: {} -> columns {}", label, mappedColumnIndexes);
                }
            }
        } catch (Exception e) {
            log.error("Critical error in buildImageFieldMappingsFromPreProcessing: {} - {}",
                    e.getClass().getSimpleName(), e.getMessage(), e);
        }
        log.info("Image processing initialized: {} field mappings", imageFieldMappings.size());
    }

    /**
     * 处理数据行
     *
     * @param curRow 当前行
     * @param rowData  行数据
     * @param sheetIndex 工作表索引
     */
    private void handleDataRow(int curRow, List<String> rowData, int sheetIndex) {
        if (CollectionUtils.isEmpty(imageFieldMappings)) {
            return; // 快速跳过
        }
        String apiName = apiNameList.get(sheetIndex);
        List<ImageFieldMapping> imageFieldMappings = this.imageFieldMappings.stream()
                .filter(mapping -> mapping.getApiName().equals(apiName))
                .collect(Collectors.toList());

        // 处理每个图片字段映射
        for (ImageFieldMapping fieldMapping : imageFieldMappings) {
            if (!fieldMapping.isImageField()) {
                continue; // 跳过非图片字段
            }
            try {
                // 处理该字段的所有图片列
                List<String> processedPaths = processImageFieldColumns(fieldMapping, curRow - 1, rowData, sheetIndex);
                // 合并图片路径并更新行数据
                updateRowDataWithMergedPaths(fieldMapping, rowData, processedPaths);
            } catch (Exception e) {
                log.warn("Failed to process image field '{}' at row {}: {}",
                        fieldMapping.getFieldName(), curRow, e.getMessage());
                // 保持原值，不中断整个导入流程
            }
        }
    }

    /**
     * 处理图片字段的所有列（逐行架构优化模式）
     *
     * @param fieldMapping 字段映射信息
     * @param rowIndex     行索引
     * @param rowData      行数据
     * @param sheetIndex   工作表索引
     * @return 处理后的图片路径列表
     */
    private List<String> processImageFieldColumns(ImageFieldMapping fieldMapping, int rowIndex, List<String> rowData, int sheetIndex) {
        List<String> processedPaths = new ArrayList<>();
        for (int colIndex : fieldMapping.getColumnIndexes()) {
            if (colIndex < rowData.size()) {
                String originalValue = rowData.get(colIndex);
                String processedValue = processImageCell(originalValue, rowIndex, colIndex, sheetIndex);
                if (StringUtils.isNotBlank(processedValue) && !Objects.equals(originalValue, processedValue)) {
                    // 更新行数据
                    rowData.set(colIndex, processedValue);
                    processedPaths.add(processedValue);
                }
            }
        }
        return processedPaths;
    }

    /**
     * 处理单个图片单元格的核心逻辑
     * 核心逻辑：跳过文本路径图片，处理嵌入式图片
     *
     * @param cellValue 单元格值
     * @param row       行号
     * @param col       列号
     * @param sheetIndex 工作表索引
     * @return 处理后的图片路径，如果不需要处理返回原值或null
     */
    private String processImageCell(String cellValue, int row, int col, int sheetIndex) {
        try {
            // 1. 检查是否为文本路径格式（需要跳过）
            if (isTextPathFormat(cellValue)) {
                log.debug("Skipping text path format at ({}, {}): {}", row, col, cellValue);
                return cellValue; // 保持原值不变
            }

            // 2. 尝试从ZIP中提取嵌入式图片
            CellPosition position = CellPosition.of(row, col);
            byte[] imageData = imageExtractor.extractImage(fileName, position, sheetIndex);

            if (imageData == null || imageData.length == 0) {
                log.debug("No embedded image found at position ({}, {})", row, col);
                return cellValue; // 保持原值不变
            }

            // 3. 检测图片格式并生成文件名
            String format = detectImageFormat(imageData);
            String fileName = generateImageFileName(row, col, format);

            // 4. 上传图片到服务器
            ImageUploadResult result = imageUploadService.uploadImage(imageData, fileName, format, user);
            if (result.isSuccess()) {
                log.debug("Embedded image uploaded successfully: {} -> {}, size: {} bytes",
                        fileName, result.getUploadedPath(), imageData.length);
                return fileName + "#" + result.getUploadedPath();
            } else {
                log.warn("Upload failed for embedded image at ({}, {}): {}", row, col, result.getErrorMessage());
                // 🎯 使用多语key标识上传失败
                String i18nKey = result.getI18nKey();
                return String.format("[IMG_ERROR:%s]%s", i18nKey, cellValue);
            }

        } catch (Exception e) {
            log.error("Error uploading embedded image at ({}, {}): {}", row, col, e.getMessage());
            // 🎯 使用多语key标识处理异常
            String i18nKey = I18NKey.IMAGE_PROCESSING_ERROR;
            return String.format("[IMG_ERROR:%s]%s", i18nKey, cellValue);
        }
    }

    /**
     * 检查是否为文本路径格式（需要跳过处理）
     * 格式：N_xxx|xxx
     *
     * @param cellValue 单元格值
     * @return 如果是文本路径格式返回true
     */
    private boolean isTextPathFormat(String cellValue) {
        if (StringUtils.isBlank(cellValue)) {
            return false;
        }

        String trimmed = cellValue.trim();
        // 检查是否为"N_xxx|xxx"格式
        return trimmed.startsWith("N_") && trimmed.contains("|");
    }

    /**
     * 检测图片格式
     *
     * @param imageData 图片字节数据
     * @return 图片格式（jpg, png, gif等）
     */
    private String detectImageFormat(byte[] imageData) {
        FastImageInfoHelper.ImageInfo imageInfo = FastImageInfoHelper.getImageInfo(imageData);
        if (imageInfo != null) {
            return imageInfo.getExtensionName();
        }
        return "png"; // 默认格式
    }

    /**
     * 生成图片文件名
     *
     * @param row    行号
     * @param col    列号
     * @param format 图片格式
     * @return 生成的文件名
     */
    private String generateImageFileName(int row, int col, String format) {
        return String.format("import_image_%d_%d_%d.%s",
                System.currentTimeMillis() % 10000, row, col, format);
    }

    // 删除：不再使用缓存机制，直接使用UnifiedImageExtractor

    /**
     * 将合并后的图片路径更新到行数据中
     *
     * @param fieldMapping   字段映射信息
     * @param rowData        行数据
     * @param processedPaths 处理后的图片路径列表
     */
    private void updateRowDataWithMergedPaths(ImageFieldMapping fieldMapping, List<String> rowData,
                                              List<String> processedPaths) {
        // 过滤空路径
        List<String> validPaths = processedPaths.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        // 合并路径
        String mergedPaths = String.join(fieldMapping.getSeparator(), validPaths);

        // 更新主列（第一列）
        int primaryColumn = fieldMapping.getPrimaryColumnIndex();
        if (primaryColumn >= 0 && primaryColumn < rowData.size()) {
            rowData.set(primaryColumn, mergedPaths);

            if (fieldMapping.isMultiColumn()) {
                log.debug("Merged {} images for field '{}' at column {}: {}",
                        validPaths.size(), fieldMapping.getFieldName(), primaryColumn, mergedPaths);
            }
        }
        // 清空其他列
        for (int colIndex : fieldMapping.getColumnsToClean()) {
            if (colIndex < rowData.size()) {
                rowData.set(colIndex, "");
            }
        }
    }

    @Override
    public void setTotalLineCount(int rowCount) {
        originalRowReader.setTotalLineCount(rowCount);
    }

    /**
     * 提取合并单元格映射信息
     *
     * @param fileName Excel文件路径
     * @return 字段名到列索引列表的映射
     */
    private Map<String, List<Integer>> extractMergedCellMapping(String fileName, int sheetIndex) {
        try (FileInputStream fis = new FileInputStream(fileName);
             Workbook workbook = WorkbookFactory.create(fis)) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            if (sheet == null) {
                log.warn("No sheets found in Excel file: {}", fileName);
                return Collections.emptyMap();
            }
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                log.warn("No header row found in Excel file: {}", fileName);
                return Collections.emptyMap();
            }
            // 获取合并单元格区域
            List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();
            log.debug("Found {} merged cell regions", mergedRegions.size());
            // 构建字段映射
            return buildFieldMapping(headerRow, mergedRegions);
        } catch (Exception e) {
            log.error("Error extracting merged cell mapping from file {}: {}", fileName, e.getMessage());
            return Collections.emptyMap();
        }
    }

    /**
     * 构建字段映射关系
     *
     * @param headerRow     表头行
     * @param mergedRegions 合并单元格区域列表
     * @return 字段名到列索引列表的映射
     */
    private Map<String, List<Integer>> buildFieldMapping(Row headerRow, List<CellRangeAddress> mergedRegions) {
        Map<String, List<Integer>> fieldMapping = new HashMap<>();
        int lastCellNum = headerRow.getLastCellNum();
        // 创建列索引到字段名的映射
        Map<Integer, String> columnToField = new HashMap<>();
        // 1. 处理合并单元格
        for (CellRangeAddress mergedRegion : mergedRegions) {
            // 只处理第一行的合并单元格（表头）
            if (mergedRegion.getFirstRow() == 0 && mergedRegion.getLastRow() == 0) {
                int firstCol = mergedRegion.getFirstColumn();
                int lastCol = mergedRegion.getLastColumn();

                // 获取合并单元格的值（从第一个单元格）
                Cell firstCell = headerRow.getCell(firstCol);
                String fieldName = getCellStringValue(firstCell);

                if (StringUtils.isNotBlank(fieldName)) {
                    // 将合并区域的所有列都映射到这个字段
                    for (int col = firstCol; col <= lastCol; col++) {
                        columnToField.put(col, fieldName);
                    }
                    log.debug("Merged field '{}' spans columns {} to {}", fieldName, firstCol, lastCol);
                }
            }
        }

        // 2. 处理非合并单元格
        for (int col = 0; col < lastCellNum; col++) {
            if (!columnToField.containsKey(col)) {
                Cell cell = headerRow.getCell(col);
                String fieldName = getCellStringValue(cell);

                if (StringUtils.isNotBlank(fieldName)) {
                    columnToField.put(col, fieldName);
                    log.debug("Single field '{}' at column {}", fieldName, col);
                }
            }
        }

        // 3. 构建字段到列列表的映射
        for (Map.Entry<Integer, String> entry : columnToField.entrySet()) {
            String fieldName = entry.getValue();
            int columnIndex = entry.getKey();

            fieldMapping.computeIfAbsent(fieldName, k -> new ArrayList<>()).add(columnIndex);
        }

        // 4. 对每个字段的列索引进行排序
        fieldMapping.values().forEach(Collections::sort);

        log.info("Built field mapping: {} fields covering {} columns",
                fieldMapping.size(), columnToField.size());

        return fieldMapping;
    }

    /**
     * 获取单元格的字符串值
     *
     * @param cell 单元格
     * @return 字符串值，如果单元格为null或空则返回空字符串
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        try {
            switch (cell.getCellType()) {
                case Cell.CELL_TYPE_STRING:
                    return cell.getStringCellValue();
                case Cell.CELL_TYPE_NUMERIC:
                    return String.valueOf(cell.getNumericCellValue());
                case Cell.CELL_TYPE_BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case Cell.CELL_TYPE_FORMULA:
                    try {
                        return cell.getStringCellValue();
                    } catch (Exception e) {
                        return cell.getCellFormula();
                    }
                default:
                    return "";
            }
        } catch (Exception e) {
            log.debug("Error reading cell value: {}", e.getMessage());
            return "";
        }
    }

}
