package com.facishare.paas.metadata.dataloader.model;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * 预定义对象APIName Mapping
 * <p>
 * Created by l<PERSON>yiguang on 2017/8/16.
 */
public enum ObjectAPINameMapping {
    ReturnOrderReturnOrderProduct("multiimport_returnorder_returnorderproduct","multiimport_returnorder_returnorderproduct",23),
    CustomerContact("multiimport_customer_contact","multiimport_customer_contact",23),
    CustomerOrderCustomerOrderProduct("multiimport_customerorder_customerorderproduct","multiimport_customerorder_customerorderproduct",23),
    AccountAdd("AccountAddObj", "location", 39),
    AccountAtt("AccountAttObj", "attach", 24),
    AccountFinInfo("AccountFinInfoObj", "invoice", 40),
    AccountCost("AccountCostObj", "cost", 26),
    Account("AccountObj", "customer", 2),
    Contact("ContactObj", "contact", 3),
    Contract("ContractObj", "contract", 16),
    HighSeas("HighSeasObj", "highseas", 18),
    InvoiceApplication("InvoiceApplicationObj", "tradebill", 9),
    Leads("LeadsObj", "salesclue", 1),
    LeadsPool("LeadsPoolObj", "salescluepool", 17),
    MarketingEvent("MarketingEventObj", "marketingevent", 20),
    Opportunity("OpportunityObj", "opportunity", 8),
    //Payment("PaymentObj", "tradepayment", 5),
    Product("ProductObj", "product", 4),
    Refund("RefundObj", "traderefund", 6),
    ReturnedGoodsInvoice("ReturnedGoodsInvoiceObj", "returnorder", 12),
    ReturnedGoodsInvoiceProduct("ReturnedGoodsInvoiceProductObj", "returnorderproduct", 27),
    SaleAction("SaleActionObj", "saleaction", 7),
    SaleActionStage("SaleActionStageObj", "saleactionstage", 0),
    SalesOrder("SalesOrderObj", "customerorder", 11),
    SalesOrderProduct("SalesOrderProductObj", "customerorderproduct", 28),
    Visiting("VisitingObj", "visit", 13),
    BizQuery("BizQueryObj", "BizQuery", 0), //工商信息
    NotSFAObject("NotSFAObject", "NotSFAObject", 0),
    SaleEvent("saleevent","saleevent",0),
    ;
    String apiName;
    String oldAPIName;
    int type;

    public String getApiName() {
        return apiName;
    }

    public String getOldAPIName() {
        return oldAPIName;
    }

    public int getType() {
        return type;
    }

    ObjectAPINameMapping(String apiName, String oldAPIName, int type) {
        this.apiName = apiName;
        this.oldAPIName = oldAPIName;
        this.type = type;
    }

    private static final Map<String, ObjectAPINameMapping> apiNameMap = Maps.newHashMap();
    private static final Map<String, ObjectAPINameMapping> oldAPINameMap = Maps.newHashMap();

    static {
        for (ObjectAPINameMapping mapping : ObjectAPINameMapping.values()) {
            apiNameMap.put(mapping.apiName, mapping);
            oldAPINameMap.put(mapping.oldAPIName, mapping);
        }
    }

    public static String toOldAPIName(String apiName) {
        return apiNameMap.get(apiName).oldAPIName;
    }

    /**
     * 获取对象apiName
     *
     * @param oldAPIName sfa老对象的名称
     * @return 对象的apiname, 如果不是老对象返回 null
     */
    public static String toApiName(String oldAPIName) {
        ObjectAPINameMapping objectAPINameMapping = oldAPINameMap.get(oldAPIName.toLowerCase());
        return objectAPINameMapping == null ? null : objectAPINameMapping.apiName;
    }

    public static String toObjectType(String apiName) {
        //老对象转换为Type
        ObjectAPINameMapping mapping = apiNameMap.get(apiName);
        return mapping != null ? String.valueOf(mapping.type) : apiName; //自定对象没有type类型，直接返回APIName
    }

    public static int toIntObjectType(String apiName) {
        //老对象转换为Type
        ObjectAPINameMapping mapping = apiNameMap.get(apiName);
        return mapping != null ? mapping.type : 0;
    }

    public static boolean isSFAObject(String apiName) {
        ObjectAPINameMapping object = of(apiName);
        return object != NotSFAObject;
    }

    public static ObjectAPINameMapping of(String apiName) {
        ObjectAPINameMapping ret = oldAPINameMap.get(apiName);
        if (ret == null) {
            return NotSFAObject;
        }
        return ret;
    }

    public static boolean isAccountDetailObject(String apiName) {
        ObjectAPINameMapping ret = apiNameMap.get(apiName);
        return getAccountDetailObjects().contains(ret);
    }

    public static List<ObjectAPINameMapping> getAccountDetailObjects() {
        return Lists.newArrayList(AccountAdd, AccountAtt, AccountFinInfo);
    }

    public static String getStatusFieldAPIName(String apiName) {
        ObjectAPINameMapping object = of(apiName);
        switch (object) {
            case Account:
                return "account_status";
            case Contact:
                return "contact_status";
            case Leads:
                return "leads_status";
//            case Payment:
//                return "payment_status";
//            case Product:
//           Product     return "product_status";
            case SalesOrder:
                return "order_status";
            default:
                return "status";
        }
    }

    public static String getModuleCode(String apiName) {
        if ("AccountObj".equals(apiName)) {
            return "account";
        }
        if ("ContactObj".equals(apiName)) {
            return "contact";
        }
        if ("ProductObj".equals(apiName)) {
            return "product";
        }
        if ("SalesOrderObj".equals(apiName)) {
            return "sales_order";
        }
        if ("PaymentObj".equals(apiName)) {
            return "payment";
        }
        if ("LeadsObj".equals(apiName)) {
            return "leads";
        }
        if ("InvoiceApplicationObj".equals(apiName)) {
            return "invoice_application";
        }
        if ("ContractObj".equals(apiName)) {
            return "contract";
        }
        if ("OpportunityObj".equals(apiName)) {
            return "opportunity";
        }
        if ("RefundObj".equals(apiName)) {
            return "refund";
        }
        if ("VisitingObj".equals(apiName)) {
            return "visiting";
        }
        if ("LeadsPoolObj".equals(apiName)) {
            return "leads_pool";
        }
        if ("ReturnedGoodsInvoiceObj".equals(apiName)) {
            return "returned_goods_invoice";
        }
        return null;
    }

}
