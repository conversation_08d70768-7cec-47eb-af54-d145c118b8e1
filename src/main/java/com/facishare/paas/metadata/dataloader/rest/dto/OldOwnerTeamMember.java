package com.facishare.paas.metadata.dataloader.rest.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class OldOwnerTeamMember {
    @SerializedName(value = "strategy")
    @JSONField(name = "strategy", alternateNames = "_strategy")
    private String strategy;
    @SerializedName(value = "permissionType")
    @JSONField(name = "permissionType", alternateNames = "_permissionType")
    private String permissionType;
    @SerializedName(value = "roleList")
    @JSONField(name = "roleList", alternateNames = "_roleList")
    private List<String> roleList;
    @SerializedName(value = "otherObjects")
    @JSONField(name = "otherObjects", alternateNames = "_otherObjects")
    private List<String> otherObjects;
}
