package com.facishare.paas.metadata.dataloader.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.filter.ContextManager;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.rest.dto.DetailArg;
import com.facishare.paas.metadata.dataloader.service.IBulkExcelPrintService;
import com.facishare.paas.metadata.dataloader.service.IBulkExportFileService;
import com.facishare.paas.metadata.dataloader.service.IBulkExportService;
import com.facishare.paas.metadata.dataloader.service.IExportByPrintTemplateService;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import java.util.List;
import java.util.Objects;

/**
 * 导出任务Controller，不作
 *
 * <AUTHOR>
 * @date 2019-06-03 14:24
 */

@Controller
@Path("/bulkExport")
@Slf4j
@Consumes({"application/json"})
@Produces({"application/json"})
public class BulkExportRestController {
    @Autowired
    private IBulkExportService bulkExportService;
    @Autowired
    private IBulkExportFileService bulkExportFieldService;
    @Autowired
    private IBulkExcelPrintService bulkExcelPrintService;
    @Autowired
    private IExportByPrintTemplateService exportByPrintTemplateService;

    @POST
    @Path("/verify")
    public RestResult verify(String json) {
        log.info("Start verify export task, paramJson:{}", json);
        Arg arg = JSONObject.parseObject(json, Arg.class);
        Context.ExportContext context = (Context.ExportContext) ContextManager.getContext();
        configContext(context, arg);
        // 调用fs-crm的action进行verify
        IBulkExportService.VerifyResult verifyResult;
        if (arg.isExportToLocalAction()) {
            verifyResult = bulkExportFieldService.verify(context);
        } else if (arg.isBulkExportPrintTemplate()) {
            if (StringUtils.isBlank(arg.getResultProcessor())) {
                verifyResult = bulkExcelPrintService.verify(context);
            } else {
                verifyResult = exportByPrintTemplateService.verify(context);
            }
        } else {
            verifyResult = bulkExportService.verify(context);
        }
        log.info("Finish verify export task, {}", verifyResult);
        return RestResult.builder().code(verifyResult.getCode()).message(verifyResult.getMessage()).build();
    }

    @POST
    @Path("/invoke")
    public RestResult invoke(String json) {
        log.info("Start export data task, paramJson:{}", json);
        Arg arg = JSONObject.parseObject(json, Arg.class);
        Context.ExportContext context = (Context.ExportContext) ContextManager.getContext();
        configContext(context, arg);
        IBulkExportService.ExportResult exportResult;
        if (arg.isExportToLocalAction()) {
            exportResult = bulkExportFieldService.export(context);
        } else if (arg.isBulkExportPrintTemplate()) {
            if (StringUtils.isBlank(arg.getResultProcessor())) {
                exportResult = bulkExcelPrintService.export(context);
            } else {
                exportResult = exportByPrintTemplateService.export(context);
            }
        } else {
            exportResult = bulkExportService.export(context);
        }
        //导出消息
        //功能权限
        log.info("Finish export data task, {}", exportResult);
        return RestResult.builder().code(exportResult.getCode()).message(exportResult.getMessage()).build();
    }

    private void configContext(Context.ExportContext context, Arg arg) {
        // 设置基本参数
        if (!Strings.isNullOrEmpty(arg.getLocale())) {
            context.setLocale(arg.getLocale());
        }
        User user = context.getUser();
        user.setEa(arg.getEa());
        if (StringUtils.isNotBlank(arg.getTimezone())) {
            user.setTimezone(arg.getTimezone());
        }
        context.setJobId(arg.getJobId());
        context.setObjectDescribeApiName(arg.getApiName());
        // 设置导出时参数
        context.setDataIdList(arg.getDataIdList());
        context.setIncludeId(arg.includeId);
        context.setIsIgnoreSceneFilter(arg.isIgnoreSceneFilter);
        context.setOtherNameList(arg.getOtherNameList());
        context.setRecordTypeApiName(arg.getRecordTypeApiName());
        context.setSearchQueryInfo(arg.getSearchQueryInfo());
        context.setSearchTemplateId(arg.getSearchTemplateId());
        context.setIsIgnoreSceneRecordType(arg.getIsIgnoreSceneRecordType());
        context.setSearchTemplateType(arg.getSearchTemplateType());
        context.setAccordingToList(arg.getAccordingToList());
        context.setIsNoExportRelevantTeam(arg.getIsNoExportRelevantTeam());
        context.setJobId(arg.getJobId());
        context.setDetailArg(arg.getDetailArg());
        context.setMethod(arg.getMethod());
        context.setFieldApiNames(arg.getFieldApiNames());
        context.setPrintTemplateId(arg.getPrintTemplateId());
        context.setFileFields(arg.getFileFields());
        context.setWhatApiName(arg.getWhatApiName());
        context.setResultProcessor(arg.getResultProcessor());
    }

    @Data
    public static class Arg {
        @JSONField(name = "object_describe_api_name", alternateNames = {"_object_describe_api_name", "apiName", "_apiName"})
        private String apiName;

        @JSONField(name = "userId", alternateNames = "_userId")
        private String userId;

        @JSONField(name = "jobId", alternateNames = "_jobId")
        private String jobId;

        @JSONField(name = "ea", alternateNames = "_ea")
        private String ea;

        @JSONField(name = "ei", alternateNames = "_ei")
        private String ei;

        @JSONField(name = "X-fs-Locale", alternateNames = "_X-fs-Locale")
        private String locale = "zh-CN";

        @JSONField(name = "timezone", alternateNames = "_timezone")
        private String timezone;

        @JSONField(name = "dataIdList", alternateNames = "_dataIdList")
        private List<String> dataIdList;

        @JSONField(name = "recordType_apiName", alternateNames = "_recordType_apiName")
        private String recordTypeApiName;

        @JSONField(name = "search_template_id", alternateNames = "_search_template_id")
        private String searchTemplateId;

        @JSONField(name = "search_query_info", alternateNames = "_search_query_info")
        private String searchQueryInfo;

        @JSONField(name = "other_name_list", alternateNames = "_other_name_list")
        private List<String> otherNameList;

        @JSONField(name = "include_id", alternateNames = "_include_id")
        private Boolean includeId;

        @JSONField(name = "ignore_scene_filter", alternateNames = "_ignore_scene_filter")
        private Boolean isIgnoreSceneFilter;

        @JSONField(name = "according_to_list", alternateNames = "_according_to_list")
        private Boolean accordingToList;

        @JSONField(name = "ignore_scene_record_type", alternateNames = "_ignore_scene_record_type")
        private Boolean isIgnoreSceneRecordType;

        @JSONField(name = "search_template_type", alternateNames = "_search_template_type")
        private String searchTemplateType;

        @JSONField(name = "no_export_relevant_team", alternateNames = "_no_export_relevant_team")
        private Boolean isNoExportRelevantTeam;

        @JSONField(name = "method", alternateNames = "_method")
        private Integer method;

        @JSONField(name = "field_api_name_list", alternateNames = "_field_api_name_list")
        private List<String> fieldApiNames;

        @JSONField(name = "detail_arg", alternateNames = "_detail_arg")
        private DetailArg detailArg;

        @JSONField(name = "print_template_id", alternateNames = "_print_template_id")
        private String printTemplateId;
        @JSONField(name = "bulk_export_type", alternateNames = "_bulk_export_type")
        private String bulkExportType;
        @JSONField(name = "file_fields", alternateNames = "_file_fields")
        private List<String> fileFields;
        @JSONField(name = "what_api_name", alternateNames = "_what_api_name")
        private String whatApiName;
        @JSONField(name = "result_processor", alternateNames = "_result_processor")
        private String resultProcessor;

        public boolean isExportToLocalAction() {
            return Objects.nonNull(method) && CollectionUtils.isNotEmpty(fieldApiNames);
        }

        @Deprecated
        public boolean isBulkExportExcelTemplate() {
            return StringUtils.equals(bulkExportType, "printTemplate") && StringUtils.isNotEmpty(printTemplateId);
        }

        public boolean isBulkExportPrintTemplate() {
            return StringUtils.equals(bulkExportType, "printTemplate") && StringUtils.isNotEmpty(printTemplateId);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class RestResult {
        private int code;
        private String message;
    }

}
