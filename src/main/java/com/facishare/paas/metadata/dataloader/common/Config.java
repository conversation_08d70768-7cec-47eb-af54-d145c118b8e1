package com.facishare.paas.metadata.dataloader.common;

import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.rest.Common;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Maps;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-09-09 15:28
 */
public class Config {
    public static String pingUrl;
    public static String completeUrl;
    public static String exportMaxRetryTime;

    static {
        ConfigFactory.getConfig("async-job-center-callback", iConfig -> {
            completeUrl = iConfig.get("completeUrl");
            pingUrl = iConfig.get("pingUrl");
        });
    }

    public static Map<String, String> makeHeader(Context context) {
        Map<String, String> result = Maps.newHashMap();
        User user = context.getUser();
        result.put(Common.HTTP_HEADER_EI, user.getTenantId());
        result.put(Common.HTTP_HEADER_EA, user.getEa());
        result.put(Common.TENANT_ID, user.getTenantId());
        result.put(Common.HTTP_HEADER_UID, user.getUserId());
        result.put(Common.USER_ID, user.getUserId());
        result.put(Common.HTTP_HEADER_LOCALE, context.getLocale());
        result.put(Common.HTTP_HEADER_OUT_EI, user.getOutTenantId());
        result.put(Common.HTTP_HEADER_OUT_USER, user.getOutUserId());
        if (Objects.nonNull(user.getOutAppId())) {
            result.put(Common.HTTP_HEADER_APP_ID, user.getOutAppId());
        }
        result.put(Common.OUT_IDENTITY_TYPE, user.getIdentityType());
        result.put(Common.HTTP_HEADER_TIME_ZONE, user.getTimezone());
        return result;
    }
}
