package com.facishare.paas.metadata.dataloader.task;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.metadata.dataloader.exception.CancelTaskException;
import com.facishare.paas.metadata.dataloader.exception.DataLoaderBusinessException;
import com.facishare.paas.metadata.dataloader.exception.ExcelReadException;
import com.facishare.paas.metadata.dataloader.exception.NoImportPreProcessingFunctionException;
import com.facishare.paas.metadata.dataloader.model.*;
import com.facishare.paas.metadata.dataloader.mq.ImportEventRocketMqProducer;
import com.facishare.paas.metadata.dataloader.rest.dto.OldOwnerTeamMember;
import com.facishare.paas.metadata.dataloader.service.FileService;
import com.facishare.paas.metadata.dataloader.service.IBulkImportRestService;
import com.facishare.paas.metadata.dataloader.service.IImportTaskHookService;
import com.facishare.paas.metadata.dataloader.util.FileUtil;
import com.facishare.paas.metadata.dataloader.util.I18NExt;
import com.facishare.paas.metadata.dataloader.util.I18NKey;
import com.fxiaoke.limit.GuavaLimiter;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.paas.metadata.dataloader.task.Job.*;

/**
 * 导入任务
 * <p>
 * Created by zhenglei on 2017/1/1.
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@SuppressWarnings("unchecked")
@Slf4j
public class ImportTask extends BaseTask {
    private String excelFilePath;
    private String fileExt;
    private String locale;
    private String importObjectApiName;
    /**
     * 联合导入的对象列表
     */
    private List<String> unionImportApiNameList;
    private User user;
    private int importType;
    private int matchingType;
    private boolean isEmptyValueToUpdate;
    private String jobId;
    private String importCompleteUrl;
    private String pingUrl;
    private boolean isWorkFlowEnabled;
    private boolean isApprovalFlowEnabled;
    private boolean isTextureImportEnabled;
    boolean isUnionDuplicateChecking;
    private List<String> relatedApiNameList;    //支持销售记录导入关联对象
    private String specifiedField;
    private boolean checkOutOwner;
    private boolean removeOutTeamMember;
    private boolean updateOwner;
    private OldOwnerTeamMember oldOwnerTeamMember;
    private MasterInfo masterInfo;
    private List<DetailInfo> detailInfo;
    private boolean supportFieldMapping;
    private Map<String, Object> extendAttribute;
    private String importMethod;

    private FileService fileService;
    private IBulkImportRestService importRestService;

    private int doneCount;
    private int successCount;
    private int failCount;
    private int notImportCount;
    private int totalRowCount;
    //用户点击取消，取消任务
    private boolean isCancelTask;

    //系统出现异常，终止任务
    private boolean isAbortTask;

    private int batchHandleCount;
    private int batchTimeOut;
    private String objectCode;


    //批量调用crm服务数据
    List<DataItem> batchData;
    List<ExcelCol> colIndexAndHeader;
    int batchCount;

    private Job job;
    private ExcelExecutor excelExecutor;
    private ExcelExecutor preExcelExecutor;

    private ImportEventRocketMqProducer producer;
    private IImportTaskHookService importTaskHookService;

    /**
     * 导入批次
     */
    private int importBatch;

    private FileUtil.LocalFile localFile;

    private static String appName = ConfigHelper.getProcessInfo().getName();
    private static String serverIp = ConfigHelper.getProcessInfo().getIp();
    private static String profile = ConfigHelper.getProcessInfo().getProfile();
    private static String action = "import";

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    private final String CANCEL_STATE = I18NExt.getOrDefault(I18NKey.IMPORT_NO_IMPORT, "未导入");//ignoreI18n

    public ImportTask init() {
        batchData = Lists.newArrayList();
        if (isSupportFieldMapping() && !CollectionUtils.isEmpty(detailInfo)) {
            unionImportApiNameList = Lists.newArrayList();
            String apiName = masterInfo.getApiName();
            unionImportApiNameList.add(apiName);
            List<String> detailApiNames = detailInfo.stream().map(BaseInfo::getApiName).collect(Collectors.toList());
            unionImportApiNameList.addAll(detailApiNames);
        }
        excelExecutor = ExcelExecutor.builder()
                .fileExt(fileExt)
                .user(user)
                .unionImportApiNameList(unionImportApiNameList)
                .importObjectApiName(importObjectApiName)
                .importRestService(importRestService)
                .supportFieldMapping(supportFieldMapping)
                .isTextureImportEnabled(isTextureImportEnabled)
                .importMethod(importMethod)
                .locale(locale)
                .build()
                .init();
        job = Job.builder()
                .jobId(jobId)
                .importCompleteUrl(importCompleteUrl)
                .pingUrl(pingUrl)
                .build()
                .init();
        return this;
    }

    private String execImportTaskHookBefore(String path) {
        ImportTaskHook.Arg arg = ImportTaskHook.Arg.builder()
                .filePath(path)
                .importObjectApiName(importObjectApiName)
                .jobId(jobId)
                .fileExt(fileExt)
                .build();
        ImportTaskHook.Result result = importTaskHookService.before(user, arg);
        return result.getFilePath();
    }

    private String execImportTaskHookAfter(String filePath, String fileName) {
        ImportTaskHook.Arg arg = ImportTaskHook.Arg.builder()
                .filePath(filePath)
                .importObjectApiName(importObjectApiName)
                .jobId(jobId)
                .fileExt(fileExt)
                .originalFilename(fileName)
                .build();
        ImportTaskHook.Result result = importTaskHookService.after(user, arg);
        return result.getFilePath();
    }

    private void calcTotalRowCount(int curRow, List<String> rowList) {
        if (excelExecutor.isEmptyLine(rowList) || curRow == 1) {
            //空行或者标题行不计算总行数
            return;
        }
        totalRowCount++;
    }

    public void invoke() throws IOException {
        try {
            if (!executePreProcessing()) {
                return;
            }
            execute();
        } finally {
            //clean work
            job.stopHeartBeats();
            // 完成后删除本地文件
            Optional.ofNullable(localFile).ifPresent(FileUtil.LocalFile::deleteTempFile);
        }
    }

    /**
     * @return true 预处理通过，继续导入。 false 预处理失败，导入终止
     */
    private boolean executePreProcessing() throws IOException {
        if (isAddAction()) {
            log.warn("import addAction not support preProcess,user:{},apiName:{}", user, getPreProcessingCurrentApiName());
            return true;
        }
        log.info("Start Import Task execute, user:{}, filePath:{}, fileExt:{}, apiName:{}, importCompleteUrl:{}, pingUrl:{} ",
                user, excelFilePath, fileExt, importObjectApiName, importCompleteUrl, pingUrl);
        String currentApiName = getPreProcessingCurrentApiName();
        if (!importRestService.hasPreProcessingFunction(user, currentApiName, importType)) {
            log.warn("import pre processing no has function, user:{}, apiName:{}", user, currentApiName);
            return true;
        }
        excelExecutor.setImportPreProcessing(true);
        excelExecutor.setImportType(importType);
        try {
            //保存源excel文件到本地
            FileUtil.LocalFile localFile = getLocalFile();
            //创建心跳服务并定时调用
            createJob();
            //执行导入
            excelExecutor.init();
//            executeImportPreProcessing(localFile.getPath(), excelExecutor);
            executeImport(localFile.getPath());
            return true;
        } catch (NoImportPreProcessingFunctionException e) {
            // 没有配置预处理函数，跳过后续读 excel 的逻辑
            log.warn("not exit pre processing function, jobId:{}, importObjectApiName:{}", jobId, importObjectApiName, e);
            return true;
        } catch (DataLoaderBusinessException e) {
            log.warn("import is cancel pre processing, jobId:{},importObjectApiName:{}", jobId, importObjectApiName, e);
            importPreProcessingComplete(jobId, COMPLETE_CANCEL_CODE, e.getMessage());
            return false;
        } catch (ExcelReadException e) {
            log.warn("import verify read excel fail, filePath:{}, jobId:{}", excelFilePath, jobId, e);
            importPreProcessingComplete(jobId, COMPLETE_FAIL_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_READ_FILE_FAILED, "读取excel文件失败，请检查文件或者稍后重试"));//ignoreI18n
            return false;
        } catch (Exception e) {
            log.warn("Send complete by Unexpected Error, file token:{}, jobId:{} ", "", jobId, e);
            if (excelExecutor.isExcelFormatWrong()) {
                importPreProcessingComplete(jobId, COMPLETE_FAIL_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_FILE_CONTENT_ERROR, "Excel文件内容的格式不正确，请检查"));//ignoreI18n
                return false;
            } else {
                importPreProcessingComplete(jobId, COMPLETE_FAIL_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_FAIL, "失败"));//ignoreI18n
                throw e;
            }
        } finally {
            resetByPreProcessing();
        }
    }

    /**
     * 预处理完成,需要重置 batchCount 和 totalRowCount
     */
    private void resetByPreProcessing() {
        batchCount = 0;
        totalRowCount = 0;
        importBatch = 0;
    }

    private FileUtil.LocalFile getLocalFile() {
        if (Objects.nonNull(localFile)) {
            return localFile;
        }
        String newPath = execImportTaskHookBefore(excelFilePath);
        if (StringUtils.isNotEmpty(newPath)) {
            log.warn("execImportTaskHookBefore, oldPath:{}, newPath:{}", excelFilePath, newPath);
            excelFilePath = newPath;
        }
        return localFile = FileUtil.getInstance().saveFile(fileService, user, excelFilePath, fileExt);
    }

    private String getPreProcessingCurrentApiName() {
        return CollectionUtils.isEmpty(unionImportApiNameList) ? importObjectApiName : unionImportApiNameList.get(0);
    }

    public void execute() throws IOException {
        log.info("Start Import Task execute, user:{}, filePath:{}, fileExt:{}, apiName:{}, importCompleteUrl:{}, pingUrl:{} ",
                user, excelFilePath, fileExt, importObjectApiName, importCompleteUrl, pingUrl);
        String resultPath = null;
        try {
            //保存源excel文件到本地
            FileUtil.LocalFile localFile = getLocalFile();
            //创建心跳服务并定时调用
            createJob();
            excelExecutor.setImportPreProcessing(false);
            excelExecutor.setImportType(importType);
            excelExecutor.init();
            //执行导入
            executeImport(localFile.getPath());

            //上传结果
            resultPath = uploadExcel();
            //导入完成
            doComplete(resultPath);
        } catch (ExcelReadException e) {
            log.warn("import verify read excel fail, filePath:{}, jobId:{}", excelFilePath, jobId, e);
            importComplete(jobId, resultPath, COMPLETE_FAIL_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_READ_FILE_FAILED, "读取excel文件失败，请检查文件或者稍后重试"), "");//ignoreI18n
        } catch (Exception e) {
            log.error("Send complete by Unexpected Error, file token:{}, jobId:{} ", resultPath, jobId, e);
            if (excelExecutor.isExcelFormatWrong()) {
                importComplete(jobId, resultPath, COMPLETE_FAIL_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_FILE_CONTENT_ERROR, "Excel文件内容的格式不正确，请检查"), "");//ignoreI18n
            } else {
                importComplete(jobId, resultPath, COMPLETE_FAIL_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_FAIL, "失败"), "");//ignoreI18n
                throw e;
            }
        }
    }

    private String uploadExcel() throws IOException {
        String filePath = FileUtil.getInstance().uploadExcel(user, excelFilePath, fileExt, fileService, excelExecutor);
        String fileName = I18N.text(I18NKey.IMPORT_RESULT, formatter.format(LocalDate.now()));
        String newPath = execImportTaskHookAfter(filePath, fileName);
        if (StringUtils.isNotEmpty(newPath)) {
            log.warn("execImportTaskHookAfter, fileName:{}, oldPath:{}, newPath:{}", fileName, filePath, newPath);
            return newPath;
        }
        return filePath;
    }

    private void createJob() {
        job.create(() -> {
            return InvokeResult.builder()
                    .jobId(jobId)
                    .completeRowCount(doneCount)
                    .totalRowCount(totalRowCount)
                    .build();
        }, () -> {
            isCancelTask = true;
        }, () -> {
            isAbortTask = true;
            job.stopHeartBeats();
        });
    }

    private void executeImport(String path) throws IOException {
        //开始执行导入
        boolean importPreProcessing = excelExecutor.isImportPreProcessing();
        excelExecutor.execute(path, (curRow, rowInfo) -> {
            calcTotalRowCount(curRow, rowInfo);
            if (curRow == 1 && !CollectionUtils.isEmpty(unionImportApiNameList)) {
                // 联合导入，导入上一个sheet不足一批量的数据
                importLastData(getLastImportApiName(), importPreProcessing);
                return true;
            } else if (curRow == 1) {
                return true;
            }
            try {
                batchImportData(curRow, rowInfo, getCurrentImportApiName(), importPreProcessing);
            } catch (DataLoaderBusinessException e) {
                log.error("DataLoaderBusinessException,Error in handle row, rowIndex:{},rowInfo:{}", curRow, rowInfo, e);
                throw e;
            } catch (Exception e) {
                log.error("Exception Error in handle row, rowIndex:{},rowInfo:{}", curRow, rowInfo, e);
            }
            return true;
        });

        //处理最后不到一批次的数据
        importLastData(getCurrentImportApiName(), importPreProcessing);
    }

    /**
     * 联合导入，导入上一个sheet不足一批量的数据
     * <p>
     * 联合导入解析多个sheet时，读取各个sheet的第0行时，表示此时处理的apiName已经更新了,
     * 这里是导入上一个sheet的剩余部分数据(不满50条)，所以先将当前处理的apiName设置为
     * 上一个sheet的apiName，处理完之后再还原.
     *
     * @param importApiName       对象apiName
     * @param importPreProcessing 是否预处理
     */
    private void importLastData(String importApiName, boolean importPreProcessing) {
        if (CollectionUtils.isEmpty(batchData)) {
            return;
        }
        try {
            if (isAbortTask && !isCancelTask) {
                job.stopHeartBeats();
                excelExecutor.updateLastResultWhenCancel(batchData, importApiName);
            } else if (isCancelTask) {
                excelExecutor.updateLastResultWhenCancel(batchData, importApiName);
            } else {
                doImportData(importApiName, importPreProcessing, true);
            }
        } finally {
            batchData.clear();
        }
    }

    private void doComplete(String resultPath) {
        if (isAbortTask && !isCancelTask) {
            log.info("Send complete by abort, file token:{}, jobId:{} ", resultPath, jobId);
            importComplete(jobId, resultPath, COMPLETE_FAIL_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_FAIL, "失败"), "");//ignoreI18n
        } else if (isCancelTask) {
            log.info("Send complete by cancelling, file token:{}, jobId:{} ", resultPath, jobId);
            importComplete(jobId, resultPath, COMPLETE_CANCEL_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_CANCEL, "取消"), "");//ignoreI18n
        } else {
            log.info("Send complete by Success, file token:{}, jobId:{} ", resultPath, jobId);
            String fileName = I18N.text(I18NKey.IMPORT_RESULT, formatter.format(LocalDate.now()));
            importComplete(jobId, resultPath, COMPLETE_SUCCESS_CODE, I18NExt.getOrDefault(I18NKey.IMPORT_SUCCESS, "成功"), fileName);//ignoreI18n
        }
        // 使用biz-log-client包发送日志
        sendAuditLog();
    }

    /**
     * 发送日志
     */
    private void sendAuditLog() {
        AuditLogDTO dto = AuditLogDTO.builder()
                .appName(appName)
                .serverIp(serverIp)
                .profile(profile)
                .action(action)
                .ea(user.getEa())
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .objectApiNames(importObjectApiName)
                .num(doneCount)
                .build();
        BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());

    }

    private void batchImportData(int curRow, List<String> rowInfo, String currentApiName, boolean importPreProcessing) {
//        if(excelExecutor.isEmptyLine(rowInfo)){
//            // 空行不处理
//            return;
//        }
        if (isAbortTask && !isCancelTask) {
            job.stopHeartBeats();
            updateResultWhenCancel(rowInfo, curRow, currentApiName);
        } else if (isCancelTask) {
            updateResultWhenCancel(rowInfo, curRow, currentApiName);
        } else {
            if (batchCount > 0 && batchCount % batchHandleCount == 0) {
                //调用CRM服务，批量导入
                doImportData(currentApiName, importPreProcessing, false);
            }

            excelExecutor.toObjectData(rowInfo, curRow, true).ifPresent(data -> {
                //联合导入，从对象补充主对象ID
                if (!CollectionUtils.isEmpty(unionImportApiNameList) && unionImportApiNameList.contains(currentApiName)) {
                    if (isSupportFieldMapping()) {
                        Optional<DetailInfo> detailObject = detailInfo.stream()
                                .filter(x -> Objects.equals(x.getApiName(), currentApiName))
                                .findFirst();
                        if (detailObject.isPresent()) {
                            List<BaseInfo.FieldMapping> fieldMapping = detailObject.get().getFieldMapping();
                            if (!CollectionUtils.isEmpty(fieldMapping)) {
                                Optional<String> relatedMarkColIndex = fieldMapping.stream()
                                        .filter(x -> Objects.equals("RELATED_MARK", x.getImportFieldMark()))
                                        .map(BaseInfo.FieldMapping::getColIndex)
                                        .findFirst();
                                Optional<String> unionIdMarkColIndex = fieldMapping.stream()
                                        .filter(x -> Objects.equals("UNION_ID_MARK", x.getImportFieldMark()))
                                        .map(BaseInfo.FieldMapping::getColIndex)
                                        .findFirst();
                                if (relatedMarkColIndex.isPresent() && unionIdMarkColIndex.isPresent()) {
                                    Object obj = data.get(relatedMarkColIndex.get());
                                    String mark = Objects.nonNull(obj) ? (String) obj : "";
                                    if (!CollectionUtils.isEmpty(excelExecutor.masterRedisFailMap)) {
                                        String masterId = excelExecutor.masterRedisFailMap.get(mark);
                                        data.put(unionIdMarkColIndex.get(), masterId);
                                    }
                                }
                            }
                        }
                    } else {
                        Object obj = data.get(I18N.text(I18NKey.RELATED_MARK));
                        String mark = Objects.nonNull(obj) ? (String) obj : "";
                        if (!CollectionUtils.isEmpty(excelExecutor.masterRedisFailMap)) {
                            String masterId = excelExecutor.masterRedisFailMap.get(mark);
                            data.put(I18N.text(I18NKey.ASSOCIATE_OBJECT_ID), masterId);
                        }
                    }
                }
                batchCount++;
                batchData.add(data);
            });
        }
    }

    private String getCurrentImportApiName() {
        return CollectionUtils.isEmpty(unionImportApiNameList) ? importObjectApiName : excelExecutor.getCurrentApiName();
    }

    private String getLastImportApiName() {
        return CollectionUtils.isEmpty(unionImportApiNameList) ? importObjectApiName : excelExecutor.getLastApiName();
    }

    private void doImportData(String importApiName, boolean importPreProcessing, Boolean finalBatch) {
        // 在这里限速
        waitForImport();
        if (isAddAction()) {
            if (Objects.isNull(masterInfo)) {
                masterInfo = new MasterInfo();
            }
            masterInfo.setData(batchData);
            masterInfo.setApiName(importApiName);
        }
        int count = importBatch++;
        String traceId = TraceContext.get().getTraceId();
        try {
            TraceContext.get().setTraceId(String.format("%s_%s_%s", traceId, importPreProcessing ? "pre" : "data", count));
            IBulkImportRestService.BulkInsertArg bulkInsertArg = IBulkImportRestService.BulkInsertArg.builder()
                    .apiName(importApiName)
                    .importType(importType)
                    .matchingType(matchingType)
                    .isEmptyValueToUpdate(isEmptyValueToUpdate)
                    .isWorkFlowEnabled(isWorkFlowEnabled)
                    .isApprovalFlowEnabled(isApprovalFlowEnabled)
                    .isTextureImportEnabled(isTextureImportEnabled)
                    .rows(isAddAction() ? Lists.newArrayList() : batchData)
                    .user(user)
                    .unionApiNames(unionImportApiNameList)
                    .isUnionDuplicateChecking(isUnionDuplicateChecking)
                    .jobId(jobId)
                    .relatedApiNameList(relatedApiNameList)
                    .objectCode(objectCode)
                    .specifiedField(specifiedField)
                    .checkOutOwner(checkOutOwner)
                    .removeOutTeamMember(removeOutTeamMember)
                    .finalBatch(finalBatch)
                    .updateOwner(updateOwner)
                    .oldOwnerTeamMember(oldOwnerTeamMember)
                    .importPreProcessing(importPreProcessing)
                    .fileCode(IBulkImportRestService.BulkInsertArg.FileCode.of(localFile.getMd5Code(), localFile.getSha256Code()))
                    .supportFieldMapping(supportFieldMapping)
                    .masterInfo(masterInfo)
                    .detailInfo(detailInfo)
                    .headerExcelCols(excelExecutor.getHeaderExcelCols())
                    .extendAttribute(extendAttribute)
                    .parameter(IBulkImportRestService.BulkInsertArg.Parameter.builder()
                            .importType(importType)
                            .matchingType(matchingType)
                            .isEmptyValueToUpdate(isEmptyValueToUpdate)
                            .isVerify(false)
                            .isWorkFlowEnabled(isWorkFlowEnabled)
                            .isApprovalFlowEnabled(isApprovalFlowEnabled)
                            .isTextureImportEnabled(isTextureImportEnabled)
                            .operationType(getOperationType())
                            .isUnionDuplicateChecking(isUnionDuplicateChecking)
                            .unionApiNames(unionImportApiNameList)
                            .jobId(jobId)
                            .relatedApiNameList(relatedApiNameList)
                            .objectCode(objectCode)
                            .specifiedField(specifiedField)
                            .checkOutOwner(checkOutOwner)
                            .removeOutTeamMember(removeOutTeamMember)
                            .updateOwner(updateOwner)
                            .oldOwnerTeamMember(oldOwnerTeamMember)
                            .importMethod(importMethod)
                            .build())
                    .locale(locale)
                    .build();
            IBulkImportRestService.BulkInsertResult result = importRestService.bulkInsert(bulkInsertArg, batchTimeOut);
            log.info("Bulk Import data current Line:{}, importCount:{} done", doneCount, batchData.size());
            if (null == result) {
                log.warn("Bulk Import data current Line:{}, importCount:{} done, but crm return null", doneCount, batchData.size());
            }

            // 导入预处理结果
            if (importPreProcessing) {
                importPreProcessingResult(result);
                return;
            }

            //更新结果excel
            updateResultExcel(batchData, result, importApiName);
        } catch (DataLoaderBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error in bulkInsert batch data or update excel, current Line:{}, importCount:{},jobId:{}",
                    doneCount, batchData.size(), jobId, e);
        } finally {
            //不论本批数据导入是否有问题，不影响下次处理
            log.info("Bulk Import data current Line:{}, importCount:{}, update Result Excel done", doneCount, batchData.size());
            doneCount += batchData.size();
            batchData.clear();
            batchCount = 0;
            TraceContext.get().setTraceId(traceId);
        }
    }

    private String getOperationType() {
        if (isAddAction()) {
            return OperationType.ADD_INVOKE.name();
        }
        return CollectionUtils.isEmpty(unionImportApiNameList) ? OperationType.INVOKE.name() :
                OperationType.UNION_INVOKE.name();
    }

    private boolean isAddAction() {
        return ImportMethod.isAddAction(importMethod, importType);
    }

    private void importPreProcessingResult(IBulkImportRestService.BulkInsertResult result) {
        if (Objects.isNull(result)) {
            return;
        }
        if (!result.isSuccess()) {
            String errorMsg = result.getMessage();
            throw new CancelTaskException(errorMsg);
        }
        if (BooleanUtils.isFalse(result.getHasImportPreProcessingFunction())) {
            throw new NoImportPreProcessingFunctionException();
        }
    }

    private void waitForImport() {
        // doneCount 数量为 0 时(第一批导入时)立即执行导入，否则等待一段时间
        if (doneCount == 0) {
            return;
        }
        if (CollectionUtils.isEmpty(batchData)) {
            return;
        }
        GuavaLimiter.acquire("limit-crm-import-dataloader-rate", user.getTenantId(), batchData.size());
    }


    private void updateResultExcel(List<DataItem> dataList, IBulkImportRestService.BulkInsertResult result, String importApiName) {
        ExcelExecutor.UpdateExcelResult updateExcelResult;
        if (!CollectionUtils.isEmpty(unionImportApiNameList)) {
            //联合导入
            updateExcelResult = excelExecutor.updateUnionResult(dataList, result, importApiName);
        } else {
            updateExcelResult = excelExecutor.updateResultCommon(dataList, result);
        }
        successCount += updateExcelResult.getSuccessCount();
        failCount += updateExcelResult.getFailCount();
    }

    private void updateResultWhenCancel(List<String> rowInfo, int rowIndex, String apiName) {
        if (excelExecutor.isEmptyLine(rowInfo)) {
            //空行，跳过
            return;
        }
        if (CollectionUtils.isEmpty(unionImportApiNameList)) {
            //非联合导入逻辑
            excelExecutor.copyDataToAllRow(rowInfo, rowIndex);
            excelExecutor.copyDataToFailRow(rowInfo);
        } else {
            //联合导入
            excelExecutor.copyDataToUnionFailRow(rowInfo, apiName);
        }
    }

    /**
     * @param jobId
     * @param status
     * @param message
     */
    private void importPreProcessingComplete(String jobId, String status, String message) {
        InvokeResult invokeResult = InvokeResult.builder()
                .jobId(jobId)
                .code(status)
                .message(message)
                .importType(importType)
                .importObjectApiName(importObjectApiName)
                .unionImportApiNameList(unionImportApiNameList)
                .user(user)
                .build();

        job.completeJob(invokeResult);
    }

    /**
     * 导入完成调用平台接口，包括失败和成功
     *
     * @param status 0 代表成功，-1 代码失败
     */
    private void importComplete(String jobId, String filePath, String status, String message, String fileName) {
        InvokeResult invokeResult = InvokeResult.builder()
                .jobId(jobId)
                .result(filePath)
                .code(status)
                .message(message)
                .successRowCount(successCount)
                .failRowCount(failCount)
                .notImportCount(notImportCount)
                .totalRowCount(COMPLETE_CANCEL_CODE.equals(status) ? successCount + failCount : totalRowCount)
                .fileExt(fileExt)
                .fileName(fileName)
                .importType(importType)
                .importObjectApiName(importObjectApiName)
                .unionImportApiNameList(unionImportApiNameList)
                .approvalFlowEnabled(isApprovalFlowEnabled)
                .workFlowEnabled(isWorkFlowEnabled)
                .user(user)
                .fileExpiredTime(FileService.getFileExpiredTimeWithNow())
                .build();

        job.completeJob(invokeResult);
        SendResult sendResult = producer.sendMessage(jobId, JSON.toJSONBytes(invokeResult));
        log.info("importComplete, ei:{}, jobId:{}, msgId:{}", user.getTenantId(), jobId, sendResult.getMsgId());
    }
}