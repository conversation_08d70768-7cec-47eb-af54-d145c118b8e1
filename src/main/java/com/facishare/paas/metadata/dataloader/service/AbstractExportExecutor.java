package com.facishare.paas.metadata.dataloader.service;

import com.alibaba.fastjson.JSON;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.paas.metadata.dataloader.common.DataLoaderConfig;
import com.facishare.paas.metadata.dataloader.exception.ExportBusinessException;
import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportCommonDTO;
import com.facishare.paas.metadata.dataloader.task.ExportTask;
import com.facishare.paas.metadata.dataloader.util.FileUtil;
import com.facishare.paas.metadata.dataloader.util.SpringContextUtil;
import com.facishare.paas.token.api.ITokenService;
import com.facishare.paas.token.model.TokenInfo;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.rmi.server.ExportException;
import java.util.List;
import java.util.Optional;

@Slf4j
public abstract class AbstractExportExecutor {

    public static final String ERROR_SPLIT = "$$";
    public final int tokenExpireSeconds = 3600;
    protected int totalCount = 0;
    protected int currentCount = 0;
    public int dataBatchSize;
    protected String fileName;
    protected String token;
    protected int excelColumnWidth;

    protected boolean isGenerating = false;
    protected final IExportDataService exportDataService;
    protected final FileService fileService;
    protected final ITokenService tokenService;
    protected final Context.ExportCommonContext context;
    protected Arg arg;
    protected List<ExportCommonDTO.Header> exportHeaders = Lists.newArrayList();
    protected List<ExportCommonDTO.GroupHeader> groupHeaders = Lists.newArrayList();

    public AbstractExportExecutor(Context.ExportCommonContext context, Arg arg) {
        this.arg = arg;
        this.context = context;
        tokenService = SpringContextUtil.getContext().getBean(ITokenService.class);
        exportDataService = SpringContextUtil.getContext().getBean(IExportDataService.class);
        fileService = SpringContextUtil.getContext().getBean(FileService.class);
        dataBatchSize = DataLoaderConfig.INSTANCE.getExportBizBatchSize(arg.getExportBizType());
        excelColumnWidth = DataLoaderConfig.INSTANCE.getExcelColumnWidth();
    }

    public ExportCommonDTO.Result executeExport() {
        beforeExport();
        ExportTask.createTask(context.getJobId(), token, totalCount, this::startExport);
        return ExportCommonDTO.Result.builder()
                .data(ExportCommonDTO.ExportCommon.builder()
                        .ext(arg.getFileExt())
                        .jobId(context.getJobId())
                        .totalCount(totalCount)
                        .build())
                .build();
    }

    private void beforeExport() {
        generateToken();
        generateFileName();
        handleExportHeader();
    }

    private void generateToken() {
        token = tokenService.create(tokenExpireSeconds);
    }

    private void generateFileName() {
        String filePath = getFilePath();
        fileName = FileUtil.getInstance().genFileName(filePath, context.getFileExt());
    }

    private String getFilePath() {
        return context.getExportBizType() + context.getJobId();
    }

    protected void handleExportHeader() {
        ExportCommonDTO.Arg headerArg = ExportCommonDTO.Arg.builder()
                .jobId(context.getJobId())
                .searchQuery(arg.getSearchQuery())
                .exportBizType(arg.getExportBizType())
                .build();
        ExportCommonDTO.ExportHeader header = exportDataService.findExportHeader(headerArg, context);
        exportHeaders = header.getExportHeaders();
        groupHeaders = header.getGroupHeaders();
        totalCount = header.getTotalCount();
    }

    private ExportCommonDTO.Result startExport() {
        if (!isGenerating) {
            isGenerating = true;
            String traceId = TraceContext.get().getTraceId();
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
            parallelTask.submit(() -> {
                try {
                    doExport();
                    completeExport();
                } catch (ExportException e) {
                    log.warn("export data failed!tenantId:{},arg:{}", context.getTenantId(), JSON.toJSONString(arg), e);
                    TokenInfo tokenInfo = TokenInfo.buildError(token, "srcTrace:" + traceId + ERROR_SPLIT + e.getMessage());
                    tokenService.complete(tokenInfo, tokenExpireSeconds);
                } catch (Exception e) {
                    log.error("export data error!tenantId:{},arg:{}", context.getTenantId(), JSON.toJSONString(arg), e);
                    TokenInfo tokenInfo = TokenInfo.buildError(token, "srcTrace:" + traceId);
                    tokenService.complete(tokenInfo, tokenExpireSeconds);
                }
            });
            parallelTask.run();
        }
        return generateResult(token);
    }

    protected abstract void doExport() throws IOException;

    private void completeExport() throws IOException {
        FileUtil.LocalFile result = uploadFile();
        TokenInfo tokenInfo = TokenInfo.buildSuccess(token, result.getPath() + "|" + result.getSize() + "|" + FileService.getFileExpiredTimeWithNow());
        tokenService.complete(tokenInfo, tokenExpireSeconds);
        log.info("export data success,jobId:{},tenantId:{},tokeInfo:{}", context.getJobId(), context.getTenantId(), JSON.toJSONString(tokenInfo));
    }

    protected abstract FileUtil.LocalFile uploadFile() throws IOException;

    private ExportCommonDTO.Result generateResult(String token) {
        TokenInfo tokenInfo = tokenService.query(token);
        if (tokenInfo == null) {
            throw new ExportBusinessException("token not exist!");
        }
        int num = Optional.ofNullable(tokenInfo.getProgress()).orElse(0);
        if (tokenInfo.isOngoing()) {
            return ExportCommonDTO.Result.builder().data(
                    ExportCommonDTO.ExportCommon.builder().ext(arg.getFileExt()).token(token).path("")
                            .fileName("").totalCount(totalCount).currentCount(num).build()).build();
        }

        if (tokenInfo.isSuccess()) {
            String pathAndSize = tokenInfo.getResult();
            String[] arr = pathAndSize.split("\\|");
            Long fileExpiredTime = null;
            if (arr.length > 2) {
                fileExpiredTime = Long.parseLong(arr[2]);
            }
            return ExportCommonDTO.Result.builder()
                    .data(ExportCommonDTO.ExportCommon.builder()
                            .ext(arg.getFileExt())
                            .token(token)
                            .path(arr[0])
                            .size(Long.parseLong(arr[1]))
                            .fileName(arr[0])
                            .totalCount(totalCount)
                            .fileExpiredTime(fileExpiredTime)
                            .currentCount(num).build()).build();
        }
        if (tokenInfo.isError()) {
            String errorMsg = tokenInfo.getMessage();
            if (StringUtils.isNotBlank(errorMsg) && errorMsg.contains(ERROR_SPLIT)) {
                String message = StringUtils.substringAfter(errorMsg, ERROR_SPLIT);
                throw new ExportBusinessException(message);
            }
            throw new ExportBusinessException("export data failed! " + errorMsg);
        }
        throw new ExportBusinessException("export data failed!");
    }

    @Data
    static class Arg {
        private String fileExt;
        private String searchQuery;
        private String fileFormat;
        private String exportBizType;
    }
}
