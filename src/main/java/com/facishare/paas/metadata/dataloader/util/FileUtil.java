package com.facishare.paas.metadata.dataloader.util;

import com.alibaba.fastjson.JSON;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NGetFileMetaData;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.service.FileService;
import com.facishare.paas.metadata.dataloader.task.ExcelExecutor;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.Objects;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2016/12/30.
 */

@Slf4j
public class FileUtil {
    private FileUtil() {
    }

    public static FileUtil getInstance() {
        return FileUtilHolder.instance;
    }

    private static class FileUtilHolder {
        private final static FileUtil instance = new FileUtil();
    }

    public void deleteTempFile(String tmpFileName) {
        if (Strings.isNullOrEmpty(tmpFileName)) {
            return;
        }
        int index = tmpFileName.lastIndexOf(".");
        if (index < 0) {
            return;
        }
        String suffix = tmpFileName.substring(index + 1);
        if (!ExcelUtil.EXCEL2003.equalsIgnoreCase(suffix)
                && !ExcelUtil.EXCEL2007.equalsIgnoreCase(suffix)
                && !Objects.equals("csv", suffix)) {
            return;
        }
        try {
            File file = new File(tmpFileName);
            if (file.exists() && file.isFile()) {
                file.delete();
            }
        } catch (Exception e) {
            log.error("Error in deleting TempFile, fileName:{}", tmpFileName, e);
        }
    }

    public LocalFile saveFile(FileService fileService, User user, String excelFilePath, String fileExt) {
        byte[] fileData;
        FileOutputStream fileOutputStream = null;
        String fileName = null;
        try {
            fileData = fileService.getFileData(excelFilePath, user);
            fileName = genFileName(excelFilePath, fileExt);
            fileOutputStream = new FileOutputStream(fileName);
            fileOutputStream.write(fileData);
            fileOutputStream.close();
            NGetFileMetaData.Result fileMetaData = fileService.getFileMetaData(user, excelFilePath);
            return LocalFile.builder()
                    .path(fileName)
                    .size(fileData.length)
                    .md5Code(fileMetaData.getCode())
                    .sha256Code(fileMetaData.getSha256())
                    .build();
        } catch (Exception e) {
            log.error("Error in Save source temp file", e);
        } finally {
            if (null != fileOutputStream) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    log.error("Error in closing FileOutputStream for save source temp file", e);
                }
            }

        }
        return LocalFile.builder().path(fileName).size(0).build();
    }

    public String genFileName(String filePath, String fileExt) {
        String basePath = getTempFileBasePath();
        return String.format("%ssourceTemp/%s.%s", basePath, filePath, fileExt);
    }

    public String getResultFilePath(String filePath, String fileExt) {
        String basePath = getTempFileBasePath();
        return String.format("%sresultTemp/%s.%s", basePath, filePath, fileExt);
    }

    public String uploadExcel(User user, String excelFilePath, String fileExt, FileService fileService, ExcelExecutor excelExecutor) throws IOException {
        String fileName = getResultFilePath(excelFilePath, fileExt);
        InputStream inputStream = null;
        try {
            excelExecutor.writeToLocalFile(fileName);
            inputStream = getInputStreamForResult(fileName);
            //上传
            String resultPath = fileService.uploadFile(user, inputStream, fileExt).getPath();
            log.info("Upload excel result done, file token:{}", resultPath);
            return resultPath;
        } catch (Exception e) {
            log.error("Upload result excel fail", e);
            throw new IOException(e.getMessage(), e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error("Error in closing stream when uploading excel", e);
            }
            deleteTempFile(fileName);
        }
    }

    public LocalFile uploadExcel(User user, String fileName, String fileExt, FileService fileService, Workbook workbook) throws IOException {
        writeToLocalFile(fileName, workbook);
        return uploadFile(user, fileName, fileExt, fileService);
    }

    private void writeToLocalFile(String fileName, Workbook workbook) {
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(fileName);
            workbook.write(outputStream);
            outputStream.flush();
        } catch (IOException e) {
            log.error("Error in write result file to disk", e);
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                log.error("Error in closing stream when uploading excel", e);
            }
        }
    }

    private InputStream getInputStreamForResult(String fileName) throws IOException {
        InputStream inputStream;
        try {
            inputStream = new FileInputStream(fileName);
        } catch (IOException e) {
            log.error("Error in turn result excel to inputStream,", e);
            throw e;
        }
        return inputStream;
    }

    public LocalFile uploadFile(User user, String fileName, String fileExt, FileService fileService) throws IOException {
        InputStream inputStream = null;
        try {
            inputStream = getInputStreamForResult(fileName);
            //上传
            LocalFile localFile = fileService.uploadFile(user, inputStream, fileExt);
            log.info("upload file result done, localFile:{}", JSON.toJSONString(localFile));
            return localFile;
        } catch (Exception e) {
            log.error("upload file result fail", e);
            throw new IOException(e.getMessage(), e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error("Error in closing stream when uploading file", e);
            }
            deleteTempFile(fileName);
        }

    }

    private String getTempFileBasePath() {
        URL url = this.getClass().getResource("/");
        if (url == null) {
            log.error("Can not find SourceTemp folder");
            return null;
        }

        String basePath;
        try {
            basePath = url.toURI().getPath();
        } catch (URISyntaxException e) {
            log.error("Error in get temp file path", e);
            return null;
        }
        return basePath;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LocalFile {
        private String path;
        private long size;
        private String md5Code;
        private String sha256Code;

        public boolean exist() {
            return !Strings.isNullOrEmpty(path) && size > 0;
        }

        public void deleteTempFile() {
            if (Strings.isNullOrEmpty(path)) {
                return;
            }
            FileUtil.getInstance().deleteTempFile(path);
        }
    }
}
