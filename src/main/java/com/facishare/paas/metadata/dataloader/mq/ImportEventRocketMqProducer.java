package com.facishare.paas.metadata.dataloader.mq;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/07/22
 */
@Slf4j
@Component
public class ImportEventRocketMqProducer {
    public static final String CONFIG = "fs-paas-import-event-producer";
    private AutoConfMQProducer producer;

    private ImportEventRocketMqProducer() {
    }

    @PostConstruct
    public void init() {
        producer = new AutoConfMQProducer(CONFIG);
    }

    @PreDestroy
    public void close() {
        producer.close();
    }

    public SendResult sendMessage(String key, byte[] messageData) {
        String defaultTopic = producer.getDefaultTopic();
        Message message = new Message(defaultTopic, "", key, messageData);
        return producer.send(message);
    }
}
