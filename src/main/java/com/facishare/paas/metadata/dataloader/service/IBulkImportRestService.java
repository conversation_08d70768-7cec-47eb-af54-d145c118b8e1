package com.facishare.paas.metadata.dataloader.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.paas.metadata.dataloader.model.*;
import com.facishare.paas.metadata.dataloader.rest.dto.OldOwnerTeamMember;
import com.facishare.paas.metadata.dataloader.util.I18NExt;
import com.facishare.paas.metadata.dataloader.util.I18NKey;
import com.google.common.collect.Lists;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by tidus on 2016/11/9.
 */
public interface IBulkImportRestService {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class BulkInsertArg {
        String apiName;
        User user;
        String locale;
        @JSONField(name = "ImportType")
        int importType;
        @JSONField(name = "MatchingType")
        int matchingType;
        @JSONField(name = "IsEmptyValueToUpdate")
        boolean isEmptyValueToUpdate;
        @JSONField(name = "IsWorkFlowEnabled")
        boolean isWorkFlowEnabled;
        @JSONField(name = "IsApprovalFlowEnabled")
        boolean isApprovalFlowEnabled;
        @JSONField(name = "IsTextureImportEnabled")
        boolean isTextureImportEnabled;
        @JSONField(name = "IsUnionDuplicateChecking")
        boolean isUnionDuplicateChecking;
        @JSONField(name = "unionApiNames")
        List<String> unionApiNames;
        @JSONField(name = "jobId")
        String jobId;
        @JSONField(name = "relatedApiNameList")
        private List<String> relatedApiNameList;
        @JSONField(name = "objectCode")
        String objectCode;
        @JSONField(name = "specifiedField")
        String specifiedField; //指定字段
        @JSONField(name = "IsCheckOutOwner")
        private boolean checkOutOwner;
        @JSONField(name = "IsRemoveOutTeamMember")
        private boolean removeOutTeamMember;
        @JSONField(name = "IsImportPreProcessing")
        private Boolean importPreProcessing;
        @JSONField(name = "IsFinalBatch")
        private Boolean finalBatch;
        @JSONField(name = "IsUpdateOwner")
        private Boolean updateOwner;
        @JSONField(name = "oldOwnerTeamMember")
        private OldOwnerTeamMember oldOwnerTeamMember;
        @JSONField(name = "fileCode")
        private FileCode fileCode;
        @JSONField(name = "masterInfo")
        private MasterInfo masterInfo;
        @JSONField(name = "detailInfo")
        private List<DetailInfo> detailInfo;
        @JSONField(name = "supportFieldMapping")
        private Boolean supportFieldMapping;
        private Map<String, Object> extendAttribute;
        Parameter parameter;
        List<DataItem> rows;
        List<ExcelCol> headerExcelCols;

        public String toJSON() {
            return JSON.toJSONString(this, SerializerFeature.DisableCircularReferenceDetect);
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Parameter {
            @JSONField(name = "ImportType")
            int importType;
            @JSONField(name = "MatchingType")
            int matchingType;
            @JSONField(name = "isVerify")
            boolean isVerify;
            @JSONField(name = "operationType")
            String operationType;
            @JSONField(name = "IsEmptyValueToUpdate")
            boolean isEmptyValueToUpdate;
            @JSONField(name = "IsWorkFlowEnabled")
            boolean isWorkFlowEnabled;
            @JSONField(name = "IsApprovalFlowEnabled")
            boolean isApprovalFlowEnabled;
            @JSONField(name = "IsTextureImportEnabled")
            boolean isTextureImportEnabled;
            @JSONField(name = "IsUnionDuplicateChecking")
            boolean isUnionDuplicateChecking;
            @JSONField(name = "unionApiNames")
            List<String> unionApiNames;
            @JSONField(name = "jobId")
            String jobId;
            @JSONField(name = "relatedApiNameList")
            private List<String> relatedApiNameList;
            @JSONField(name = "objectCode")
            String objectCode;
            @JSONField(name = "specifiedField")
            String specifiedField; //指定字段
            @JSONField(name = "IsCheckOutOwner")
            private boolean checkOutOwner;
            @JSONField(name = "IsRemoveOutTeamMember")
            private boolean removeOutTeamMember;
            @JSONField(name = "IsUpdateOwner")
            private Boolean updateOwner;
            @JSONField(name = "oldOwnerTeamMember")
            private OldOwnerTeamMember oldOwnerTeamMember;
            @JSONField(name = "importMethod")
            private String importMethod;
        }

        @Getter
        @ToString
        @AllArgsConstructor(staticName = "of")
        public static class FileCode {
            private String md5;
            private String sha256;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class BulkInsertResultExt {
        Integer errCode;
        String errMessage;
        BulkInsertResult result;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class BulkInsertResult {
        boolean success;
        String message;
        int errorCode;
        Value value;
        private Boolean hasImportPreProcessingFunction;

        public static BulkInsertResult emptyResult() {
            Value value = Value.builder()
                    .importSucceedCount(0)
                    .rowErrorList(Lists.newArrayList())
                    .build();
            return builder().success(true)
                    .errorCode(0)
                    .value(value)
                    .build();
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Value {
            @JSONField(name = "ImportSucceedCount")
            int importSucceedCount;

            @JSONField(name = "RowErrorList")
            List<RowError> rowErrorList;

            public static List<RowError> initUnexpectedErrorList(BulkInsertArg arg) {
                String errorMessage = I18NExt.getOrDefault(I18NKey.IMPORT_NULL_ERROR, "发生未知异常，请手动检查是否导入成功");//ignoreI18n
                if (ImportMethod.isAddAction(arg.getParameter().getImportMethod(), arg.getImportType())) {
                    List<RowError> errors = Lists.newArrayList();
                    MasterInfo masterInfo = arg.getMasterInfo();
                    List<DataItem> dataItems = masterInfo.getData();
                    for (DataItem dataItem : dataItems) {
                        RowError error = RowError.builder()
                                .rowNo(dataItem.getRowNo())
                                .errorMessage(errorMessage)
                                .objectApiName(masterInfo.getApiName())
                                .build();
                        errors.add(error);
                    }
                    List<DetailInfo> detailInfo = arg.getDetailInfo();
                    if (CollectionUtils.isEmpty(detailInfo)) {
                        return errors;
                    }
                    for (DetailInfo info : detailInfo) {
                        List<DataItem> dataList = info.getDataList();
                        for (DataItem dataItem : dataList) {
                            RowError rowError = RowError.builder()
                                    .rowNo(dataItem.getRowNo())
                                    .errorMessage(errorMessage)
                                    .objectApiName(info.getApiName())
                                    .build();
                            errors.add(rowError);
                        }
                    }
                    return errors;
                }
                return arg.getRows().stream()
                        .filter(a -> Objects.nonNull(a.getRowNo()))
                        .map(a -> RowError.builder()
                                .rowNo(a.getRowNo())
                                .errorMessage(errorMessage)
                                .build())
                        .collect(Collectors.toList());
            }
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class RowError {
            @JSONField(name = "RowNo")
            int rowNo;
            @JSONField(name = "ErrorMessage")
            String errorMessage;
            @JSONField(name = "MasterId")
            String masterId;
            @JSONField(name = "AssociateMark")
            String associateMark;
            @JSONField(name = "objectApiName")
            String objectApiName;
        }
    }

    BulkInsertResult bulkInsert(BulkInsertArg arg, int batchTimeOut);

    /**
     * 当前企业的指定对象，有没有配置预处理函数
     *
     * @param user
     * @param apiName
     * @param importType
     * @return
     */
    boolean hasPreProcessingFunction(User user, String apiName, int importType);

    String findImportMethod(User user, String apiName);
}
