package com.facishare.paas.metadata.dataloader.rest.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019-06-10 11:18
 */
public interface BaseDTO {

    @Data
    abstract class CrmResult {
        @SerializedName("code")
        protected int code;
        @SerializedName("message")
        protected String message;

        public boolean hasBusinessError() {
//            return 320_002_500 == code;
            return (0 != code) && (200 != code);
        }
    }
}
