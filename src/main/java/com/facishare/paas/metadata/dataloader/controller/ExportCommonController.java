package com.facishare.paas.metadata.dataloader.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.filter.ContextManager;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.service.IExportCommonService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Slf4j
@Controller
@Path("/exportCommon")
@Consumes({"application/json"})
@Produces({"application/json"})
public class ExportCommonController {

    @Autowired
    private IExportCommonService exportCommonService;

    @Path("/verify")
    @POST
    public RestResult verify(String json) {
        log.info("Start common verify export task, paramJson:{}", json);
        ExportCommonController.Arg arg = JSONObject.parseObject(json, ExportCommonController.Arg.class);
        Context.ExportCommonContext context = (Context.ExportCommonContext) ContextManager.getContext();
        initContext(context, arg);
        IExportCommonService.VerifyResult verifyResult = exportCommonService.verify(context);
        log.info("Finish common verify export task, {}", verifyResult);
        return RestResult.builder().code(verifyResult.getCode()).message(verifyResult.getMessage()).build();
    }

    @Path(value = "/invoke")
    @POST
    public RestResult invoke(String json) {
        log.info("Start common export data task, paramJson:{}", json);
        ExportCommonController.Arg arg = JSONObject.parseObject(json, ExportCommonController.Arg.class);
        Context.ExportCommonContext context = (Context.ExportCommonContext) ContextManager.getContext();
        initContext(context, arg);
        IExportCommonService.ExportResult exportResult = exportCommonService.export(context);
        log.info("Finish common export data task, exportResult:{}", exportResult);
        return RestResult.builder().code(exportResult.getCode()).message(exportResult.getMessage()).build();
    }

    private void initContext(Context.ExportCommonContext context, ExportCommonController.Arg arg) {
        // 设置基本参数
        User user = context.getUser();
        user.setEa(arg.getEa());
        if (StringUtils.isNotBlank(arg.getLocale())) {
            context.setLocale(arg.getLocale());
        }
        if (StringUtils.isNotBlank(arg.getTimezone())) {
            user.setTimezone(arg.getTimezone());
        }
        context.setJobId(arg.getJobId());
        context.setExportBizType(arg.getExportBizType());
        context.setSearchQuery(arg.getSearchQuery());
        context.setFileFormat(arg.getFileFormat());
        context.setFileExt(arg.getFileExt());
    }


    @Data
    public static class Arg {

        @JSONField(name = "userId", alternateNames = "_userId")
        private String userId;

        @JSONField(name = "jobId", alternateNames = "_jobId")
        private String jobId;

        @JSONField(name = "ea", alternateNames = "_ea")
        private String ea;

        @JSONField(name = "ei", alternateNames = "_ei")
        private String ei;

        @JSONField(name = "X-fs-Locale", alternateNames = "_X-fs-Locale")
        private String locale = "zh-CN";

        @JSONField(name = "timezone", alternateNames = "_timezone")
        private String timezone;

        @JSONField(name = "searchQuery", alternateNames = "_searchQuery")
        private String searchQuery;

        @JSONField(name = "exportBizType", alternateNames = "_exportBizType")
        private String exportBizType;

        @JSONField(name = "fileFormat", alternateNames = "_fileFormat")
        private String fileFormat;

        @JSONField(name = "fileExt", alternateNames = "_fileExt")
        private String fileExt;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class RestResult {
        private int code;
        private String message;
    }
}
