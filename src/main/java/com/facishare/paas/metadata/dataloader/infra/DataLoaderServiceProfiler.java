package com.facishare.paas.metadata.dataloader.infra;

import com.facishare.paas.metadata.dataloader.exception.DataLoaderBusinessException;
import com.facishare.paas.metadata.dataloader.exception.NoNeedParseExcelException;
import com.facishare.paas.metadata.dataloader.exception.StopParseExcelException;
import com.github.trace.aop.ServiceProfiler;
import org.apache.poi.EncryptedDocumentException;

import java.net.SocketTimeoutException;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/3/2.
 */
public class DataLoaderServiceProfiler extends ServiceProfiler {
    @Override
    protected boolean isFail(Throwable e) {
        if (e == null) {
            return false;
        }

        return !(e instanceof NoNeedParseExcelException) && !(e instanceof StopParseExcelException)
                && !(e instanceof SocketTimeoutException) && !(e instanceof EncryptedDocumentException)
                && !(e instanceof DataLoaderBusinessException);
    }
}
