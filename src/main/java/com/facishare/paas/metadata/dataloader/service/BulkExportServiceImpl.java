package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.common.Config;
import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.rest.Common;
import com.facishare.paas.metadata.dataloader.rest.ExportRestProxy;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportDataDTO;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportVerifyDTO;
import com.facishare.paas.metadata.dataloader.task.ExportTask;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-06-05 09:56
 */
@Slf4j
@Service
public class BulkExportServiceImpl implements IBulkExportService {
    @Autowired
    private ExportRestProxy exportRestProxy;

    @Override
    public VerifyResult verify(Context.ExportContext context) {
        VerifyResult result = null;
        try {
            // verify时不需要特殊处理，直接调用导入导出服务的action即可
            ExportVerifyDTO.Arg arg = ExportVerifyDTO.Arg.builder()
                    .apiName(context.getObjectDescribeApiName())
                    .dataIdList(context.getDataIdList())
                    .recordTypeApiName(context.getRecordTypeApiName())
                    .searchQueryInfo(context.getSearchQueryInfo())
                    .searchTemplateId(context.getSearchTemplateId())
                    .otherNameList(context.getOtherNameList())
                    .detailArg(context.getDetailArg())
                    .fileFields(context.getFileFields())
                    .whatApiName(context.getWhatApiName())
                    .build();
            Map<String, String> header = Config.makeHeader(context);
            ExportVerifyDTO.Result restResult = exportRestProxy.verifyExport(arg, header,
                    context.getObjectDescribeApiName());
            // 将错误码统一为0或者-1，方便前端处理
            int code = restResult.getCode() == Common.SUCCESS ? Common.SUCCESS : Common.FAIL;
            result = VerifyResult.builder().code(code).message(restResult.getMessage()).build();
        } catch (Exception e) {
            log.error("Error in export verify, tenantId:{}, ea:{},userId:{}, apiName:{}",
                    context.getUser().getTenantId(),
                    context.getUser().getEa(),
                    context.getUser().getUserId(),
                    context.getObjectDescribeApiName(),
                    e);
            return VerifyResult.builder().code(Common.FAIL).message("System Error!").build();
        }
        return result;
    }

    @Override
    public ExportResult export(Context.ExportContext context) {
        // 构造导出数据请求参数
        ExportDataDTO.Arg arg = ExportDataDTO.Arg.builder()
                .apiName(context.getObjectDescribeApiName())
                .accordingToList(context.getAccordingToList())
                .dataIdList(context.getDataIdList())
                .includeId(context.getIncludeId())
                .isIgnoreSceneFilter(context.getIsIgnoreSceneFilter())
                .otherNameList(context.getOtherNameList())
                .recordTypeApiName(context.getRecordTypeApiName())
                .searchQueryInfo(context.getSearchQueryInfo())
                .searchTemplateId(context.getSearchTemplateId())
                .searchTemplateType(context.getSearchTemplateType())
                .isIgnoreSceneRecordType(context.getIsIgnoreSceneRecordType())
                .isNoExportRelevantTeam(context.getIsNoExportRelevantTeam())
                .jobId(context.getJobId())
                .detailArg(context.getDetailArg())
                .accordingFieldList(context.getFieldApiNames())
                .fileFields(context.getFileFields())
                .whatApiName(context.getWhatApiName())
                .build();
        Map<String, String> header = Config.makeHeader(context);
        // 第一次调用导出Action，获取token和导出数据的总数量
        ExportDataDTO.Result result;
        try {
            result = exportRestProxy.exportData(arg, header, context.getObjectDescribeApiName());
        } catch (Exception e) {
            log.error("Error in export data, tenantId:{}, ea:{},userId:{}, apiName:{}",
                    context.getUser().getTenantId(),
                    context.getUser().getEa(),
                    context.getUser().getUserId(),
                    context.getObjectDescribeApiName(),
                    e);
            return ExportResult.builder().code(Common.FAIL).message("System Error!").build();
        }
        if (result.getCode() != Common.SUCCESS) {
            log.warn("First invoke export action failed. arg:{}, result:{}", arg, result);
            return ExportResult.builder().code(Common.FAIL).message(result.getMessage()).build();
        }
        if (Strings.isNullOrEmpty(result.getData().getToken())) {
            // 未获取到token，失败
            log.warn("First invoke export action failed. No token, arg:{}, result:{}", arg, result);
            return ExportResult.builder().code(Common.FAIL).message(result.getMessage()).build();
        }
        // 创建导出任务 ExportTask 实例。
        // 在新线程中定时通过Rest接口调用action。
        // 第一次调用导出才会返回totalCount
        // 第一次调用完成后，将返回的token设置入arg中进行之后的调用
        ExportDataDTO.Arg tokenArg = ExportDataDTO.Arg.fromToken(result.getData().getToken(), context.getJobId());
        String apiName = context.getObjectDescribeApiName();
        ExportTask.createTask(context.getJobId(), result.getData().getToken(), result.getData().getTotalCount(),
                () -> exportRestProxy.exportData(tokenArg, header, apiName));
        return ExportResult.builder().code(Common.SUCCESS).message("ok").build();
    }
}
