package com.facishare.paas.metadata.dataloader.rest.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Delegate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/08/08
 */
public interface ExportExcelTemplate {

    @Data
    @Builder
    class Arg {
        @SerializedName(value = "object_describe_api_name")
        private String describeApiName;
        @SerializedName(value = "dataIdList")
        private List<String> dataIdList;

        @SerializedName(value = "search_template_id")
        private String searchTemplateId;

        @SerializedName(value = "search_query_info")
        private String searchQueryInfo;

        @SerializedName(value = "search_template_type")
        private String searchTemplateType;

        @SerializedName("ignore_scene_filter")
        private Boolean isIgnoreSceneFilter;

        @SerializedName("ignore_scene_record_type")
        private Boolean isIgnoreSceneRecordType;
        @SerializedName("print_template_id")
        private String printTemplateId;


        /**
         * 任务 token 用于异步导出
         */
        private String token;

        private String jobId;
    }

    @Data
    class Result extends BaseDTO.CrmResult implements ExportResult {

        @Delegate
        @SerializedName("data")
        private ExportToLocal data;
    }

    @Data
    class ExportToLocal {
        private String token;

        private String ext;

        private String path;

        @SerializedName("total_count")
        private int totalCount;

        private int currentCount;

        @SerializedName("export_type")
        private String exportType;

        @SerializedName("file_name")
        private String fileName;

        private long size;

        private Long fileExpiredTime;
    }
}
