package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.filter.Context;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


public interface IExportCommonService {

    /**
     * @param exportContext 导出上下文环境，包含各种参数。
     * @return 校验结果。
     */
    VerifyResult verify(Context.ExportCommonContext exportContext);

    /**
     * 导出数据
     * 1. 主线程定时调用导出的Action，记录全部导出数量，并根据结果更新导出数量;
     * 2. 另一个线程定时ping任务中心，上报导出进度;
     * 3. 导出完毕后调用任务中心complete.
     *
     * @param exportContext 导出上下文环境，包含各种参数。
     */
    ExportResult export(Context.ExportCommonContext exportContext);

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class VerifyResult {
        private Integer code;
        private String message;

        public String toString() {
            return "VerifyResult:[code:" + code + ", message:" + message + "]";
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ExportResult {
        private Integer code;
        private String message;

        public String toString() {
            return "ExportResult:[code:" + code + ", message:" + message + "]";
        }
    }
}
