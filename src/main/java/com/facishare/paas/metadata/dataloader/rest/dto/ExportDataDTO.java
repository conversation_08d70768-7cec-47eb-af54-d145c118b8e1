package com.facishare.paas.metadata.dataloader.rest.dto;

import com.google.common.base.Strings;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Delegate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-06-10 11:37
 */
public interface ExportDataDTO {

    @Data
    class Result extends BaseDTO.CrmResult implements ExportResult {
        @Delegate
        @SerializedName("data")
        private ExportData data;

        @Data
        public static class ExportData {
            @SerializedName("ext")
            private String ext;
            @SerializedName("file_name")
            private String fileName;
            @SerializedName("path")
            private String path;
            @SerializedName("size")
            private long size;
            @SerializedName("token")
            private String token;
            @SerializedName("total_count")
            private int totalCount;
            @SerializedName("currentCount")
            private int currentCount;
            @SerializedName("export_type")
            private String exportType;
            private Long fileExpiredTime;
        }

    }


    @Data
    @Builder
    class Arg {
        @SerializedName("object_describe_api_name")
        private String apiName;
        @SerializedName("dataIdList")
        private List<String> dataIdList;
        @SerializedName("recordType_apiName")
        private String recordTypeApiName;
        @SerializedName("search_template_id")
        private String searchTemplateId;
        @SerializedName("search_query_info")
        private String searchQueryInfo;
        @SerializedName("other_name_list")
        private List<String> otherNameList;
        @SerializedName("include_id")
        private Boolean includeId;
        @SerializedName("ignore_scene_filter")
        private Boolean isIgnoreSceneFilter;
        @SerializedName("ignore_scene_record_type")
        private Boolean isIgnoreSceneRecordType;
        @SerializedName("search_template_type")
        private String searchTemplateType;
        @SerializedName("according_to_list")
        private Boolean accordingToList;
        @SerializedName("jobId")
        private String jobId;
        @SerializedName("token")
        private String token;
        @SerializedName("no_export_relevant_team")
        private Boolean isNoExportRelevantTeam;
        @SerializedName("detail_arg")
        private DetailArg detailArg;
        @SerializedName("according_field_list")
        private List<String> accordingFieldList;
        @SerializedName("file_fields")
        private List<String> fileFields;
        @SerializedName("what_api_name")
        private String whatApiName;

        public static Arg fromToken(String token, String jobId) {
            if (Strings.isNullOrEmpty(token)) {
                throw new IllegalArgumentException("token");
            }
            return Arg.builder().token(token).jobId(jobId).build();
        }
    }
}
