package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportCommonDTO;

public interface IExportDataService {
    ExportCommonDTO.ExportData findExportData(ExportCommonDTO.Arg arg, Context.ExportCommonContext context);

    ExportCommonDTO.ExportHeader findExportHeader(ExportCommonDTO.Arg arg, Context.ExportCommonContext context);
}
