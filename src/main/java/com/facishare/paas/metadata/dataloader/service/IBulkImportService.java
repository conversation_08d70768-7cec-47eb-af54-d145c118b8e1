package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.model.DetailInfo;
import com.facishare.paas.metadata.dataloader.model.MasterInfo;
import com.facishare.paas.metadata.dataloader.rest.dto.OldOwnerTeamMember;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 批量导入服务
 * <p>
 * Created by tidus on 2016/11/8.
 */
public interface IBulkImportService {
    /**
     * 校验导入请求合法性
     *
     * @param arg 参数
     * @return 是否合法
     */
    VerifyResult verifyData(VerifyDataArg arg) throws IOException;

    /**
     * 导入数据
     *
     * @param arg 参数
     */
    void importData(ImportDataArg arg, Context context) throws IOException;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class VerifyDataArg {
        private String excelFilePath; //excel文件路径
        private String fileExt; //后缀名
        private String importObjectApiName; //导入对象
        private List<String> unionImportApiNameList;  //联合导入对象列表
        private String tenantId; //ei
        private String ea;
        private String user; //userID
        private String specifiedField; //指定字段
        private int importType;
        private int matchingType;
        private boolean isEmptyValueToUpdate;
        private boolean isWorkFlowEnabled;
        private boolean isApprovalFlowEnabled;
        private boolean isTextureImportEnabled;
        private boolean isNoBatch;
        private boolean isUnionDuplicateChecking;
        private String locale;
        private String objectCode;
        private List<String> relatedApiNameList;
        private boolean updateOwner;
        private boolean supportFieldMapping;
        private MasterInfo masterInfo;
        private List<DetailInfo> detailInfo;
        private Map<String, Object> extendAttribute;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class VerifyResult {
        private int code;
        private String message;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ImportDataArg {
        private String excelFilePath; //excel文件路径
        private String fileExt; //后缀名
        private String importObjectApiName; //导入对象
        private List<String> unionImportApiNameList;
        private String tenantId; //ei
        private String ea;
        private String user; //userID
        private String specifiedField; //指定字段
        private boolean isEmptyValueToUpdate;
        private int importType;
        private int matchingType;
        private String jobId;
        private boolean isWorkFlowEnabled;
        private boolean isApprovalFlowEnabled;
        private boolean isTextureImportEnabled;
        private boolean isNoBatch;
        private boolean isUnionDuplicateChecking;
        private boolean isVerifyEnterprise;
        private boolean isBackFillIndustrialAndCommercialInfo;
        private boolean isBackFillOverwriteOldValue;
        private String locale;
        private List<String> relatedApiNameList;    //支持销售记录关联对象
        private String objectCode;
        private boolean checkOutOwner;
        private boolean removeOutTeamMember;
        private boolean updateOwner;     //是否更新负责人
        private OldOwnerTeamMember oldOwnerTeamMember;
        private MasterInfo masterInfo;
        private List<DetailInfo> detailInfo;
        private boolean supportFieldMapping;
        private Map<String, Object> extendAttribute;
    }
}
