package com.facishare.paas.metadata.dataloader.util;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/12/27.
 */
public interface IRowReader {

    /**
     * 业务逻辑实现方法
     *
     * @param sheetIndex 第几个sheet
     * @param curRow     当前行
     * @param rowList    行的数据
     * @return 是否继续读取
     */
    boolean getRows(int sheetIndex, int curRow, List<String> rowList);

    void setTotalLineCount(int rowCount);
}