package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.common.Config;
import com.facishare.paas.metadata.dataloader.common.DataLoaderConfig;
import com.facishare.paas.metadata.dataloader.common.ExportUrl;
import com.facishare.paas.metadata.dataloader.exception.ExportBusinessException;
import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.rest.Common;
import com.facishare.paas.metadata.dataloader.rest.ExportRestProxy;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportCommonDTO;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportCommonVerify;
import com.facishare.paas.metadata.dataloader.util.ExportFileExt;
import com.facishare.paas.metadata.dataloader.util.I18NExt;
import com.facishare.paas.metadata.dataloader.util.I18NKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Service
@Slf4j
public class ExportExecutorImpl implements IExportExecutor {

    @Autowired
    private ExportRestProxy exportRestProxy;

    @Override
    public ExportCommonVerify.Result exportVerify(Context.ExportCommonContext context) {
        ExportCommonVerify.Result result = ExportCommonVerify.Result.builder().build();
        List<ExportUrl> exportUrls = ExportUrl.exportUrls;
        Optional<String> verifyUrl = exportUrls.stream()
                .filter(x -> Objects.equals(x.getExportBizType(), context.getExportBizType()))
                .map(ExportUrl::getVerifyUrl).findFirst();
        if (!verifyUrl.isPresent()) {
            log.error("obtain exportVerify url failed,please check config,jodId:{}", context.getJobId());
            throw new ExportBusinessException(I18NExt.getOrDefault(I18NKey.SYSTEM_ERROR, "服务器繁忙"));//ignoreI18n
        }
        ExportCommonVerify.Arg arg = ExportCommonVerify.Arg.builder()
                .jobId(context.getJobId())
                .exportBizType(context.getExportBizType())
                .searchQuery(context.getSearchQuery())
                .build();
        Map<String, String> header = Config.makeHeader(context);
        log.info("export verify,arg:{},url:{},header:{}", arg, verifyUrl.get(), header);
        ExportCommonVerify.Result verifyResult = exportRestProxy.exportVerify(arg, verifyUrl.get(), header);
        if (Objects.isNull(verifyResult) || Objects.isNull(verifyResult.getData())) {
            log.error("verifyResult end,result is null");
            throw new ExportBusinessException(I18NExt.getOrDefault(I18NKey.SYSTEM_ERROR, "服务器繁忙"));//ignoreI18n
        }
        int totalCount = verifyResult.getData().getTotalCount();
        long exportMaxCount = DataLoaderConfig.INSTANCE.getExportMaxCount();
        if (totalCount > exportMaxCount) {
            result.setCode(Common.FAIL);
            result.setMessage(I18NExt.getOrDefault(I18NKey.EXPORT_EXCEED_LIMIT, "导出数据量上限为{0},请重新筛选后导出", exportMaxCount));//ignoreI18n
        } else {
            result.setCode(Common.SUCCESS);
        }
        return result;
    }

    @Override
    public ExportCommonDTO.Result exportData(Context.ExportCommonContext context) {
        AbstractExportExecutor.Arg arg = new AbstractExportExecutor.Arg();
        arg.setFileExt(context.getFileExt());
        arg.setSearchQuery(context.getSearchQuery());
        arg.setFileFormat(context.getFileFormat());
        arg.setExportBizType(context.getExportBizType());

        AbstractExportExecutor exportExecutor;
        if (ExportFileExt.CSV.getFileExt().equals(context.getFileExt())) {
            exportExecutor = new ExportCSVExecutor(context, arg);
        } else {
            exportExecutor = new ExportExcelExecutor(context, arg);
        }
        return exportExecutor.executeExport();
    }
}
