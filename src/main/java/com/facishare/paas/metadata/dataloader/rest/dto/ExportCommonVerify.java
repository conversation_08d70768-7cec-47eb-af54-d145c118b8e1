package com.facishare.paas.metadata.dataloader.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface ExportCommonVerify {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        private String jobId;
        private String searchQuery;
        String exportBizType;
    }

    @Data
    @Builder
    class Result extends BaseDTO.CrmResult {
        VerifyResult data;
    }

    @Data
    class VerifyResult {
        private int totalCount;
    }
}
