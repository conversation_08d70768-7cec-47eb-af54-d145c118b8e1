package com.facishare.paas.metadata.dataloader.mongo.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity(value = "import_data", noClassnameStored = true)
public class ImportData {
    @Id
    private String id;
    private String objectApiName;
    private String tenantId;
    private String userId;
    private String data;
    private String masterId;
    private String jobId;
    private Date createTime;
    private Date expireTime;
}
