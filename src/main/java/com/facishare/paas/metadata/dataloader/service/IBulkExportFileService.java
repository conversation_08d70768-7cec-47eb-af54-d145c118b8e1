package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.filter.Context;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/01/07
 */
public interface IBulkExportFileService {
    /**
     * 准备调用fs-crm校验导出功能权限、数量以及其它必要项。
     * @param exportContext 导出上下文环境，包含各种参数。
     * @return 校验结果。
     */
    IBulkExportService.VerifyResult verify(Context.ExportContext exportContext);

    /**
     * 导出数据
     * 1. 主线程定时调用导出的Action，记录全部导出数量，并根据结果更新导出数量;
     * 2. 另一个线程定时ping任务中心，上报导出进度;
     * 3. 导出完毕后调用任务中心complete.
     * @param exportContext 导出上下文环境，包含各种参数。
     */
    IBulkExportService.ExportResult export(Context.ExportContext exportContext);
}
