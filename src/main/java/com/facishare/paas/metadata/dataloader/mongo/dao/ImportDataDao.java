package com.facishare.paas.metadata.dataloader.mongo.dao;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.dataloader.common.DataLoaderConfig;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.mongo.bean.ImportData;
import com.facishare.paas.metadata.dataloader.mongo.bean.QueryResult;
import com.fxiaoke.api.IdGenerator;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mongodb.morphia.query.Query;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

public class ImportDataDao {

    @Autowired
    private DatastoreExt importDataStore;

    public void saveImportDataList(User user, List<ImportData> importDataList) {
        if (CollectionUtils.isEmpty(importDataList)) {
            return;
        }
        int offsetTime = 1000 * 60 * 60 * 24 * DataLoaderConfig.INSTANCE.getImportDataExpireTimeOffset();
        for (ImportData importData : importDataList) {
            long currentTimeMillis = System.currentTimeMillis();
            importData.setId(IdGenerator.get());
            importData.setTenantId(user.getTenantId());
            importData.setUserId(user.getUserId());
            importData.setCreateTime(new Date(currentTimeMillis));
            importData.setExpireTime(new Date(currentTimeMillis + offsetTime));
        }
        getDatastoreExt(user).save(importDataList);
    }


    public List<ImportData> findMasterData(User user, String jobId, String objectApiName, List<String> masterIds) {
        if (CollectionUtils.isEmpty(masterIds)) {
            return Lists.newArrayList();
        }
        Query<ImportData> query = getDatastoreExt(user).createQuery(ImportData.class);
        query.field("jobId").equal(jobId);
        query.field("tenantId").equal(user.getTenantId());
        query.field("objectApiName").equal(objectApiName);
        query.field("masterId").in(masterIds);
        query.order(IObjectData.ID);
        query.limit(masterIds.size());
        return query.asList();
    }


    public QueryResult findImportData(User user, String jobId, String objectApiName, String idOffSet, int limit) {
        Query<ImportData> query = getQuery(jobId, user, objectApiName, null, idOffSet, limit);
        List<ImportData> dataList = query.asList();
        if (CollectionUtils.isEmpty(dataList)) {
            return QueryResult.builder().hasMore(false).build();
        }
        if (dataList.size() < limit) {
            return QueryResult.builder().hasMore(false).dataList(dataList).build();
        }
        idOffSet = dataList.get(dataList.size() - 1).getId();
        return QueryResult.builder().hasMore(true).dataList(dataList).lastId(idOffSet).build();
    }


    public void findAndConsumer(User user, String jobId, String objectApiName, String masterId, int limit, Consumer<List<ImportData>> consumer) {
        if (StringUtils.isBlank(masterId)) {
            return;
        }
        String idOffSet = null;
        do {
            Query<ImportData> query = getQuery(jobId, user, objectApiName, masterId, idOffSet, limit);
            List<ImportData> importDataList = query.asList();
            if (CollectionUtils.isEmpty(importDataList)) {
                break;
            }
            consumer.accept(importDataList);
            int batchDataNum = importDataList.size();
            if (batchDataNum < limit) {
                break;
            }
            idOffSet = importDataList.get(batchDataNum - 1).getId();
        } while (StringUtils.isNotBlank(idOffSet));
    }

    private Query<ImportData> getQuery(String jobId, User user, String objectApiName, String masterId, String idOffSet, int limit) {
        Query<ImportData> query = getDatastoreExt(user).createQuery(ImportData.class);
        query.field("jobId").equal(jobId);
        query.field("tenantId").equal(user.getTenantId());
        query.field("objectApiName").equal(objectApiName);
        if (StringUtils.isNotBlank(masterId)) {
            query.field("masterId").equal(masterId);
        }
        query.order(IObjectData.ID);
        if (Objects.nonNull(idOffSet)) {
            query.field(IObjectData.ID).greaterThan(idOffSet);
        }
        query.limit(limit);
        return query;
    }

    private DatastoreExt getDatastoreExt(User user) {
        return importDataStore.setTenantId(user.getTenantId());
    }
}
