package com.facishare.paas.metadata.dataloader.excel;

import com.facishare.paas.metadata.dataloader.exception.ExcelReadException;
import com.facishare.paas.metadata.dataloader.exception.StopParseExcelException;
import com.facishare.paas.metadata.dataloader.image.decorator.OptimizedImageAwareRowReader;
import com.facishare.paas.metadata.dataloader.task.ExcelExecutor;
import com.facishare.paas.metadata.dataloader.task.ExcelRowReader;
import com.facishare.paas.metadata.dataloader.util.IRowReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.openxml4j.exceptions.OpenXML4JException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.util.CollectionUtils;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;

/**
 * 处理联合导入
 *
 * <AUTHOR>
 * @date 2019-04-12 14:24
 */
@Slf4j
public class Excel2007UnionReader extends Excel2007Reader {

    public Excel2007UnionReader(boolean skipHiddenSheet) {
        super(skipHiddenSheet);
    }

    @Override
    public void endElement(String uri, String localName, String name) throws SAXException {

        String thisStr = null;
        if ("row".equals(name) && !isFirstRowEnd) {
            isFirstRowEnd = true;
        }

        // v => contents of a cell
        if ("t".equals(name) || "v".equals(name)) {
            // Process the value contents as required.
            // Do now, as characters() may be called more than once
            switch (nextDataType) {
                case BOOL:
                    char first = value.charAt(0);
                    thisStr = first == '0' ? "FALSE" : "TRUE";
                    break;
                case ERROR:
                    thisStr = "ERROR:" + value.toString();
                    break;
                case FORMULA:
                    thisStr = value.toString();
                    break;
                case INLINESTR:
                    XSSFRichTextString rtsi = new XSSFRichTextString(value.toString());
                    thisStr = rtsi.toString();
                    break;
                case SSTINDEX:
                    String sstIndex = value.toString();
                    try {
                        int idx = Integer.parseInt(sstIndex);
                        XSSFRichTextString rtss = new XSSFRichTextString(sharedStringsTable.getEntryAt(idx));
                        thisStr = rtss.toString();
                    } catch (NumberFormatException ex) {
                        log.error("Failed to parse SST index '" + sstIndex + "': " + ex.toString());
                    }
                    break;
                case NUMBER:
                    String n = value.toString();
                    try {
                        if (DateUtil.isADateFormat(formatIndex, formatString)) {
                            Date date = HSSFDateUtil.getJavaDate(Double.parseDouble(n));
                            thisStr = SIMPLE_DATE_FORMAT.format(date);
                        } else {
                            //按默认格式，否则会有如下问题：保存的数字是100.1200000000001,实际显示是100.12，由于在sheet.xml中对应cell中缺少格式导致
                            //因此对于没有格式的数字，按默认格式处理
                            this.formatString = "General";
                            this.formatIndex = 0;
                            thisStr = formatter.formatRawCellContents(Double.parseDouble(n), this.formatIndex, this.formatString);
                            //thisStr = n;
                        }
                    } catch (Exception e) {
                        log.error("Analyze date format error. ", e);
                        thisStr = n;
                    }
                    break;

                default:
                    log.warn("(Unexpected type:{})", nextDataType);
                    thisStr = "";
                    break;
            }

            if (lastColumnNumber == -1) {
                lastColumnNumber = 0;
            }
            rowList.add(thisStr);
            // Update column
            if (thisColumn > -1) {
                lastColumnNumber = thisColumn;
            }
        } else if ("row".equals(name)) {
            // We're onto a new row
            if (!CollectionUtils.isEmpty(headerColList)) {
                int headerSize = headerColList.size();
                int rowSize = rowList.size();
                if (headerSize > rowSize) {
                    for (int i = 0; i < headerSize - rowSize; i++) {
                        rowList.add("");
                    }
                }
            }

            boolean isContinue = rowReader.getRows(sheetIndex, rowCount, rowList);
            if (!isContinue) {
                throw new StopParseExcelException("Terminate reading excel");
            }
            clearInfo();
            //     clearInfo(false);
        } else if ("c".equals(name)) {
            //离开cell时
            if (isEnterCell && isCellNoValue) {
                rowList.add("");
            }
            isEnterCell = false;
            isCellNoValue = true;

            //处理丢失的cell
            if (isFirstRowEnd) {
                prevColName = currentColName;
            }
        } else if ("sheetData".equals(name)) {
            //读取完sheet数据，清理
            if (headerColList != null) {
                headerColList.clear();
                isFirstRowEnd = false;
            }
        }
    }

    /**
     * 清除内存信息
     */
    public void clearInfo() {
        rowList.clear();
        lastColumnNumber = -1;
        //处理丢失的cell
        if (isFirstRowEnd) {
            prevColName = null;
            currentColName = null;
        }
    }

//    /**
//     * 清除内存信息
//     */
//    public void clearInfo(boolean resetRowCount) {
//        rowList.clear();
//        if (resetRowCount) {
//            rowCount = 0;
//        } else {
//            rowCount++;
//        }
//        lastColumnNumber = -1;
//
//        //处理丢失的cell
//        if (isFirstRowEnd) {
//            prevColName = null;
//            currentColName = null;
//        }
//    }

    @Override
    public void process(String fileName) {
        InputStream stream = null;
        try {
            OPCPackage opcPackage = OPCPackage.open(fileName);
            sharedStringsTable = new SharedStringTableReader(opcPackage);
            XSSFReader xssfReader = new XSSFReader(opcPackage);
            stylesTable = xssfReader.getStylesTable();
            InputStream workbookData = xssfReader.getWorkbookData();
            processSheet(workbookData);
            XSSFReader.SheetIterator iterator = (XSSFReader.SheetIterator) xssfReader.getSheetsData();
            // 获取ExcelExecutor实例，支持装饰器模式
            ExcelExecutor executor = getExcelExecutor(rowReader);
            if (executor != null) {
                //一共导入主对象+从对象个sheet
                for (int i = 0; iterator.hasNext() && i < executor.getUnionImportApiNameList().size(); i++) {
                    stream = iterator.next();
                    String sheetName = iterator.getSheetName();
                    if (skipHiddenSheet && hiddenSheets.contains(sheetName)) {
                        continue;
                    }
                    //清除上次参数
                    clearInfo();

                    executor.nextSheet(sheetName);
                    final InputStream finalStream = stream;
                    // 读excel 并作向应的处理
                    executor.processSheetIfSupportImportPreProcessing(() -> processSheet(finalStream));
                }
            }
            if (rowReader instanceof ExcelRowReader) {
                ExcelRowReader excelRowReader = (ExcelRowReader) rowReader;
                while (iterator.hasNext()) {
                    stream = iterator.next();
                    String sheetName = iterator.getSheetName();

                    if (hiddenSheets.contains(sheetName)) {
                        continue;
                    }

                    //清除上次参数
                    clearInfo();

                    excelRowReader.nextSheet(sheetName);
                    final InputStream finalStream = stream;
                    excelRowReader.processSheet(() -> processSheet(finalStream));
                }
            }
        } catch (IOException | SAXException | ParserConfigurationException | OpenXML4JException e) {
            log.error("Can not read excel file, fileName:{}", fileName, e);
            throw new ExcelReadException(e);
        } finally {
            try {
                if (null != stream) {
                    stream.close();
                }
            } catch (IOException e) {
                log.error("Error in closing InputStream, fileName:{} ", fileName, e);
            }
        }
    }

    /**
     * 获取ExcelExecutor实例，支持装饰器模式
     *
     * @param rowReader 行读取器
     * @return ExcelExecutor实例，如果无法获取则返回null
     */
    private ExcelExecutor getExcelExecutor(IRowReader rowReader) {
        if (rowReader instanceof ExcelExecutor) {
            return (ExcelExecutor) rowReader;
        } else if (rowReader instanceof OptimizedImageAwareRowReader) {
            OptimizedImageAwareRowReader imageAwareReader = (OptimizedImageAwareRowReader) rowReader;
            if (imageAwareReader.getOriginalRowReader() instanceof ExcelExecutor) {
                return (ExcelExecutor) imageAwareReader.getOriginalRowReader();
            }
        }
        return null;
    }
}
