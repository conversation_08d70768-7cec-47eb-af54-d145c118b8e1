package com.facishare.paas.metadata.dataloader.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class BaseInfo {

    @SerializedName(value = "apiName")
    @JSONField(name = "apiName", alternateNames = "_apiName")
    private String apiName;

    @SerializedName(value = "fieldMapping")
    @JSONField(name = "fieldMapping", alternateNames = "_fieldMapping")
    private List<FieldMapping> fieldMapping;


    @Data
    public static class FieldMapping {

        @SerializedName(value = "colIndex")
        @JSONField(name = "colIndex", alternateNames = "_colIndex")
        private String colIndex;

        @SerializedName(value = "apiName")
        @JSONField(name = "apiName", alternateNames = "_apiName")
        private String apiName;

        @SerializedName(value = "importFieldMark")
        @J<PERSON><PERSON>ield(name = "importFieldMark", alternateNames = "_importFieldMark")
        private String importFieldMark;
    }
}
