package com.facishare.paas.metadata.dataloader.task;

import com.facishare.paas.metadata.dataloader.exception.ExcelReadColumnIndexExceedException;
import com.facishare.paas.metadata.dataloader.exception.ExcelReadException;
import com.facishare.paas.metadata.dataloader.model.*;
import com.facishare.paas.metadata.dataloader.service.FileService;
import com.facishare.paas.metadata.dataloader.service.IBulkImportRestService;
import com.facishare.paas.metadata.dataloader.service.IBulkImportService;
import com.facishare.paas.metadata.dataloader.service.IImportTaskHookService;
import com.facishare.paas.metadata.dataloader.util.FileUtil;
import com.facishare.paas.metadata.dataloader.util.I18NExt;
import com.facishare.paas.metadata.dataloader.util.I18NKey;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 校验导入任务
 * <p>
 * Created by zhenglei on 2017/1/1.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@SuppressWarnings("unchecked")
@Slf4j
public class VerifyTask extends BaseTask {

    private String excelFilePath;
    private String fileExt;

    private int importType;
    private int matchingType;
    private String importObjectApiName;
    /**
     * 导入的从对象列表
     */
    private List<String> unionImportApiNameList;
    private boolean isEmptyValueToUpdate;
    private boolean isWorkFlowEnabled;
    private boolean isTextureImportEnabled;
    private boolean isApprovalFlowEnabled;
    private boolean isNoBatch;
    private User user;
    boolean isUnionDuplicateChecking;
    private String specifiedField; //指定字段
    private boolean updateOwner;
    private boolean supportFieldMapping;
    private MasterInfo masterInfo;
    private List<DetailInfo> detailInfo;
    private Map<String, Object> extendAttribute;

    private IBulkImportRestService importRestService;
    private FileService fileService;
    private IImportTaskHookService importTaskHookService;
    private IBulkImportService.VerifyResult verifyResult;
    private int noBatchCount;
    private int batchTimeOut;

    private String objectCode;

    private static final int MAX_LINE_COUNT = 300000;
    /**
     * 开启工作流，默认最大导入数量
     */
    private static int maxCountWorkflow = 1000;
    /**
     * 开启审批流，默认最大导入数量
     */
    private static int maxCountApprovalflow = 500;
    /**
     * 开启工作流，灰度企业最大导入数量
     */
    private static int maxCountWorkflowGray;
    /**
     * 工作流灰度企业
     */
    private static List<String> grayTenantIdList = Lists.newArrayList();
    private static final int FILE_SIZE_50_MB = 1024 * 1024 * 50;
    private static final int FILE_SIZE_2_MB = 1024 * 1024 * 2;

    private static int maxFlowFileSizeByte;
    private static int maxFlowFileSizeByteGray;
    private static int maxFlowFileSizeMB;
    private static int maxFlowFileSizeMBGray;
    private static int fileSizeLimit;


    private ExcelExecutor excelExecutor;

    private String locale;
    private List<String> relatedApiNameList;

    private static Map<String, Integer> workflowMaxCountGray = Maps.newHashMap();
    private static Map<String, Integer> workflowMaxFileSizeMBGray = Maps.newHashMap();

    private static final Splitter CONFIG_SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();
    private static final Splitter CONFIG_SPLITTER2 = Splitter.on(";").omitEmptyStrings().trimResults();

    private static Map<String, Integer> importFileMaxSizeLimit = Maps.newHashMap();

    private int getTotalRowCount() {
        return excelExecutor.getTotalRowCount();
    }

    static {
        // 初始化在开启工作流下，最大允许导入条数
        ConfigFactory.getConfig("fs-paas-metadata-dataloader", iConfig -> {
            log.warn("start load config fs-paas-metadata-dataloader:{}", iConfig.getString());
            String greyTenant = iConfig.get("workflowCountGray");
            List<String> list = null;
            if (!Strings.isNullOrEmpty(greyTenant)) {
                String[] split = greyTenant.split(",");
                list = Lists.newArrayList(split);
            }
            if (!CollectionUtils.isEmpty(list)) {
                grayTenantIdList = list;
            }
            maxCountWorkflowGray = iConfig.getInt("maxWorkflowCountGray");
            maxFlowFileSizeMB = iConfig.getInt("maxFlowFileSizeMB", 2);
            maxFlowFileSizeMBGray = iConfig.getInt("maxFlowFileSizeMBGray", 10);

            workflowMaxCountGray = getMaxCountGray(iConfig, "workflowMaxCountGray");
            workflowMaxFileSizeMBGray = getMaxCountGray(iConfig, "workflowMaxFileSizeMBGray");
            importFileMaxSizeLimit = getMaxCountGray(iConfig, "importFileMaxSizeLimit");
        });

    }

    private static Map<String, Integer> getMaxCountGray(IConfig config, String confKey) {
        String configStr = config.get(confKey);
        if (StringUtils.isBlank(configStr)) {
            return Maps.newHashMap();
        }
        Map<String, Integer> resultMap = Maps.newHashMap();
        for (String str : CONFIG_SPLITTER2.split(configStr)) {
            String key = StringUtils.substringBefore(str, ":");
            String value = StringUtils.substringAfter(str, ":");
            Integer num = Integer.valueOf(key);
            for (String tenantId : CONFIG_SPLITTER.split(value)) {
                resultMap.put(tenantId, num);
            }
        }
        return resultMap;
    }

    private boolean checkTotalRowCount() {
        int totalRowCount = getTotalRowCount();
        if (isApprovalFlowEnabled) {
            return totalRowCount <= maxCountApprovalflow;
        } else if (isWorkFlowEnabled) {
            return isNoBatch ? totalRowCount <= noBatchCount : totalRowCount <= getMaxCountWithWorkflow(user.getTenantId());
        } else {
            return isNoBatch ? totalRowCount <= noBatchCount : totalRowCount <= MAX_LINE_COUNT;
        }
    }

    private int getMaxCountWithWorkflow(String tenantId) {
        return workflowMaxCountGray.getOrDefault(tenantId, maxCountWorkflow);
    }

    public VerifyTask init() {
        if (isSupportFieldMapping() && !CollectionUtils.isEmpty(detailInfo)) {
            String apiName = masterInfo.getApiName();
            List<String> detailApiNames = detailInfo.stream().map(x -> x.getApiName()).collect(Collectors.toList());
            unionImportApiNameList = Lists.newArrayList();
            unionImportApiNameList.add(apiName);
            unionImportApiNameList.addAll(detailApiNames);
        }
        excelExecutor = ExcelExecutor.builder()
                .fileExt(fileExt)
                .user(user)
                .unionImportApiNameList(unionImportApiNameList)
                .supportFieldMapping(supportFieldMapping)
                .build()
                .init();
        return this;
    }

    private void initContext() {
        verifyResult = new IBulkImportService.VerifyResult();
    }

    private void doVerify(int curRow, List<String> rowList) {
        if (curRow > 1 && checkHeaderEmpty()) {
            return;
        }

        if (curRow != 1) {
            return;
        }

        if (!supportFieldMapping && !excelExecutor.checkTitleRepeat()) {
            setVerifyDataResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_FILE_HEADER_REPEAT_ERROR_MESSAGE, "请检查Excel表头是否存在重复的字段，如有疑问，请联系管理员"));//ignoreI18n
            return;
        }

        if (checkHeaderEmpty()) {
            return;
        }

        //联合查重复,检查是否已生成结果
        if (Objects.nonNull(verifyResult) && verifyResult.getCode() != 0) {
            return;
        }

        //收集数据
        Optional<DataItem> data = excelExecutor.toObjectData(rowList, curRow, false);
        /*兼容联合导入逻辑*/
        //1. 获取当前导入对象，单对象导入即获取importObjectApiName,多个对象，根据Executor获取当前正在处理的sheet所对应的对象
        String importApiName = CollectionUtils.isEmpty(unionImportApiNameList) ? importObjectApiName :
                excelExecutor.getCurrentApiName();
        String type = CollectionUtils.isEmpty(unionImportApiNameList) ? OperationType.VERIFY.name() :
                OperationType.UNION_VERIFY.name();
        //调用
        IBulkImportRestService.BulkInsertArg bulkInsertArg = IBulkImportRestService.BulkInsertArg.builder()
                .apiName(importApiName)
                .importType(importType)
                .matchingType(matchingType)
                .isEmptyValueToUpdate(isEmptyValueToUpdate)
                .isWorkFlowEnabled(isWorkFlowEnabled)
                .isTextureImportEnabled(isTextureImportEnabled)
                .rows(Lists.newArrayList(data.get()))
                .user(user)
                .isUnionDuplicateChecking(isUnionDuplicateChecking)
                .unionApiNames(unionImportApiNameList)
                .objectCode(objectCode)
                .relatedApiNameList(relatedApiNameList)
                .specifiedField(specifiedField)
                .updateOwner(updateOwner)
                .supportFieldMapping(supportFieldMapping)
                .masterInfo(masterInfo)
                .detailInfo(detailInfo)
                .headerExcelCols(excelExecutor.getHeaderExcelCols())
                .extendAttribute(extendAttribute)
                .parameter(IBulkImportRestService.BulkInsertArg.Parameter.builder()
                        .importType(importType)
                        .matchingType(matchingType)
                        .isEmptyValueToUpdate(isEmptyValueToUpdate)
                        .isVerify(true)
                        .operationType(type)
                        .isWorkFlowEnabled(isWorkFlowEnabled)
                        .isTextureImportEnabled(isTextureImportEnabled)
                        .isUnionDuplicateChecking(isUnionDuplicateChecking)
                        .unionApiNames(unionImportApiNameList)
                        .objectCode(objectCode)
                        .relatedApiNameList(relatedApiNameList)
                        .specifiedField(specifiedField)
                        .updateOwner(updateOwner)
                        .build())
                .locale(locale)
                .build();
        IBulkImportRestService.BulkInsertResult result = importRestService.bulkInsert(bulkInsertArg, batchTimeOut);


        if (null == result) {
            setVerifyDataResult(-1, "Can not get Import Result from CRM");
            return;
        }

        boolean success = result.isSuccess();
        String message = result.getMessage();
        setVerifyDataResult(success ? 0 : -1, message);
    }

    private boolean checkHeaderEmpty() {
        //检查表头
        if (CollectionUtils.isEmpty(unionImportApiNameList)) {
            if (CollectionUtils.isEmpty(excelExecutor.getHeaderRow())) {
                setVerifyDataResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_FILE_HEADER_IS_NULL, "导入的Excel文件表头为空，请检查"));//ignoreI18n
                return true;
            }
        } else {
            String currentApiName = excelExecutor.getCurrentApiName();
            for (int i = 0; i < unionImportApiNameList.size(); i++) {
                String apiName = unionImportApiNameList.get(i);
                if (currentApiName.equals(apiName)) {
                    if (i == 0) {
                        // 主对象
                        if (CollectionUtils.isEmpty(excelExecutor.getMasterHeaderRow())) {
                            setVerifyDataResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_FILE_HEADER_IS_NULL, "导入的Excel文件表头为空，请检查"));//ignoreI18n
                            return true;
                        }
                    } else {
                        // 从对象
                        if (CollectionUtils.isEmpty(excelExecutor.getDetailHeaderRowList().get(i - 1))) {
                            setVerifyDataResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_FILE_HEADER_IS_NULL, "导入的Excel文件表头为空，请检查"));//ignoreI18n
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }


    private FileUtil.LocalFile saveSourceTempFile() {
        String newPath = execImportTaskHook(excelFilePath);
        if (StringUtils.isNotEmpty(newPath)) {
            log.warn("execImportTaskHook, oldPath:{}, newPath:{}", excelFilePath, newPath);
            excelFilePath = newPath;
        }
        FileUtil.LocalFile localFile = FileUtil.getInstance().saveFile(fileService, user, excelFilePath, fileExt);
        int maxFileSizeMB = importFileMaxSizeLimit.getOrDefault(user.getTenantId(), 50);
        int maxFileSize = maxFileSizeMB * 1024 * 1024;
        if (localFile.getSize() > maxFileSize) {
            setVerifyDataResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_FILE_SIZE_EXCEED, "导入文件大小不能超过{0}MB，数据不能超过{1}万条", maxFileSizeMB, 30));//ignoreI18n
            return null;
        }
        if (isWorkFlowEnabled && localFile.getSize() > getMaxFlowFileSizeByte(user.getTenantId())) {
            setVerifyDataResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_FILE_SIZE_EXCEED, "导入文件大小不能超过{0}MB，数据不能超过{1}万条", getMaxFlowFileSizeMb(user.getTenantId()), getMaxCountWithWorkflow(user.getTenantId())));//ignoreI18n
            return null;
        }
        return localFile;
    }

    private String execImportTaskHook(String path) {
        ImportTaskHook.Arg arg = ImportTaskHook.Arg.builder()
                .filePath(path)
                .importObjectApiName(importObjectApiName)
                .fileExt(fileExt)
                .build();
        ImportTaskHook.Result result = importTaskHookService.before(user, arg);
        return result.getFilePath();
    }

    private int getMaxFlowFileSizeByte(String tenantId) {
        Integer size = workflowMaxFileSizeMBGray.get(tenantId);
        if (size == null) {
            return FILE_SIZE_2_MB;
        }
        return size * 1024 * 1024;
    }

    private int getMaxFlowFileSizeMb(String tenantId) {
        return workflowMaxFileSizeMBGray.getOrDefault(tenantId, 2);
    }


    public IBulkImportService.VerifyResult verify() throws IOException {
        log.info("Start Verify Import Task execute, ea:{}, ei:{}, userId:{}, filePath:{}, fileExt:{}, apiName:{} ",
                user.getEa(), user.getTenantId(), user, excelFilePath, fileExt, importType);
        FileUtil.LocalFile localFile = null;
        try {
            localFile = saveSourceTempFile();
            if (Objects.nonNull(verifyResult) && !Strings.isNullOrEmpty(verifyResult.getMessage())) {
                return verifyResult;
            }
            execute(localFile.getPath());

            //检查行数
            if (!checkTotalRowCount()) {
                if (isApprovalFlowEnabled) {
                    setVerifyDataResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_TRIGGER_APPROVAL_FLOW_EXCEED, "导入触发审批流时，导入数量不能超过{0}条", maxCountApprovalflow));//ignoreI18n
                } else if (isWorkFlowEnabled) {
                    setVerifyDataResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_TRIGGER_WORK_FLOW_EXCEED, "导入触发工作流时，导入数量不能超过{0}条", getMaxCountWithWorkflow(user.getTenantId())));//ignoreI18n
                } else if (isNoBatch) {
                    setVerifyDataResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_DATA_COUNT_EXCEED, "导入数量不能超过{0}条", noBatchCount));//ignoreI18n
                } else {
                    setVerifyDataResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_FILE_SIZE_EXCEED, "导入文件大小不能超过{0}MB，数据不能超过{1}万条", 50, 30));//ignoreI18n
                }
            }
        } catch (ExcelReadColumnIndexExceedException e) {
            log.warn("import verify read excel column index exceed fail, , user:{}, filePath:{}", user, excelFilePath, e);
            setVerifyDataResult(-1, e.getMessage());
        } catch (ExcelReadException e) {
            log.warn("import verify read excel fail, user:{}, filePath:{}", user, excelFilePath, e);
            setVerifyDataResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_READ_FILE_FAILED, "读取文件失败，请检查文件或者稍后重试"));//ignoreI18n
        } catch (Exception e) {
            log.warn("Verify import Data Error, user:{}, filePath:{}, ", user, excelFilePath, e);
            if (excelExecutor.isExcelFormatWrong()) {
                setVerifyDataResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_FILE_CONTENT_ERROR, "Excel文件内容的格式不正确，请检查"));//ignoreI18n
            } else if (e.getMessage().contains("EncryptedPackage, EncryptionInfo")) {
                setVerifyDataResult(-1, I18NExt.getOrDefault(I18NKey.IMPORT_ENCRYPT_NO_READ, "Excel文件被加密，无法读取"));//ignoreI18n
            } else {
                setVerifyDataResult(-1, "Unexpected Error Occur:" + e.getMessage());
                throw e;
            }
        } finally {
            if (Objects.nonNull(localFile)) {
                localFile.deleteTempFile();
            }
        }

        return verifyResult;
    }

    private void execute(String path) throws IOException {
        //开始执行导入
        excelExecutor.execute(path, (curRow, rowInfo) -> {
            try {
                doVerify(curRow, rowInfo);
                return curRow <= 0;
            } catch (Exception e) {
                log.error("Error in handle row, rowIndex:{} ", curRow, e);
            }
            return true;
        });
    }

    private void setVerifyDataResult(int code, String message) {
        if (code == -1) {
            if (!CollectionUtils.isEmpty(unionImportApiNameList)) {
                //联合导入提示
                message = excelExecutor.getCurrentSheetName() + ": " + message;
            }
        }
        verifyResult = IBulkImportService.VerifyResult.builder()
                .code(code)
                .message(message)
                .build();
    }
}

