package com.facishare.paas.metadata.dataloader.controller;


import com.facishare.converter.EIEAConverter;
import com.facishare.paas.metadata.dataloader.filter.ContextManager;
import com.facishare.paas.metadata.dataloader.model.SheetContent;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.service.ExcelParseService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import java.util.List;

@Component
@Path("/bulkimport")
@Slf4j
@Consumes({"application/json"})
@Produces({"application/json"})
public class ExcelParseController {

    @Autowired
    private ExcelParseService excelParseService;
    @Autowired
    private EIEAConverter converter;

    @Path("/excelParse")
    @POST
    public Result excelParse(Arg arg) {
        User user = ContextManager.getContext().getUser();
        user.setEa(converter.enterpriseIdToAccount(Integer.parseInt(user.getTenantId())));
        List<SheetContent> parse = excelParseService.parse(user, arg.getFilePath(), BooleanUtils.isTrue(arg.getMultiSheets()));
        Result result = new Result();
        result.setSheetContents(parse);
        return result;
    }

    @Data
    public static class Arg {
        private String filePath;
        private Boolean multiSheets;
    }

    @Data
    public static class Result {
        private List<SheetContent> sheetContents;
    }
}
