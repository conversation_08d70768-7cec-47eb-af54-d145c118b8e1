package com.facishare.paas.metadata.dataloader.image.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Excel单元格位置
 * 用于标识图片在Excel中的具体位置
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode
public class CellPosition {
    
    /**
     * 行号（0-based）
     */
    private final int row;
    
    /**
     * 列号（0-based）
     */
    private final int col;
    
    /**
     * 构造函数
     * 
     * @param row 行号（0-based）
     * @param col 列号（0-based）
     */
    public CellPosition(int row, int col) {
        this.row = row;
        this.col = col;
    }
    
    /**
     * 获取Excel格式的单元格地址（如A1, B2等）
     * 
     * @return Excel格式的地址
     */
    public String getExcelAddress() {
        return getColumnLetter(col) + (row + 1);
    }
    
    /**
     * 将列号转换为Excel列字母
     * 
     * @param columnIndex 列号（0-based）
     * @return Excel列字母（如A, B, AA等）
     */
    private String getColumnLetter(int columnIndex) {
        StringBuilder columnLetter = new StringBuilder();
        while (columnIndex >= 0) {
            columnLetter.insert(0, (char) ('A' + columnIndex % 26));
            columnIndex = columnIndex / 26 - 1;
        }
        return columnLetter.toString();
    }
    
    /**
     * 创建CellPosition实例
     *
     * @param row 行号（0-based）
     * @param col 列号（0-based）
     * @return CellPosition实例
     */
    public static CellPosition of(int row, int col) {
        return new CellPosition(row, col);
    }

    /**
     * 从Excel地址创建CellPosition
     *
     * @param excelAddress Excel地址（如A1, B2等）
     * @return CellPosition实例
     * @throws IllegalArgumentException 如果地址格式不正确
     */
    public static CellPosition fromExcelAddress(String excelAddress) {
        if (excelAddress == null || excelAddress.trim().isEmpty()) {
            throw new IllegalArgumentException("Excel address cannot be null or empty");
        }
        
        excelAddress = excelAddress.trim().toUpperCase();
        
        // 分离字母和数字部分
        int i = 0;
        while (i < excelAddress.length() && Character.isLetter(excelAddress.charAt(i))) {
            i++;
        }
        
        if (i == 0 || i == excelAddress.length()) {
            throw new IllegalArgumentException("Invalid Excel address format: " + excelAddress);
        }
        
        String columnPart = excelAddress.substring(0, i);
        String rowPart = excelAddress.substring(i);
        
        try {
            int row = Integer.parseInt(rowPart) - 1; // Convert to 0-based
            int col = getColumnIndex(columnPart);
            
            if (row < 0 || col < 0) {
                throw new IllegalArgumentException("Invalid Excel address: " + excelAddress);
            }
            
            return new CellPosition(row, col);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid Excel address format: " + excelAddress, e);
        }
    }
    
    /**
     * 将Excel列字母转换为列号
     * 
     * @param columnLetter Excel列字母（如A, B, AA等）
     * @return 列号（0-based）
     */
    private static int getColumnIndex(String columnLetter) {
        int columnIndex = 0;
        for (int i = 0; i < columnLetter.length(); i++) {
            columnIndex = columnIndex * 26 + (columnLetter.charAt(i) - 'A' + 1);
        }
        return columnIndex - 1; // Convert to 0-based
    }
    
    /**
     * 检查位置是否有效
     * 
     * @return 如果位置有效返回true
     */
    public boolean isValid() {
        return row >= 0 && col >= 0;
    }
    
    /**
     * 获取相对位置
     * 
     * @param rowOffset 行偏移量
     * @param colOffset 列偏移量
     * @return 新的CellPosition
     */
    public CellPosition offset(int rowOffset, int colOffset) {
        return new CellPosition(row + rowOffset, col + colOffset);
    }
    
    /**
     * 计算与另一个位置的距离
     * 
     * @param other 另一个位置
     * @return 曼哈顿距离
     */
    public int distanceTo(CellPosition other) {
        return Math.abs(this.row - other.row) + Math.abs(this.col - other.col);
    }
    
    /**
     * 检查是否在指定范围内
     * 
     * @param startRow 起始行
     * @param endRow 结束行
     * @param startCol 起始列
     * @param endCol 结束列
     * @return 如果在范围内返回true
     */
    public boolean isInRange(int startRow, int endRow, int startCol, int endCol) {
        return row >= startRow && row <= endRow && col >= startCol && col <= endCol;
    }
    
    @Override
    public String toString() {
        return String.format("CellPosition(%d, %d) [%s]", row, col, getExcelAddress());
    }
    
    /**
     * 比较两个位置的大小（先按行，再按列）
     * 
     * @param other 另一个位置
     * @return 比较结果
     */
    public int compareTo(CellPosition other) {
        if (this.row != other.row) {
            return Integer.compare(this.row, other.row);
        }
        return Integer.compare(this.col, other.col);
    }
}
