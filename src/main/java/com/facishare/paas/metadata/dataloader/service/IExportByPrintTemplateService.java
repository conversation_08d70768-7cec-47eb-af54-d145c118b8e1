package com.facishare.paas.metadata.dataloader.service;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.rest.dto.BaseDTO;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportResult;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Delegate;

import java.util.List;


/**
 * 批量打印文件导出服务
 *
 */
public interface IExportByPrintTemplateService {
    /**
     * 准备调用fs-crm校验导出功能权限、数量以及其它必要项。
     * @param exportContext 导出上下文环境，包含各种参数。
     * @return 校验结果。
     */
    IBulkExportService.VerifyResult verify(Context.ExportContext exportContext);

    /**
     * 导出数据
     * 1. 主线程定时调用导出的Action，记录全部导出数量，并根据结果更新导出数量;
     * 2. 另一个线程定时ping任务中心，上报导出进度;
     * 3. 导出完毕后调用任务中心complete.
     * @param exportContext 导出上下文环境，包含各种参数。
     */
    IBulkExportService.ExportResult export(Context.ExportContext exportContext);

    @Builder
    @Data
    class Arg {
        @JsonProperty("job_id")
        @SerializedName("job_id")
        private String jobId;
        @JsonProperty("dataIdList")
        @SerializedName("dataIdList")
        private List<String> dataIdList;
        @JsonProperty(value = "search_query_info")
        @SerializedName(value = "search_query_info")
        String searchQueryInfo;
        @JsonProperty("object_describe_api_name")
        @SerializedName("object_describe_api_name")
        private String objectDescribeApiName;
        @JsonProperty("print_template_id")
        @SerializedName("print_template_id")
        private String printTemplateId;
        @JsonProperty("bulk_export_type")
        @SerializedName("bulk_export_type")
        private String bulkExportType;
        @JsonProperty("result_processor")
        @SerializedName("result_processor")
        private String resultProcessor;

        /**
         * 任务 token 用于异步导出
         */
        @JsonProperty("token")
        @SerializedName("token")
        private String token;
    }

    @Data
    class RestAPIResult extends BaseDTO.CrmResult implements ExportResult {

        @Delegate
        @SerializedName("data")
        private Result result;

    }

    @Data
    class Result {

        @JSONField(name = "success")
        @JsonProperty("success")
        @SerializedName("success")
        private Boolean success;

        @JSONField(name = "ext")
        @JsonProperty("ext")
        @SerializedName("ext")
        private String ext;

        @JSONField(name = "file_name")
        @JsonProperty("file_name")
        @SerializedName("file_name")
        String fileName;

        /**
         * 返回给 dataloader 的结果中 path 不为空认为导出成功
         */
        @JSONField(name = "path")
        @JsonProperty("path")
        @SerializedName("path")
        String path;

        @JSONField(name = "export_type")
        @JsonProperty("export_type")
        @SerializedName("export_type")
        private String exportType;

        @JSONField(name = "M4")
        String token;

        long size;

        @JSONField(name = "total_count")
        @JsonProperty("total_count")
        @SerializedName("total_count")
        int totalCount;

        @JSONField(name = "current_count")
        @JsonProperty("current_count")
        @SerializedName("current_count")
        int currentCount;


        Long fileExpiredTime;

    }
}
