package com.facishare.paas.metadata.dataloader.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.metadata.dataloader.common.Config;
import com.facishare.paas.metadata.dataloader.common.ExportUrl;
import com.facishare.paas.metadata.dataloader.exception.ExportBusinessException;
import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.rest.ExportRestProxy;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportCommonDTO;
import com.facishare.paas.metadata.dataloader.util.ExportFileExt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class ExportDataServiceImpl implements IExportDataService {

    @Autowired
    private ExportRestProxy exportRestProxy;

    public ExportCommonDTO.ExportHeader findExportHeader(ExportCommonDTO.Arg arg, Context.ExportCommonContext context) {
        ExportCommonDTO.ExportHeader result;
        List<ExportUrl> exportUrls = ExportUrl.exportUrls;
        Optional<String> headerUrl = exportUrls.stream()
                .filter(x -> Objects.equals(x.getExportBizType(), arg.getExportBizType()))
                .map(ExportUrl::getHeaderUrl).findFirst();
        if (!headerUrl.isPresent()) {
            log.error("obtain findExportHeader url failed,please check config,jodId:{}", arg.getJobId());
            throw new ExportBusinessException("obtain findExportHeader url failed,please check config");
        }
        Map<String, String> header = Config.makeHeader(context);
        log.info("findExportHeader start,arg:{},url:{},header:{}", arg, headerUrl.get(), header);
        ExportCommonDTO.ExportHeaderResult headerResult = exportRestProxy.findExportHeader(arg, headerUrl.get(), header);
        if (Objects.isNull(headerResult) || Objects.isNull(headerResult.getData())) {
            log.error("findExportHeader end,result is null,jodId:{}", arg.getJobId());
            throw new ExportBusinessException("obtain exportHeader failed!");
        }
        result = headerResult.getData();
        log.info("findExportHeader end,result:{}", JSON.toJSONString(result));
        return result;
    }

    public ExportCommonDTO.ExportData findExportData(ExportCommonDTO.Arg arg, Context.ExportCommonContext context) {
        ExportCommonDTO.ExportData result = ExportCommonDTO.ExportData.builder().build();
        List<ExportUrl> exportUrls = ExportUrl.exportUrls;
        Optional<String> exportUrl = exportUrls.stream()
                .filter(x -> Objects.equals(x.getExportBizType(), arg.getExportBizType()))
                .map(ExportUrl::getDataUrl)
                .findFirst();
        if (!exportUrl.isPresent()) {
            log.error("obtain findExportData url failed,please check config,jodId:{}", arg.getJobId());
            return result;
        }
        try {
            Map<String, String> header = Config.makeHeader(context);
            log.info("findExportData start,jobId:{},arg:{},url:{},header:{}", arg.getJobId(), arg, exportUrl.get(), header);
            ExportCommonDTO.ExportDataResult exportDataResult = exportRestProxy.findExportData(arg, exportUrl.get(), header);
            if (Objects.isNull(exportDataResult) || Objects.isNull(exportDataResult.getData())) {
                log.info("findExportData end,result is null,jodId:{}", arg.getJobId());
                return result;
            }
            result = exportDataResult.getData();
            log.info("findExportData end,jodId:{},callBackData:{},size:{}",
                    arg.getJobId(), result.getCallBackData(), CollectionUtils.emptyIfNull(result.getGroupDataList()).size());
            return result;
        } catch (Exception e) {
            log.error("findExportData error,jobId:{},arg:{}", arg.getJobId(), arg, e);
            return result;
        }
    }



}
