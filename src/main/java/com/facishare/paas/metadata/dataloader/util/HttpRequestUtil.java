package com.facishare.paas.metadata.dataloader.util;

import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.List;
import java.util.Map;

/**
 * 发送http请求
 * <p>
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/11/15.
 */
@Slf4j
public class HttpRequestUtil {
    private HttpRequestUtil() {
    }

    private enum HttpRequestUtilHolder {
        INSTANCE;

        private HttpRequestUtil singleton;

        HttpRequestUtilHolder() {
            singleton = new HttpRequestUtil();
        }

        public HttpRequestUtil getSingleton() {
            return singleton;
        }
    }

    public static HttpRequestUtil getInstance() {
        return HttpRequestUtilHolder.INSTANCE.getSingleton();
    }

    public String doPost(String url, String bodyJSON, Map<String, String> headers) {
        return doPost(url, bodyJSON, headers, -1);
    }

    public String doPost(String url, String bodyJSON, Map<String, String> headers, int timeOut) {
        StringEntity stringEntity = new StringEntity(bodyJSON, Charsets.UTF_8);
        stringEntity.setContentEncoding("UTF-8");
        stringEntity.setContentType("application/json");

        HttpPost httppost = new HttpPost(url);
        httppost.setEntity(stringEntity);
        Header[] headerArray = parseFromMap(headers);
        httppost.setHeaders(headerArray);
        if (timeOut > 0) {
            httppost.setConfig(RequestConfig.custom().setSocketTimeout(timeOut * 1000).build());
        }

        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response;
        try {
            response = httpClient.execute(httppost);
            int statusCode = response.getStatusLine().getStatusCode();
            if (HttpStatus.SC_OK != statusCode) {
                log.error("error in request, statusCode: {}, reason: {}", response.getStatusLine().getStatusCode(),
                        response.getStatusLine().getReasonPhrase());
                if (HttpStatus.SC_GATEWAY_TIMEOUT == statusCode) {
                    return I18NExt.getOrDefault(I18NKey.SYSTEM_TIMEOUT_ERROR, "系统超时，请检查数据是否导入成功");//ignoreI18n
                } else {
                    log.error("requestBody:{}", bodyJSON);
                    return I18NExt.getOrDefault(I18NKey.SYSTEM_ERROR, "服务器繁忙");//ignoreI18n
                }
            }

        } catch (SocketTimeoutException e) {
            log.error("timeout in send http POST request on importing excel, url:{}, jsonBody:[{}]", url, bodyJSON, e);
            return I18NExt.getOrDefault(I18NKey.SYSTEM_TIMEOUT_ERROR, "系统超时，请检查数据是否导入成功");//ignoreI18n
        } catch (IOException e) {
            log.error("Error in send http POST request on importing excel, url:{}, jsonBody:[{}]", url, bodyJSON, e);
            return null;
        }

        HttpEntity responseEntity = response.getEntity();
        String result;
        try {
            result = EntityUtils.toString(responseEntity, "UTF-8");
        } catch (IOException e) {
            log.error("Error in getting and parsing http POST response on importing excel, url:{}", url, e);
            return null;
        } finally {
            try {
                response.close();
                httpClient.close();
            } catch (IOException e) {
                log.error("Error in closing HttpConnection in post Request", e);
            }
        }
        return result;
    }

    private Header[] parseFromMap(Map<String, String> headers) {
        if (CollectionUtils.isEmpty(headers)) {
            return null;
        }
        List<Header> list = Lists.newArrayList();
        headers.forEach((k, v) -> list.add(new BasicHeader(k, v)));
        return list.toArray(new Header[0]);
    }
}
