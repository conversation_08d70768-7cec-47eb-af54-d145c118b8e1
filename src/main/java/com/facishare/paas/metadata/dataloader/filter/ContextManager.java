package com.facishare.paas.metadata.dataloader.filter;

import com.facishare.paas.I18N;
import com.github.trace.TraceContext;

/**
 * 创建，保存，删除，修改Context，
 * 每个线程对应一个Context
 *
 * <AUTHOR>
 * @date 2019-05-29 11:42
 */
public class ContextManager {
    private static final InheritableThreadLocal<Context> LOCAL_THREAD = new InheritableThreadLocal<>();

    public static Context getContext() {
        return LOCAL_THREAD.get();
    }

    public static void setContext(Context context) {
        LOCAL_THREAD.set(context);
        I18N.setContext(context.getTenantId(), context.getLocale());
        TraceContext.get().setLocale(context.getLocale());
    }

    public static void removeContext() {
        LOCAL_THREAD.remove();
        I18N.clearContext();
        TraceContext.remove();
    }
}
