package com.facishare.paas.metadata.dataloader.filter;

import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.rest.dto.DetailArg;
import lombok.Data;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 使用上下文环境保存参数
 * Build模式，只有在filter包中才能创建相应的Context对象实例
 *
 * <AUTHOR>
 * @date 2019-05-29 11:42
 */
@Data
public abstract class Context {
    @Delegate
    private User user;
    private String jobId;

    private Context(Builder<?> builder) {
    }

    /**
     * 递归参数类型，创建具体子类时无需Cast
     */
    static abstract class Builder<T extends Builder<T>> {
        abstract Context build();

        abstract T self();
    }

    @Data
    public static class ExportCommonContext extends Context {
        private String exportBizType;
        private String fileFormat;
        private String searchQuery;
        private String fileExt;
        private List<String> describeApiNames;

        public static class Builder extends Context.Builder<Builder> {
            public Builder() {
            }

            @Override
            public ExportCommonContext build() {
                return new ExportCommonContext(this);
            }

            @Override
            Builder self() {
                return this;
            }
        }

        ExportCommonContext(Builder builder) {
            super(builder);
        }

        public String getFileFormat() {
            return StringUtils.isBlank(fileFormat) ? "UTF-8" : fileFormat;
        }

        public String getFileExt() {
            return StringUtils.isBlank(fileExt) ? "csv" : fileExt;
        }
    }

    @Data
    public static class NoUsedContext extends Context {

        static class Builder extends Context.Builder<Builder> {
            public Builder() {
            }

            @Override
            NoUsedContext build() {
                return new NoUsedContext(this);
            }

            @Override
            Builder self() {
                return this;
            }
        }

        private NoUsedContext(Builder builder) {
            super(builder);
        }
    }


    @Data
    public static class ExportContext extends Context {
        private String objectDescribeApiName;
        private List<String> dataIdList;
        private String recordTypeApiName;
        private String searchTemplateId;
        private String searchQueryInfo;
        private List<String> otherNameList;
        private Boolean includeId;
        private Boolean isIgnoreSceneFilter;
        private Boolean isIgnoreSceneRecordType;
        private String searchTemplateType;
        private Boolean accordingToList;
        private Boolean isNoExportRelevantTeam;
        private Integer method;
        private List<String> fieldApiNames;
        private DetailArg detailArg;
        private Boolean isBulkExcelPrint;
        private String printTemplateId;
        private List<String> fileFields;
        private String whatApiName;
        private String resultProcessor;

        static class Builder extends Context.Builder<Builder> {
            Builder() {
            }

            @Override
            ExportContext build() {
                return new ExportContext(this);
            }

            @Override
            Builder self() {
                return this;
            }
        }

        private ExportContext(Builder builder) {
            super(builder);
        }
    }

    @Data
    public static class ImportContext extends Context {
        private String objectDescribeApiName;
        private String excelFilePath; //excel文件路径
        private String fileExt; //后缀名
        private List<String> unionImportApiNameList;  //联合导入对象列表
        private int importType;
        private int matchingType;
        private boolean isEmptyValueToUpdate;
        private boolean isWorkFlowEnabled;
        private boolean isNoBatch;
        private boolean isUnionDuplicateChecking;
        private String objectCode;
        private List<String> relatedApiNameList;


        static class Builder extends Context.Builder<ImportContext.Builder> {
            Builder() {
            }

            @Override
            ImportContext build() {
                return new ImportContext(this);
            }

            @Override
            Builder self() {
                return this;
            }
        }

        private ImportContext(Builder builder) {
            super(builder);
        }
    }

}
