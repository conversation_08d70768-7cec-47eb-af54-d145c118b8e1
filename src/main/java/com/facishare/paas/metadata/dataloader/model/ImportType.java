package com.facishare.paas.metadata.dataloader.model;

import com.google.common.collect.Lists;

import java.util.Map;
import java.util.stream.Collectors;

public enum ImportType {
    INSERT(0),
    UPDATE(1),
    UPSERT(2);

    private int value;
    private static Map<Integer, ImportType> map;
    ImportType(int value) {
        this.value = value;
    }

    static {
        map = Lists.newArrayList(ImportType.values()).stream().collect(Collectors.toMap(ImportType::getValue, a -> a));
    }

    public int getValue() {
        return value;
    }

    public static ImportType parseValue(int value) {
        return map.get(value);
    }

}
