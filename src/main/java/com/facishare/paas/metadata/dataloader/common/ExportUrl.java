package com.facishare.paas.metadata.dataloader.common;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class ExportUrl {
    private String verifyUrl;
    private String headerUrl;
    private String dataUrl;
    private String exportBizType;


    public static List<ExportUrl> exportUrls = Lists.newArrayList();

    static {
        ConfigFactory.getConfig("fs-paas-metadata-dataloader-export", iConfig -> {
            exportUrls = initUrl(iConfig.getString());
        });
    }

    private static List<ExportUrl> initUrl(String exportUrl) {
        if (StringUtils.isBlank(exportUrl)) {
            return Lists.newArrayList();
        }
        List<ExportUrl> exportUrls = JSON.parseArray(exportUrl, ExportUrl.class);
        if (CollectionUtils.isEmpty(exportUrls)) {
            return Lists.newArrayList();
        }
        return exportUrls;
    }
}
