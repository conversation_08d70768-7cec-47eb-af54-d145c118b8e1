package com.facishare.paas.metadata.dataloader.task;

import com.facishare.paas.metadata.dataloader.model.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InvokeResult {
    String jobId;
    String result;
    String code;
    String message;
    Integer successRowCount;
    Integer failRowCount;
    Integer notImportCount;
    Integer totalRowCount;
    Integer completeRowCount;
    String fileExt;
    String fileName;

    /**
     * 0:新建导入
     * 1：更新导入
     */
    private Integer importType;
    /**
     * 导入的对象
     */
    private String importObjectApiName;
    /**
     * 联合导入的对象列表
     */
    private List<String> unionImportApiNameList;
    /**
     * 操作人信息
     */
    private User user;

    /**
     * 触发审批流程
     */
    private Boolean approvalFlowEnabled;

    /**
     * 触发工作流程
     */
    private Boolean workFlowEnabled;

    /**
     * 文件过期时间
     */
    private Long fileExpiredTime;
}
