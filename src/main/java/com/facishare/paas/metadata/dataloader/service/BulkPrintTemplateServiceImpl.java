package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.common.Config;
import com.facishare.paas.metadata.dataloader.filter.Context.ExportContext;
import com.facishare.paas.metadata.dataloader.rest.Common;
import com.facishare.paas.metadata.dataloader.rest.ExportRestProxy;
import com.facishare.paas.metadata.dataloader.service.IBulkExportService.ExportResult;
import com.facishare.paas.metadata.dataloader.service.IBulkExportService.VerifyResult;
import com.facishare.paas.metadata.dataloader.service.IBulkExportService.VerifyResult.VerifyResultBuilder;
import com.facishare.paas.metadata.dataloader.task.ExportFilesTask;
import com.facishare.paas.metadata.dataloader.task.ExportTask;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class BulkPrintTemplateServiceImpl implements IExportByPrintTemplateService {

    @Autowired
    private ExportRestProxy exportRestProxy;
    @Override
    public VerifyResult verify(ExportContext context) {
        VerifyResult result = null;
        String objApiName = context.getObjectDescribeApiName();
        Map<String, String> header = Config.makeHeader(context);
        Arg body = Arg.builder()
                .objectDescribeApiName(objApiName)
                .dataIdList(context.getDataIdList())
                .printTemplateId(context.getPrintTemplateId())
                .build();
        try {
            RestAPIResult apiResult = exportRestProxy.exportByPrintTemplateVerify(body, header, objApiName);
            int code = Optional.of(apiResult)
                    .filter(r -> r.getCode() == Common.SUCCESS)
                    .filter(RestAPIResult::getSuccess)
                    .map(c -> Common.SUCCESS)
                    .orElse(Common.FAIL);
            result = VerifyResult.builder()
                    .code(code)
                    .message(apiResult.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("Error in export by print template verify, tenantId:{}, ea:{}, userId:{}, apiName:{}",
                    context.getUser().getTenantId(),
                    context.getUser().getEa(),
                    context.getUser().getUserId(),
                    context.getObjectDescribeApiName(),
                    e);
            result = VerifyResult.builder()
                    .code(Common.FAIL)
                    .message("System Error!")
                    .build();

        }
        return result;
    }

    @Override
    public ExportResult export(ExportContext context) {
        ExportResult result = null;
        String objApiName = context.getObjectDescribeApiName();
        Map<String, String> header = Config.makeHeader(context);
        Arg body = Arg.builder()
                .objectDescribeApiName(objApiName)
                .dataIdList(context.getDataIdList())
                .searchQueryInfo(context.getSearchQueryInfo())
                .printTemplateId(context.getPrintTemplateId())
                .resultProcessor(context.getResultProcessor())
                .jobId(context.getJobId())//可能需要文件服务调用异步中心来最终完成整y个流程
                .build();

        try {
            RestAPIResult apiResult = exportRestProxy.exportByPrintTemplate(body, header, objApiName);
            if (apiResult.getCode() != Common.SUCCESS) {
                log.warn("First invoke export by print template action failed. body:{}, result:{}", body, apiResult);
                return IBulkExportService.ExportResult.builder()
                        .code(Common.FAIL)
                        .message(apiResult.getMessage())
                        .build();
            }
            if (Strings.isNullOrEmpty(apiResult.getToken())) {
                // 未获取到token，失败
                log.warn("First invoke export by print template action failed. No token, body:{}, result:{}", body, apiResult);
                return IBulkExportService.ExportResult.builder().code(Common.FAIL).message(apiResult.getMessage()).build();
            }

            // 创建导出任务 ExportTask 实例。
            // 在新线程中定时通过Rest接口调用action。
            // 导出不需要ping任务中心，导出完成后调用complete接口即可
            // 第一次调用导出才会返回totalCount
            body.setToken(apiResult.getToken());  // 第一次调用完成后，将返回的token设置入body中进行之后的调用
            String apiName = context.getObjectDescribeApiName();
            ExportFilesTask.createTaskAndInvoker(context.getJobId(), apiResult.getToken(), apiResult.getTotalCount(),
                    () -> exportRestProxy.exportByPrintTemplate(body, header, apiName));
            result = ExportResult.builder()
                    .code(Common.SUCCESS)
                    .message(apiResult.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("Error in export by print template, tenantId:{}, ea:{}, userId:{}, apiName:{}",
                    context.getUser().getTenantId(),
                    context.getUser().getEa(),
                    context.getUser().getUserId(),
                    context.getObjectDescribeApiName(),
                    e);
            return ExportResult.builder()
                    .code(Common.FAIL)
                    .message("System Error!")
                    .build();
        }


        return result;
    }
}
