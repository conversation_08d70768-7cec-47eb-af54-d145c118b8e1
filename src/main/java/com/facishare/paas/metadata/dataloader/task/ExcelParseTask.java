package com.facishare.paas.metadata.dataloader.task;

import com.facishare.paas.metadata.dataloader.exception.ExcelReadException;
import com.facishare.paas.metadata.dataloader.model.ExcelCol;
import com.facishare.paas.metadata.dataloader.model.SheetContent;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.paas.metadata.dataloader.service.FileService;
import com.facishare.paas.metadata.dataloader.util.FileUtil;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Data
@Builder
@SuppressWarnings("unchecked")
@Slf4j
public class ExcelParseTask extends BaseTask {
    private String excelFilePath;
    private String fileExt;
    private String locale;
    private User user;
    private FileService fileService;
    private ExcelRowReader excelRowReader;
    private FileUtil.LocalFile localFile;


    private boolean multiSheets;
    private List<SheetContent> sheetContents;

    public ExcelParseTask init() {
        excelRowReader = new ExcelRowReader(multiSheets, fileExt);
        sheetContents = Lists.newArrayList();
        return this;
    }

    public List<SheetContent> parse() throws IOException {
        try {
            executeParseExcel();
            return sheetContents;
        } finally {
            // 完成后删除本地文件
            Optional.ofNullable(localFile).ifPresent(FileUtil.LocalFile::deleteTempFile);
        }
    }

    private FileUtil.LocalFile getLocalFile() {
        return localFile = FileUtil.getInstance().saveFile(fileService, user, excelFilePath, fileExt);
    }

    public void executeParseExcel() {
        try {
            //保存源excel文件到本地
            FileUtil.LocalFile localFile = getLocalFile();
            //解析excel文件
            executeReader(localFile.getPath());
        } catch (ExcelReadException e) {
            log.error("ExcelReadException", e);
        } catch (Exception e) {
            log.error("Exception execute", e);
        }
    }

    private void executeReader(String path) throws IOException {
        excelRowReader.reader(path, (curRow, rowInfo) -> {
            if (curRow == 1) {
                handleRowInfo(rowInfo);
            }
            return false;
        });
    }

    private void handleRowInfo(List<String> rowInfo) {
        List<ExcelCol> excelCols = Lists.newArrayList();
        for (int i = 0; i < rowInfo.size(); i++) {
            String rowContent = rowInfo.get(i);
            if (StringUtils.isBlank(rowContent)) {
                continue;
            }
            ExcelCol excelCol = ExcelCol.builder().colName(rowContent).colIndex(String.valueOf(i)).build();
            excelCols.add(excelCol);
        }
        SheetContent sheetContent = SheetContent.builder()
                .sheetName(excelRowReader.getCurrentSheetName())
                .sheetIndex(String.valueOf(excelRowReader.getCurrentSheetIndex()))
                .excelCols(excelCols).build();
        sheetContents.add(sheetContent);
    }
}
