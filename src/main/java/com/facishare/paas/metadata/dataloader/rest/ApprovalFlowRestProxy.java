package com.facishare.paas.metadata.dataloader.rest;

import com.facishare.paas.metadata.dataloader.rest.dto.FindFlowType;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * 审批流rest接口代理类
 * Created by zhouwr on 2022/12/9.
 */
@RestResource(value = "PAAS-FLOW", desc = "审批流服务", contentType = "application/json", codec = "com.facishare.paas.metadata.dataloader.rest.ExportRestCodec")
public interface ApprovalFlowRestProxy {
    @POST(value = "fs-crm-workflow/free/approval/flowType")
    FindFlowType.Result findFlowType(@Body FindFlowType.Arg arg, @HeaderMap Map<String, String> headers);
}
