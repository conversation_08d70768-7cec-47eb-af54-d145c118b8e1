package com.facishare.paas.metadata.dataloader.rest;

/**
 * <AUTHOR>
 * @date 2019-06-10 16:11
 */
public interface Common {
    int SUCCESS = 0;
    int FAIL = -1;
    String HTTP_HEADER_EI = "x-fs-ei";
    String HTTP_HEADER_EA = "x-fs-ea";
    String HTTP_HEADER_UID = "x-fs-userInfo";
    String HTTP_HEADER_LOCALE = "x-fs-locale";
    String HTTP_HEADER_OUT_EI = "x-out-tenant-id";
    String HTTP_HEADER_OUT_USER = "x-out-user-id";
    String HTTP_HEADER_OUT_APP_ID = "x-out-app-id";
    String HTTP_HEADER_UPSTREAM_OWNER_ID = "x-fs-upstream-owner-id";

    String HTTP_HEADER_APP_ID = "x-app-id";
    String OUT_IDENTITY_TYPE = "x-out-identity-type";

    /**
     * 追加 CEP 的 header，兼容 GoalValueObj 导出的路由规则
     */
    String TENANT_ID = "X-fs-Enterprise-Id";
    String USER_ID = "X-fs-Employee-Id";

    String HTTP_HEADER_TIME_ZONE ="x-fs-timezone";

    String HTTP_HEADER_TRACE_ID ="X-fs-Trace-Id";
}
