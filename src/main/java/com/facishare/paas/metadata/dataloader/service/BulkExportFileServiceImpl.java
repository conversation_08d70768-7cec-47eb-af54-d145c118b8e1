package com.facishare.paas.metadata.dataloader.service;

import com.facishare.paas.metadata.dataloader.common.Config;
import com.facishare.paas.metadata.dataloader.filter.Context;
import com.facishare.paas.metadata.dataloader.rest.Common;
import com.facishare.paas.metadata.dataloader.rest.ExportRestProxy;
import com.facishare.paas.metadata.dataloader.rest.dto.ExportFileAttachment;
import com.facishare.paas.metadata.dataloader.service.IBulkExportService.ExportResult;
import com.facishare.paas.metadata.dataloader.task.ExportFilesTask;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * create by z<PERSON><PERSON> on 2021/01/07
 */
@Slf4j
@Service
public class BulkExportFileServiceImpl implements IBulkExportFileService {
    @Autowired
    private ExportRestProxy exportRestProxy;

    @Override
    public IBulkExportService.VerifyResult verify(Context.ExportContext context) {
        IBulkExportService.VerifyResult result;
        try {
            ExportFileAttachment.Arg arg = ExportFileAttachment.Arg.builder()
                    .describeApiName(context.getObjectDescribeApiName())
                    .dataIdList(context.getDataIdList())
                    .searchTemplateId(context.getSearchTemplateId())
                    .searchQueryInfo(context.getSearchQueryInfo())
                    .searchTemplateType(context.getSearchTemplateType())
                    .isIgnoreSceneFilter(context.getIsIgnoreSceneFilter())
                    .isIgnoreSceneRecordType(context.getIsIgnoreSceneRecordType())
                    .method(context.getMethod())
                    .fieldApiNames(context.getFieldApiNames())
                    .build();
            Map<String, String> header = Config.makeHeader(context);
            ExportFileAttachment.Result restResult = exportRestProxy.exportFileVerify(arg, header, context.getObjectDescribeApiName());
            // 将错误码统一为0或者-1，方便前端处理
            int code = restResult.getCode() == Common.SUCCESS ? Common.SUCCESS : Common.FAIL;
            result = IBulkExportService.VerifyResult.builder().code(code).message(restResult.getMessage()).build();
        } catch (Exception e) {
            log.error("Error in export file verify, tenantId:{}, ea:{},userId:{}, apiName:{}",
                    context.getUser().getTenantId(),
                    context.getUser().getEa(),
                    context.getUser().getUserId(),
                    context.getObjectDescribeApiName(),
                    e);
            return IBulkExportService.VerifyResult.builder().code(Common.FAIL).message("System Error!").build();
        }
        return result;
    }

    @Override
    public ExportResult export(Context.ExportContext context) {
        ExportFileAttachment.Arg arg = ExportFileAttachment.Arg.builder()
                .describeApiName(context.getObjectDescribeApiName())
                .dataIdList(context.getDataIdList())
                .searchTemplateId(context.getSearchTemplateId())
                .searchQueryInfo(context.getSearchQueryInfo())
                .searchTemplateType(context.getSearchTemplateType())
                .isIgnoreSceneFilter(context.getIsIgnoreSceneFilter())
                .isIgnoreSceneRecordType(context.getIsIgnoreSceneRecordType())
                .method(context.getMethod())
                .fieldApiNames(context.getFieldApiNames())
                .jobId(context.getJobId())
                .build();
        Map<String, String> header = Config.makeHeader(context);

        ExportFileAttachment.Result result;
        try {
            result = exportRestProxy.exportFileAttachment(arg, header, context.getObjectDescribeApiName());
        } catch (Exception e) {
            log.error("Error in export to local, tenantId:{}, ea:{},userId:{}, apiName:{}",
                    context.getUser().getTenantId(),
                    context.getUser().getEa(),
                    context.getUser().getUserId(),
                    context.getObjectDescribeApiName(),
                    e);
            return ExportResult.builder().code(Common.FAIL).message("System Error!").build();
        }
        if (result.getCode() != Common.SUCCESS) {
            log.warn("First invoke export to local action failed. arg:{}, result:{}", arg, result);
            return ExportResult.builder().code(Common.FAIL).message(result.getMessage()).build();
        }
        if (Strings.isNullOrEmpty(result.getData().getToken())) {
            // 未获取到token，失败
            log.warn("First invoke export to local action failed. No token, arg:{}, result:{}", arg, result);
            return ExportResult.builder().code(Common.FAIL).message(result.getMessage()).build();
        }
        // 创建导出任务 ExportTask 实例。

        // 在新线程中定时通过Rest接口调用action。
        // 导出不需要ping任务中心，导出完成后调用complete接口即可
        // 第一次调用导出才会返回totalCount
        arg.setToken(result.getData().getToken());  // 第一次调用完成后，将返回的token设置入arg中进行之后的调用
        String apiName = context.getObjectDescribeApiName();
        ExportFilesTask.createTaskAndInvoker(context.getJobId(), result.getData().getToken(), result.getData().getTotalCount(),
                () -> exportRestProxy.exportFileAttachment(arg, header, apiName));
        return ExportResult.builder().code(Common.SUCCESS).message("ok").build();
    }
}
