package com.facishare.paas.metadata.dataloader.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class SpringContextUtil implements ApplicationContextAware {

    private static ApplicationContext CONTEXT = null;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        CONTEXT = applicationContext;
    }

    public static ApplicationContext getContext() {
        return CONTEXT;
    }

    /**
     * 根据类型获取Bean
     *
     * @param clazz Bean类型
     * @param <T> Bean类型泛型
     * @return Bean实例，如果不存在返回null
     */
    public static <T> T getBean(Class<T> clazz) {
        if (CONTEXT == null) {
            return null;
        }
        try {
            return CONTEXT.getBean(clazz);
        } catch (BeansException e) {
            return null;
        }
    }

    /**
     * 根据名称获取Bean
     *
     * @param name Bean名称
     * @return Bean实例，如果不存在返回null
     */
    public static Object getBean(String name) {
        if (CONTEXT == null) {
            return null;
        }
        try {
            return CONTEXT.getBean(name);
        } catch (BeansException e) {
            return null;
        }
    }
}
