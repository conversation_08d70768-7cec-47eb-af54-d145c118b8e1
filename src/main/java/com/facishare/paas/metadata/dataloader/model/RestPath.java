package com.facishare.paas.metadata.dataloader.model;

import com.google.common.base.Objects;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

public class RestPath {
    private RestPath() {
    }

    private List<String> greyTenantList;

    private enum SingletonHolder {
        INSTANCE;

        private RestPath restPath;

        SingletonHolder() {
            restPath = new RestPath();
        }

        public RestPath getRestPath() {
            return restPath;
        }

    }

    public static RestPath getInstance() {
        return SingletonHolder.INSTANCE.getRestPath();
    }

    private String feedPath;
    private String personPath;

    public void init(String sfaRestPath, String defObjPath, String sfaRestPathGrey, String defObjPathGrey,
                     List<String> greyTenantList, String feedPath, String personPath) {
        this.greyTenantList = greyTenantList;
        this.feedPath = feedPath;
        this.personPath = personPath;
        initBasePathMap(sfaRestPath, defObjPath, sfaRestPathGrey, defObjPathGrey);
        //test
//        initBasePathMap(sfaRestPath,"http://localhost:8080/API/v1/object",sfaRestPathGrey,"http://localhost:8080/API/v1/object");
        initExtPathMap();
    }

    private void initExtPathMap() {
        Map<String, String> map = Maps.newHashMap();
        map.put(getExtPathMapKey(OperationType.VERIFY, ImportType.INSERT), "InsertImportVerify");
        map.put(getExtPathMapKey(OperationType.INVOKE, ImportType.INSERT), "InsertImportData");
        map.put(getExtPathMapKey(OperationType.VERIFY, ImportType.UPDATE), "UpdateImportVerify");
        map.put(getExtPathMapKey(OperationType.INVOKE, ImportType.UPDATE), "UpdateImportData");

        map.put(getExtPathMapKey(OperationType.UNION_VERIFY, ImportType.INSERT), "UnionInsertImportVerify");
        map.put(getExtPathMapKey(OperationType.UNION_INVOKE, ImportType.INSERT), "UnionInsertImportData");
        map.put(getExtPathMapKey(OperationType.UNION_VERIFY, ImportType.UPDATE), "UnionUpdateImportVerify");
        map.put(getExtPathMapKey(OperationType.UNION_INVOKE, ImportType.UPDATE), "UnionUpdateImportData");

        map.put(getExtPathMapKey(OperationType.ADD_INVOKE, ImportType.INSERT), "ImportDataAdd");
        extPathMap = map;
    }

    private void initBasePathMap(String sfaRestPath, String defObjPath, String sfaRestPathGrey, String defObjPathGrey) {
        Map<String, RestPathInfo> map = Maps.newHashMap();
        map.put(getBasePathMapKey(false, true), RestPathInfo.builder()
                .isGrey(false)
                .isSFAObject(true)
                .path(sfaRestPath)
                .build());
        map.put(getBasePathMapKey(false, false), RestPathInfo.builder()
                .isGrey(false)
                .isSFAObject(false)
                .path(defObjPath)
                .build());
        map.put(getBasePathMapKey(true, true), RestPathInfo.builder()
                .isGrey(true)
                .isSFAObject(true)
                .path(sfaRestPathGrey)
                .build());
        map.put(getBasePathMapKey(true, false), RestPathInfo.builder()
                .isGrey(true)
                .isSFAObject(false)
                .path(defObjPathGrey)
                .build());
        basePathMap = map;
    }

    private Map<String, RestPathInfo> basePathMap = Maps.newHashMap();
    private Map<String, String> extPathMap = Maps.newHashMap();

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RestPathInfo {
        private String path;
        private boolean isGrey;
        private boolean isSFAObject;
    }


    public String getPath(String apiName, OperationType operationType, String tenantId, ImportType importType, List<String> unionApiNames) {
        //地址接入apibus后可以去掉这个特殊处理
        if (Objects.equal(apiName, "ActiveRecordObj")) {
            return String.format("%s/%s/action/%s", feedPath, apiName, extPathMap.get(getExtPathMapKey(operationType, importType)));
        } else if (Objects.equal(apiName, "PersonnelObj") && Objects.equal(importType, ImportType.INSERT)) {
            return String.format("%s/%s/action/%s", personPath, apiName, extPathMap.get(getExtPathMapKey(operationType, importType)));
        }

        if (!CollectionUtils.isEmpty(unionApiNames)) {
            RestPathInfo path = basePathMap.getOrDefault(
                    String.format("%s%s", isGreyTenant(tenantId), false),
                    RestPathInfo.builder().build());
            //联合导入走新路径
            return String.format("%s/%s/action/%s", path.getPath(), apiName, extPathMap.get(getExtPathMapKey(operationType, importType)));
        }

        boolean isSFA = ObjectAPINameMapping.isSFAObject(apiName);
        RestPathInfo restPathInfo = basePathMap.getOrDefault(
                String.format("%s%s", isGreyTenant(tenantId), isSFA),
                RestPathInfo.builder().build());

        return isSFA ? restPathInfo.getPath() :
                String.format("%s/%s/action/%s", restPathInfo.getPath(), apiName, extPathMap.get(getExtPathMapKey(operationType, importType)));
    }

    private boolean isGreyTenant(String tenantId) {
        return greyTenantList.contains(tenantId);
    }

    private String getBasePathMapKey(boolean isGrey, boolean isSFA) {
        return String.format("%s%s", isGrey, isSFA);
    }

    private String getExtPathMapKey(OperationType operationType, ImportType importType) {
        return String.format("%s%s", operationType, importType);
    }

}
