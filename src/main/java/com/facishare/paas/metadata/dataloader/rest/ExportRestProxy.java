package com.facishare.paas.metadata.dataloader.rest;

import com.facishare.paas.metadata.dataloader.rest.dto.*;
import com.facishare.paas.metadata.dataloader.service.IExportByPrintTemplateService;
import com.facishare.rest.core.annotation.*;

import java.util.Map;

/**
 * 导出服务Rest代理
 *
 * <AUTHOR>
 * @date 2019-06-10 11:13
 */
@RestResource(value = "Export", desc = "Export Rest接口", contentType = "application/json",
        codec = "com.facishare.paas.metadata.dataloader.rest.ExportRestCodec")
public interface ExportRestProxy {
    @POST(value = "{apiName}/action/ExportVerify", desc = "导出Verify服务")
    ExportVerifyDTO.Result verifyExport(@Body ExportVerifyDTO.Arg arg,
                                        @HeaderMap Map<String, String> header,
                                        @PathParam("apiName") String apiName);

    @POST(value = "{apiName}/action/Export", desc = "导出数据服务")
    ExportDataDTO.Result exportData(@Body ExportDataDTO.Arg arg,
                                    @HeaderMap Map<String, String> header,
                                    @PathParam("apiName") String apiName);

    @POST(value = "{apiName}/action/ExportFileAttachment", desc = "导出图片/附件服务")
    ExportFileAttachment.Result exportFileAttachment(@Body ExportFileAttachment.Arg arg,
                                                     @HeaderMap Map<String, String> header,
                                                     @PathParam("apiName") String apiName);

    @POST(value = "{apiName}/action/ExportFileVerify", desc = "导出图片/附件 Verify 服务")
    ExportFileAttachment.Result exportFileVerify(@Body ExportFileAttachment.Arg arg,
                                                 @HeaderMap Map<String, String> header,
                                                 @PathParam("apiName") String apiName);

    @POST(value = "{apiName}/action/ExportExcelTemplateVerify", desc = "导出excel模板 Verify 服务")
    ExportExcelTemplate.Result exportExcelTemplateVerify(@Body ExportExcelTemplate.Arg arg,
                                                         @HeaderMap Map<String, String> header,
                                                         @PathParam("apiName") String apiName);

    @POST(value = "{apiName}/action/ExportExcelTemplate", desc = "导出excel打印模板服务")
    ExportExcelTemplate.Result exportExcelTemplateData(@Body ExportExcelTemplate.Arg arg,
                                                       @HeaderMap Map<String, String> header,
                                                       @PathParam("apiName") String apiName);

    @POST(value = "object_import/service/find_config", desc = "导入的 config 逻辑")
    FindImportConfig.Result findImportConfig(@Body FindImportConfig.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "object_import/service/executeExportTaskHookFunction", desc = "执行导入的hook函数",
            codec = "com.facishare.paas.metadata.dataloader.rest.ImportTaskHookProxyCodec")
    ExecuteExportTaskHookFunction.Result executeExportTaskHookFunction(@Body ExecuteExportTaskHookFunction.Arg arg,
                                                                       @HeaderMap Map<String, String> header);

    @POST(desc = "导出校验")
    ExportCommonVerify.Result exportVerify(@Body ExportCommonVerify.Arg arg, @ServiceURLParam String url, @HeaderMap Map<String, String> header);

    @POST(desc = "获取模板数据")
    ExportCommonDTO.ExportHeaderResult findExportHeader(@Body ExportCommonDTO.Arg arg, @ServiceURLParam String url, @HeaderMap Map<String, String> header);

    @POST(desc = "获取模板数据")
    ExportCommonDTO.ExportDataResult findExportData(@Body ExportCommonDTO.Arg arg, @ServiceURLParam String url, @HeaderMap Map<String, String> header);


    @POST(value = "{apiName}/action/ExportByPrintTemplateVerify", desc = "根据打印模板导出数据校验接口")
    IExportByPrintTemplateService.RestAPIResult exportByPrintTemplateVerify(@Body IExportByPrintTemplateService.Arg arg,
                                                                            @HeaderMap Map<String, String> header,
                                                                            @PathParam("apiName") String apiName);

    @POST(value = "{apiName}/action/ExportByPrintTemplate", desc = "根据打印模板导出数据")
    IExportByPrintTemplateService.RestAPIResult exportByPrintTemplate(@Body IExportByPrintTemplateService.Arg arg,
                                                                      @HeaderMap Map<String, String> header,
                                                                      @PathParam("apiName") String apiName);

    /**
     * 查询对象描述信息
     * 调用CRM服务获取对象的字段描述信息，用于图片字段识别
     *
     * @param arg 请求参数，包含对象API名称
     * @param header 请求头信息，包含用户认证信息
     * @return 对象描述信息
     */
    @POST(value = "describe/service/findDescribeByApiName", desc = "查询对象描述信息")
    ObjectDescribeDocument.Result findDescribeByApiName(@Body ObjectDescribeDocument.Arg arg,
                                                       @HeaderMap Map<String, String> header);

}
