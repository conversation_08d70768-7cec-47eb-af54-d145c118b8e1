package com.facishare.paas.metadata.dataloader.rest.dto;

import com.facishare.paas.metadata.dataloader.model.ImportType;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * create by z<PERSON><PERSON> on 2021/12/10
 */
public interface FindImportConfig {

    @Data
    @AllArgsConstructor(staticName = "of")
    class Arg {
        @SerializedName("describe_api_name")
        private String describeApiName;
    }

    @Data
    class Result extends BaseDTO.CrmResult {
        @Delegate
        @SerializedName("data")
        private ImportConfigData data;
    }

    @Data
    class ImportConfigData {
        private ImportConfig insert;
        private ImportConfig update;
        private ImportConfig insertPreProcessing;
        private ImportConfig updatePreProcessing;
        private ImportConfig insertImport;

        public boolean preProcessingFuncIsEnableByImportType(ImportType type) {
            ImportConfig importConfig = null;
            if (ImportType.INSERT == type) {
                importConfig = insertPreProcessing;
            } else if (ImportType.UPDATE == type) {
                importConfig = updatePreProcessing;
            }

            return Optional.ofNullable(importConfig)
                    .map(ImportConfig::getFunctionApiName)
                    .map(StringUtils::isNotEmpty)
                    .orElse(false);
        }
    }

    @Data
    class ImportConfig {
        @SerializedName("describe_api_name")
        private String describeApiName;
        @SerializedName("function_api_name")
        private String functionApiName;
        /**
         * insert 新建导入
         * update 更新导入
         */
        @SerializedName("import_type")
        private String importType;
        @SerializedName("function_name")
        private String functionName;
        private String remark;
        /**
         * import 导入前验证函数
         * importPreProcessing 导入预处理函数
         */
        @SerializedName("define_type")
        private String defineType;

        /**
         * 导入的方式
         * normal 正常导入
         * addAction 导入走addAction
         */
        @SerializedName("import_method")
        private String importMethod;
    }
}
