<?xml version="1.0" encoding="UTF-8"?>
<!--suppress ALL -->
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop.xsd
       http://www.springframework.org/schema/task
       http://www.springframework.org/schema/task/spring-task.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.facishare.paas.metadata.dataloader"/>

    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <!-- 定时执行及线程池配置 -->
    <bean id="monitorAsyncTaskExecutor" class="com.github.trace.executor.MonitorAsyncTaskExecutor"
          c:executor-ref="importExecutor"/>
    <task:annotation-driven executor="monitorAsyncTaskExecutor"/>
    <bean id="importTaskExecutor" class="com.github.trace.executor.MonitorAsyncTaskExecutor"
          c:executor-ref="importExecutor"/>
    <task:executor id="importExecutor" pool-size="20-100" queue-capacity="20" rejection-policy="CALLER_RUNS"/>

    <import resource="classpath:spring/token-core.xml"/>

    <!-- dubbo config -->
    <bean id="autoConf"
          class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:application.properties"
          p:configName="
        async-job-center-callback
        fs-paas-metadata-dataloader
        fs-paas-dataloader-rest
        fs-paas-dataloader-rest_gray"/>

    <!--index service-->
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>

    <!--jvm监控-->
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>
    <!--蜂眼监控-->
    <bean id="serviceProfiler" class="com.facishare.paas.metadata.dataloader.infra.DataLoaderServiceProfiler"/>
    <aop:config>
        <aop:aspect ref="serviceProfiler" order="1">
            <aop:around method="profile" pointcut="
            execution(* com.facishare.paas.metadata.dataloader.service.BulkImportRestServiceImpl.*(..) )
            or execution(* com.facishare.paas.metadata.dataloader.service.BulkImportServiceImpl.*(..) )
            or execution(* com.facishare.paas.metadata.dataloader.controller.BulkImportRestController.*(..) )
            or execution(* com.facishare.paas.metadata.dataloader.excel.Excel2003Reader.*(..) )
            or execution(* com.facishare.paas.metadata.dataloader.excel.Excel2007Reader.*(..) )
            or execution(* com.facishare.paas.metadata.dataloader.util.ExcelUtil.*(..) )
            or execution(* com.facishare.paas.metadata.dataloader.util.FileUtil.*(..) )
            or execution(* com.facishare.paas.metadata.dataloader.service.FileService.*(..) )
            or execution(* com.facishare.paas.metadata.dataloader.util.HttpRequestUtil.*(..) )
            or execution(* com.facishare.paas.metadata.dataloader.task.ImportTask.*(..) )
            or execution(* com.facishare.paas.metadata.dataloader.task.VerifyTask.*(..) )
"/>
        </aop:aspect>
    </aop:config>

    <!--文件上传配置-->
    <import resource="classpath:META-INF/spring/fs-fsi-proxy-service.xml"/>

    <!--Rest Proxy配置-->
    <bean id="serviceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-paas-dataloader-rest" init-method="init"/>

    <!--Export REST接口-->
    <bean id="export" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.metadata.dataloader.rest.ExportRestProxy">
        <property name="factory" ref="serviceProxyFactory"/>
    </bean>

    <!--审批流 REST接口-->
    <bean id="approvalFlowRestProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.metadata.dataloader.rest.ApprovalFlowRestProxy">
        <property name="factory" ref="serviceProxyFactory"/>
    </bean>

    <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>
    </bean>

    <import resource="classpath:spring/mongo.xml"/>
</beans>
