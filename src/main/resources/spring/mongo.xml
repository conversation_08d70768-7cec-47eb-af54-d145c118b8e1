<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
    <bean id="importDataTenantPolicy" class="com.facishare.paas.pod.mongo.MongoRoutePolicy"
          p:biz="PAAS-IMPORT"
          p:application="paas-import"
          p:dialect="mongodb"
    />

    <bean id="importDataStore" class="com.github.mongo.support.MongoDataStoreFactoryBean"
          p:configName="fs-paas-import-mongo"
          p:tenantPolicy-ref="importDataTenantPolicy"
    />

    <bean id="importDataDao" class="com.facishare.paas.metadata.dataloader.mongo.dao.ImportDataDao"/>
</beans>