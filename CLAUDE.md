# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在此代码仓库中工作时提供指导。

## 项目概述

FS-PaaS Metadata DataLoader 是一个企业级数据导入导出服务，专为处理大规模Excel数据和高级图片支持而设计。这是一个Java Maven Web应用程序（WAR打包），为CRM系统提供批量数据加载解决方案。

## 构建和开发命令

### Maven 命令
```bash
# 清理并构建项目
mvn clean package -DskipTests

# 运行测试
mvn test

# 运行特定测试
mvn test -Dtest=UnifiedImageExtractorTest

# 运行带模拟服务的集成测试
mvn test -Dtest=*IntegrationTest -Ddev.mode=true

# 生成代码覆盖率报告 (JaCoCo)
mvn jacoco:report

# 生成Swagger文档
mvn compile  # Swagger文档生成在 generated/swagger-ui/
```

### 开发环境设置
```bash
# 启动本地MongoDB用于测试
mongod --dbpath /path/to/data

# 创建测试数据库
mongo
use paas_dataloader_test

# 部署WAR到Tomcat
cp target/fs-paas-metadata-dataloader-1.0.0-SNAPSHOT.war $TOMCAT_HOME/webapps/

# 查看应用日志
tail -f $TOMCAT_HOME/logs/dataloader-*.log
```

## 核心架构

### 技术栈
- **Java 8+** 配合 Spring Framework 4.x
- **RESTEasy 3.x** 用于 REST API  
- **Apache POI 3.15** 用于Excel处理和图片提取
- **MongoDB 3.10.1** 用于临时数据存储
- **Apache RocketMQ** 用于异步消息传递
- **Maven** 用于构建管理

### 关键组件

#### 主要服务层
- `controller/` - REST API端点 (`BulkImportRestController`, `BulkExportRestController`)
- `service/` - 业务逻辑 (`BulkImportServiceImpl`, `ExcelParseServiceImpl`)
- `task/` - 异步任务执行 (`ImportTask`, `VerifyTask`, `ExportTask`)

#### 核心处理模块
- `excel/` - Excel文件读取器 (`Excel2003Reader`, `Excel2007Reader`, `IExcelReader`)
- `image/` - 革命性图片处理模块：
  - `extractor/UnifiedImageExtractor` - 统一图片提取引擎
  - `service/ImageUploadService` - 图片上传到Stone文件服务
  - 支持Office、WPS DISPIMG函数、嵌入式图片和npath格式

#### 基础设施
- `filter/` - 请求/响应过滤器和上下文管理
- `rest/` - 外部服务代理 (CRM, Stone, 审批流)
- `mongo/` - MongoDB数据访问层
- `mq/` - RocketMQ消息生产者

### Spring 配置
- 使用基于XML的Spring配置 (`applicationContext.xml`)
- 为 `com.facishare.paas.metadata.dataloader` 启用组件扫描
- 线程池配置：导入任务使用20-100个线程
- 启用AOP性能监控

## 图片处理架构（核心特性）

该项目包含一个革命性的图片处理系统，支持：

### 支持的图片机制
- **Microsoft Office标准格式**: Drawing XML + xl/media/ 结构
- **WPS DISPIMG函数**: `=DISPIMG("image_id")` 专有格式
- **嵌入式图片**: POI Drawing API兼容的浮动图片  
- **单元格嵌入图片**: Excel 365新功能
- **历史npath格式**: 100%向后兼容 `npath|npath` 格式

### 核心类
- `UnifiedImageExtractor` - 主要图片提取引擎
- `ImageUploadService` - 处理图片上传到Stone服务
- `OptimizedImageAwareExcelReaderDecorator` - 内存优化的Excel读取器，支持图片

## 数据导入/导出流程

### 导入流程
1. **文件上传** → **格式验证** → **表头验证**
2. **图片预处理** → **数据预处理** → **业务验证**  
3. **异步任务执行** → **进度报告** → **结果生成**

### 导出流程
1. **查询准备** → **数据检索** → **格式处理**
2. **模板应用** → **文件生成** → **下载准备**

## 测试策略

### 测试结构
- `src/test/java/` - JUnit/Mockito单元测试
- `src/test/groovy/` - Spock BDD测试，用于控制器和服务
- 集成测试可用，支持模拟服务

### 测试类别
- 单个组件的单元测试
- 端到端工作流的集成测试
- 大文件处理的性能测试

## 配置管理

### 环境特定设置
```properties
# 开发模式
app.dev.mode=true
stone.service.enabled=false
crm.service.enabled=false

# 生产模式  
app.dev.mode=false
stone.service.enabled=true
crm.service.enabled=true
```

### 性能调优
- `excel.processing.batch.size` - 数据处理批次大小（默认：200）
- `image.processing.thread.pool.size` - 图片处理线程池
- `image.processing.memory.optimized` - 启用内存优化功能

## 外部依赖

### 必需服务
- **Stone文件服务** - 图片上传和存储
- **CRM元数据服务** - 字段定义和验证规则
- **审批流服务** - 工作流集成
- **RocketMQ** - 异步事件消息传递

### 开发注意事项
- 所有外部服务在开发模式下都可以被模拟
- ServiceConfiguration提供统一的环境管理
- 使用 `@Component`, `@Service` 注解标注Spring beans
- 使用RESTEasy而非Spring MVC处理REST端点

## 常见开发任务

### 添加新图片格式支持
1. 扩展 `UnifiedImageExtractor` 添加新的检测逻辑
2. 添加格式特定的提取方法
3. 如需要，更新 `ImageUploadService`
4. 添加全面的测试

### 添加新的导入/导出功能
1. 更新 `model/` 包中的数据模型
2. 扩展服务层 (`BulkImportServiceImpl`)
3. 在控制器中添加REST端点
4. 更新 `validator/` 中的验证逻辑

### 性能优化
- 使用软引用缓存进行内存管理
- 对大Excel文件实现SAX流式处理
- 批量CRM API调用以避免N+1问题
- 监控线程池和队列大小

## 故障排除

### 常见问题
- **图片导入失败**: 检查Stone服务连接性和图片格式支持
- **内存问题**: 调整JVM参数并启用内存优化
- **WPS文件处理**: 确保启用WPS解析器且DISPIMG格式正确

### 调试工具
- Excel文件的ZIP结构分析器
- 图片提取验证工具
- 蜂眼监控性能分析
- 每个处理步骤的详细日志记录