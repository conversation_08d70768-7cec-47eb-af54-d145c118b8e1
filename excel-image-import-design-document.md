# Excel嵌入式图片导入功能设计文档

## 1. 概述

Excel嵌入式图片导入功能是一个企业级数据导入解决方案，旨在自动识别、提取和上传Excel文件中的嵌入图片。该功能支持多种图片存储格式，包括Microsoft Office标准格式、WPS DISPIMG函数格式等，为企业用户提供完整的图片数据迁移能力。

## 2. 核心组件架构

### 2.1 类图

```mermaid
classDiagram
    class IExcelReader {
        <<interface>>
        +process(String fileName)
        +setRowReader(IRowReader reader)
    }
    
    class IRowReader {
        <<interface>>
        +getRows(int sheetIndex, int curRow, List<String> rowList)
        +setTotalLineCount(int rowCount)
    }
    
    class ExcelExecutor {
        -IExcelReader reader
        -String importObjectApiName
        -List<String> unionImportApiNameList
        +init()
        +process(String fileName)
    }
    
    class Excel2007Reader {
        +process(String fileName)
        +setRowReader(IRowReader reader)
    }
    
    class Excel2007UnionReader {
        +process(String fileName)
    }
    
    class OptimizedImageAwareExcelReaderDecorator {
        -IExcelReader delegate
        -List<String> apiNameList
        -User user
        -OptimizedImageAwareRowReader wrappedRowReader
        +process(String fileName)
        +setRowReader(IRowReader reader)
    }
    
    class OptimizedImageAwareRowReader {
        -IRowReader originalRowReader
        -UnifiedImageExtractor imageExtractor
        -ImageUploadService imageUploadService
        -CrmMetadataService crmMetadataService
        -List<ImageFieldMapping> imageFieldMappings
        +getRows(int sheetIndex, int curRow, List<String> rowList)
        +setTotalLineCount(int rowCount)
        -handleHeaderRow(List<String> rowData, int sheetIndex)
        -handleDataRow(int curRow, List<String> rowData, int sheetIndex)
        -processImageCell(String cellValue, int row, int col, int sheetIndex)
    }
    
    class UnifiedImageExtractor {
        +extractImage(String filePath, CellPosition position, int sheetIndex)
        -detectImageStorageType(String filePath, CellPosition position, int sheetIndex)
        -extractByStorageType(String filePath, CellPosition position, ImageStorageType storageType, int sheetIndex)
        -extractStandardOOXMLImage(String filePath, CellPosition position, int sheetIndex)
        -extractWPSDispimgImage(String filePath, CellPosition position, int sheetIndex)
    }
    
    class ImageUploadService {
        <<interface>>
        +uploadImage(byte[] imageData, String fileName, String format, User user)
    }
    
    class ImageUploadServiceImpl {
        -StoneProxyApi stoneProxyApi
        -EIEAConverter eieaConverter
        +uploadImage(byte[] imageData, String fileName, String format, User user)
        -uploadToStoneService(InputStream inputStream, String fileName, String format, long fileSize, User user)
    }
    
    class CrmMetadataService {
        -ExportRestProxy exportRestProxy
        +getImageFields(String apiName, User user)
        +getObjectDescribe(String apiName, User user)
    }
    
    class FastImageInfoHelper {
        +getImageInfo(byte[] imageData)
        +processStream(InputStream inputStream)
    }
    
    class ImageFieldMapping {
        -String apiName
        -String fieldName
        -List<Integer> columnIndexes
        -IFieldDescribe fieldDescribe
        -boolean isImageField
    }
    
    class CellPosition {
        -int row
        -int col
        +of(int row, int col)
        +getExcelAddress()
    }
    
    class ImageUploadResult {
        -boolean success
        -String uploadedPath
        -String originalFileName
        -String errorMessage
        -long fileSize
        -String format
        -long uploadDuration
    }
    
    class ExcelUtil {
        +createImageAwareReader(String fileExt, boolean isUnionImport, boolean skipHiddenSheet, List<String> apiNameList, User user)
        +createReader(String fileExt, boolean isUnionImport, boolean skipHiddenSheet)
    }
    
    IExcelReader <|.. Excel2007Reader
    IExcelReader <|.. Excel2007UnionReader
    IExcelReader <|.. OptimizedImageAwareExcelReaderDecorator
    IRowReader <|.. ExcelExecutor
    IRowReader <|.. OptimizedImageAwareRowReader
    ImageUploadService <|.. ImageUploadServiceImpl
    
    ExcelExecutor --> IExcelReader : 使用
    ExcelExecutor --> ExcelUtil : 工厂方法
    OptimizedImageAwareExcelReaderDecorator --> IExcelReader : 装饰
    OptimizedImageAwareExcelReaderDecorator --> OptimizedImageAwareRowReader : 创建
    OptimizedImageAwareRowReader --> UnifiedImageExtractor : 提取图片
    OptimizedImageAwareRowReader --> ImageUploadService : 上传图片
    OptimizedImageAwareRowReader --> CrmMetadataService : 获取字段信息
    OptimizedImageAwareRowReader --> ImageFieldMapping : 字段映射
    OptimizedImageAwareRowReader --> CellPosition : 位置信息
    UnifiedImageExtractor --> CellPosition : 位置信息
    ImageUploadServiceImpl --> StoneProxyApi : 文件上传
    ImageUploadServiceImpl --> EIEAConverter : 企业ID转换
    ImageUploadServiceImpl --> FastImageInfoHelper : 图片格式检测
    OptimizedImageAwareRowReader --> FastImageInfoHelper : 图片格式检测
```

### 2.2 核心流程图

```mermaid
flowchart TD
    A[开始Excel处理] --> B{是否为xlsx文件?}
    B -- 是 --> C{租户是否启用图片处理?}
    B -- 否 --> D[使用基础Excel读取器]
    C -- 是 --> E[创建图片感知装饰器]
    C -- 否 --> D
    E --> F[包装基础读取器]
    F --> G[设置行读取器]
    G --> H[开始读取Excel文件]
    H --> I[读取第一行表头]
    I --> J[处理表头行]
    J --> K[提取合并单元格映射]
    K --> L[获取CRM图片字段信息]
    L --> M[构建图片字段映射]
    M --> N[读取数据行]
    N --> O{是否为数据行?}
    O -- 表头行 --> J
    O -- 数据行 --> P[处理数据行]
    P --> Q[检查文本路径格式]
    Q -- 是 --> R[跳过处理]
    Q -- 否 --> S[提取嵌入式图片]
    S --> T[检测图片格式]
    T --> U[生成文件名]
    U --> V[上传图片]
    V --> W[更新行数据]
    W --> X{是否还有更多行?}
    X -- 是 --> N
    X -- 否 --> Y[完成处理]
    R --> X
```

### 2.3 核心时序图

```mermaid
sequenceDiagram
    participant Client
    participant ExcelExecutor
    participant ExcelUtil
    participant OptimizedImageAwareExcelReaderDecorator
    participant Excel2007Reader
    participant OptimizedImageAwareRowReader
    participant UnifiedImageExtractor
    participant ImageUploadService
    participant StoneProxyApi
    
    Client->>ExcelExecutor: init()
    ExcelExecutor->>ExcelUtil: createImageAwareReader()
    ExcelUtil->>ExcelUtil: createReader()
    ExcelUtil-->>ExcelExecutor: 基础读取器
    ExcelExecutor->>OptimizedImageAwareExcelReaderDecorator: new()
    ExcelExecutor-->>Client: 返回装饰器
    
    Client->>ExcelExecutor: process(fileName)
    ExcelExecutor->>OptimizedImageAwareExcelReaderDecorator: process(fileName)
    OptimizedImageAwareExcelReaderDecorator->>OptimizedImageAwareExcelReaderDecorator: 创建包装的RowReader
    OptimizedImageAwareExcelReaderDecorator->>Excel2007Reader: setRowReader()
    OptimizedImageAwareExcelReaderDecorator->>Excel2007Reader: process(fileName)
    
    Excel2007Reader->>OptimizedImageAwareRowReader: getRows(0, 1, headerRow)
    OptimizedImageAwareRowReader->>OptimizedImageAwareRowReader: handleHeaderRow()
    OptimizedImageAwareRowReader->>OptimizedImageAwareRowReader: extractMergedCellMapping()
    OptimizedImageAwareRowReader->>CrmMetadataService: getImageFields()
    OptimizedImageAwareRowReader->>OptimizedImageAwareRowReader: buildImageFieldMappings()
    
    Excel2007Reader->>OptimizedImageAwareRowReader: getRows(0, 2, dataRow)
    OptimizedImageAwareRowReader->>OptimizedImageAwareRowReader: handleDataRow()
    OptimizedImageAwareRowReader->>OptimizedImageAwareRowReader: processImageFieldColumns()
    OptimizedImageAwareRowReader->>OptimizedImageAwareRowReader: processImageCell()
    OptimizedImageAwareRowReader->>UnifiedImageExtractor: extractImage()
    UnifiedImageExtractor->>UnifiedImageExtractor: detectImageStorageType()
    UnifiedImageExtractor->>UnifiedImageExtractor: extractByStorageType()
    UnifiedImageExtractor-->>OptimizedImageAwareRowReader: 图片数据
    
    OptimizedImageAwareRowReader->>FastImageInfoHelper: getImageInfo()
    FastImageInfoHelper-->>OptimizedImageAwareRowReader: 图片格式
    OptimizedImageAwareRowReader->>OptimizedImageAwareRowReader: generateImageFileName()
    OptimizedImageAwareRowReader->>ImageUploadService: uploadImage()
    ImageUploadService->>ImageUploadService: validateUploadParameters()
    ImageUploadService->>ImageUploadService: uploadToStoneService()
    ImageUploadService->>StoneProxyApi: tempFileUploadByStream()
    StoneProxyApi-->>ImageUploadService: 上传路径
    ImageUploadService-->>OptimizedImageAwareRowReader: ImageUploadResult
    OptimizedImageAwareRowReader->>OptimizedImageAwareRowReader: updateRowDataWithMergedPaths()
    
    OptimizedImageAwareRowReader->>ExcelExecutor: getRows()
```

## 3. 详细设计说明

### 3.1 UnifiedImageExtractor（统一图片提取器）

UnifiedImageExtractor是核心的图片提取组件，负责从Excel文件中提取各种格式的嵌入图片。

#### 3.1.1 主要功能
1. **多格式支持**：
   - Microsoft Office标准格式（基于Drawing XML）
   - WPS DISPIMG函数格式
   - 嵌入式图片格式
   - 单元格内嵌图片格式

2. **多工作表支持**：
   - 动态查找对应工作表的drawing.xml文件
   - 支持联合导入场景下的多工作表处理

3. **智能检测机制**：
   - 自动检测图片存储类型
   - 早期退出优化，提高处理效率

#### 3.1.2 核心方法
- `extractImage()`：统一入口方法，根据位置提取图片
- `detectImageStorageType()`：检测图片存储类型
- `extractByStorageType()`：根据存储类型提取图片
- `extractStandardOOXMLImage()`：提取标准OOXML图片
- `extractWPSDispimgImage()`：提取WPS DISPIMG图片

### 3.2 OptimizedImageAwareRowReader（优化的图片感知行读取器）

该组件在Excel数据读取过程中处理图片相关逻辑，是图片处理功能的核心实现。

#### 3.2.1 主要功能
1. **表头处理**：
   - 合并单元格映射提取
   - CRM图片字段信息获取
   - 图片字段映射关系构建

2. **数据行处理**：
   - 文本路径格式识别（N_xxx|xxx格式）
   - 嵌入式图片提取和上传
   - 图片路径更新到行数据

3. **异常处理**：
   - 单个图片处理失败不影响整体导入流程
   - 详细的日志记录便于问题排查

#### 3.2.2 核心方法
- `getRows()`：行处理入口方法
- `handleHeaderRow()`：表头行处理
- `handleDataRow()`：数据行处理
- `processImageCell()`：单个单元格图片处理

### 3.3 ImageUploadService（图片上传服务）

负责将提取的图片数据上传到文件服务器，提供统一的上传接口。

#### 3.3.1 主要功能
1. **格式支持**：PNG、JPG、JPEG、GIF、BMP、WebP
2. **大小限制**：最大10MB
3. **企业ID转换**：通过EIEAConverter进行企业ID转换
4. **Stone服务集成**：通过StoneProxyApi上传到文件服务器

#### 3.3.2 核心方法
- `uploadImage()`：图片上传主方法
- `validateUploadParameters()`：参数验证
- `uploadToStoneService()`：实际上传到Stone服务

## 4. 工作流程详解

### 4.1 初始化阶段
1. ExcelExecutor调用ExcelUtil.createImageAwareReader()创建读取器
2. 根据文件扩展名和租户配置判断是否启用图片处理
3. 如果启用，创建OptimizedImageAwareExcelReaderDecorator装饰器

### 4.2 读取阶段
1. OptimizedImageAwareExcelReaderDecorator包装基础Excel读取器
2. 设置OptimizedImageAwareRowReader作为行读取器

### 4.3 表头处理阶段
1. 读取第一行作为表头
2. 提取合并单元格映射信息
3. 通过CrmMetadataService获取对象的图片字段信息
4. 构建图片字段映射关系

### 4.4 数据行处理阶段
1. 对每个数据行进行图片处理
2. 识别文本路径格式并跳过处理
3. 使用UnifiedImageExtractor提取嵌入式图片
4. 通过ImageUploadService上传图片到文件服务器
5. 更新行数据中的图片路径

## 5. 配置项说明

### 5.1 灰度配置
- `importSupportEmbeddedImagesInExcelGray`：控制哪些租户启用嵌入式图片导入功能

### 5.2 技术配置
- 支持的图片格式：PNG、JPG、JPEG、GIF、BMP、WebP
- 最大文件大小：10MB
- 文件服务器：Stone文件服务

## 6. 技术特点

### 6.1 性能优化
1. **内存优化**：避免加载整个Excel文件到内存
2. **早期退出优化**：在图片检测和处理过程中支持早期退出
3. **流式处理**：使用流式处理避免大文件内存占用

### 6.2 容错机制
1. **异常隔离**：单个图片处理失败不影响整体导入流程
2. **重试机制**：在关键步骤提供重试能力
3. **详细日志**：完整的日志记录便于问题排查

### 6.3 扩展性
1. **插件化设计**：通过装饰器模式支持功能扩展
2. **接口抽象**：清晰的接口定义便于替换实现
3. **配置驱动**：通过配置控制功能启用状态