# 业务流程和时序图设计

## 🔄 整体业务流程图

### 1. 完整业务处理流程

```mermaid
flowchart TD
    A[用户上传Excel文件] --> B[ImportTask.execute]
    B --> C[ExcelUtil.createImageAwareReader]
    C --> D{检查是否需要图片处理}
    
    D -->|否| E[创建基础Excel读取器]
    D -->|是| F[创建OptimizedImageAwareExcelReaderDecorator]
    
    E --> G[设置ExcelExecutor]
    F --> G
    
    G --> H[开始Excel解析]
    H --> I[读取表头行]
    I --> J[CRM字段类型查询]
    J --> K[识别图片字段列]
    
    K --> L{是否有图片字段?}
    L -->|否| M[传统文本处理流程]
    L -->|是| N[图片感知处理流程]
    
    N --> O[逐行数据处理]
    O --> P{当前单元格在图片列?}
    
    P -->|否| Q[直接传递原始值]
    P -->|是| R[检查单元格内容类型]
    
    R --> S{内容类型判断}
    S -->|npath格式| T[保持原有逻辑]
    S -->|嵌入图片| U[提取图片数据]
    S -->|WPS DISPIMG| V[解析DISPIMG函数]
    S -->|普通文本| W[返回原始值]
    
    U --> X[上传图片到Stone服务]
    V --> Y[从ZIP提取WPS图片]
    Y --> X
    
    X --> Z{上传成功?}
    Z -->|是| AA[返回npath路径]
    Z -->|否| BB[降级到原始值]
    
    T --> CC[数据导入CRM]
    AA --> CC
    BB --> CC
    W --> CC
    Q --> CC
    M --> CC
    
    CC --> DD[导入完成]
```

### 2. 装饰器模式处理流程

```mermaid
flowchart LR
    A[IExcelReader接口] --> B[基础Excel读取器]
    A --> C[OptimizedImageAwareExcelReaderDecorator]
    
    C --> D[包装基础读取器]
    D --> E[创建图片感知RowReader]
    E --> F[设置增强的RowReader]
    F --> G[委托给基础读取器执行]
    
    G --> H[基础读取器调用RowReader]
    H --> I[OptimizedImageAwareRowReader处理]
    I --> J[图片处理逻辑]
    J --> K[调用原始RowReader]
    K --> L[返回处理结果]
    
    style C fill:#e1f5fe
    style I fill:#f3e5f5
    style J fill:#fff3e0
```

## ⏱️ 详细时序图

### 1. 核心组件交互时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant ImportTask as ImportTask
    participant ExcelUtil as ExcelUtil
    participant Decorator as OptimizedImageAware<br/>ExcelReaderDecorator
    participant RowReader as OptimizedImageAware<br/>RowReader
    participant CrmService as CrmMetadataService
    participant Extractor as UnifiedImageExtractor
    participant UploadService as ImageUploadService
    participant BaseReader as 基础Excel读取器
    participant CRM as CRM系统

    User->>ImportTask: 上传Excel文件
    ImportTask->>ImportTask: executeImport(filePath)
    ImportTask->>ExcelUtil: createImageAwareReader(fileExt, isUnion, skip, apiNameList, user)
    
    ExcelUtil->>ExcelUtil: 检查灰度配置
    alt 启用图片处理
        ExcelUtil->>Decorator: new OptimizedImageAwareExcelReaderDecorator(baseReader, apiNameList, user)
        ExcelUtil-->>ImportTask: 返回装饰器包装的读取器
    else 不启用图片处理
        ExcelUtil-->>ImportTask: 返回基础读取器
    end
    
    ImportTask->>Decorator: setRowReader(excelExecutor)
    ImportTask->>Decorator: process(fileName)
    
    Decorator->>Decorator: 获取Spring Bean实例
    Decorator->>RowReader: 创建OptimizedImageAwareRowReader
    Decorator->>BaseReader: setRowReader(wrappedRowReader)
    Decorator->>BaseReader: process(fileName)
    
    Note over BaseReader,RowReader: 表头处理阶段
    BaseReader->>RowReader: getRows(1, headerRow)
    RowReader->>CrmService: getObjectDescribe(apiName, user)
    CrmService->>CRM: 调用CRM API
    CRM-->>CrmService: 返回字段描述信息
    CrmService-->>RowReader: 返回对象描述
    RowReader->>RowReader: identifyImageColumns(headerRow)
    RowReader->>BaseReader: 继续处理表头
    
    Note over BaseReader,RowReader: 数据行处理阶段
    loop 每一行数据
        BaseReader->>RowReader: getRows(rowIndex, dataRow)
        RowReader->>RowReader: handleDataRow(curRow, rowData, sheetIndex)
        
        alt 单元格在图片字段列
            RowReader->>RowReader: processImageCell(cellValue, row, col, sheetIndex)
            
            alt 检测到嵌入图片
                RowReader->>Extractor: extractImage(filePath, position, sheetIndex)
                Extractor->>Extractor: detectImageStorageType()
                
                alt Office标准格式
                    Extractor->>Extractor: extractStandardOOXMLImage()
                else WPS DISPIMG格式
                    Extractor->>Extractor: extractWPSDispimgImage()
                else 其他格式
                    Extractor->>Extractor: extractByStorageType()
                end
                
                Extractor-->>RowReader: 返回图片字节数据
                
                RowReader->>UploadService: uploadImage(imageData, fileName, format, user)
                UploadService->>UploadService: 验证图片格式和大小
                UploadService->>UploadService: uploadToStoneService()
                UploadService-->>RowReader: 返回ImageUploadResult
                
                alt 上传成功
                    RowReader->>RowReader: 更新单元格值为npath路径
                else 上传失败
                    RowReader->>RowReader: 保持原始值
                end
            else 文本路径格式
                RowReader->>RowReader: 保持原始值不变
            end
        else 非图片字段
            RowReader->>RowReader: 直接传递原始值
        end
        
        RowReader->>BaseReader: 继续处理当前行
    end
    
    BaseReader-->>Decorator: Excel解析完成
    Decorator-->>ImportTask: 处理完成
    ImportTask->>ImportTask: batchImportData()
    ImportTask->>CRM: 批量导入数据
    CRM-->>ImportTask: 导入结果
    ImportTask-->>User: 返回导入结果
```

### 2. 图片提取和上传时序图

```mermaid
sequenceDiagram
    participant RowReader as OptimizedImageAware<br/>RowReader
    participant Extractor as UnifiedImageExtractor
    participant UploadService as ImageUploadService
    participant StoneAPI as Stone文件服务
    participant FileSystem as 文件系统

    RowReader->>Extractor: extractImage(filePath, position, sheetIndex)
    
    Note over Extractor: 图片存储类型检测
    Extractor->>FileSystem: 打开Excel ZIP文件
    Extractor->>Extractor: detectImageStorageType()
    
    alt WPS DISPIMG格式
        Extractor->>Extractor: hasDISPIMGFunction()
        Extractor->>FileSystem: 读取sheet.xml查找DISPIMG函数
        Extractor->>Extractor: extractWPSDispimgImage()
        Extractor->>FileSystem: 解析DISPIMG函数获取图片ID
        Extractor->>FileSystem: 在xl/media/目录查找图片文件
    else Office标准格式
        Extractor->>Extractor: hasStandardDrawingDefinition()
        Extractor->>FileSystem: 检查drawing.xml文件
        Extractor->>Extractor: extractStandardOOXMLImage()
        Extractor->>FileSystem: 解析drawing.xml获取图片引用
        Extractor->>FileSystem: 从xl/media/提取图片数据
    else 单元格内嵌格式
        Extractor->>Extractor: hasCellEmbeddedImage()
        Extractor->>FileSystem: 检查单元格内嵌图片
    end
    
    Extractor->>FileSystem: 读取图片字节数据
    FileSystem-->>Extractor: 返回图片数据
    Extractor-->>RowReader: 返回图片字节数组
    
    Note over RowReader,StoneAPI: 图片上传流程
    RowReader->>UploadService: uploadImage(imageData, fileName, format, user)
    UploadService->>UploadService: validateUploadParameters()
    
    alt 验证通过
        UploadService->>UploadService: uploadToStoneService()
        UploadService->>UploadService: 构建Stone上传请求
        UploadService->>StoneAPI: tempFileUploadByStream()
        StoneAPI-->>UploadService: 返回上传响应
        
        alt 上传成功
            UploadService->>UploadService: 构建成功结果
            UploadService-->>RowReader: ImageUploadResult(success=true, uploadedPath)
        else 上传失败
            UploadService->>UploadService: 构建失败结果
            UploadService-->>RowReader: ImageUploadResult(success=false, errorMessage)
        end
    else 验证失败
        UploadService-->>RowReader: ImageUploadResult(success=false, 验证错误信息)
    end
```

## 📊 数据流向图

### 1. 图片数据完整流转图

```mermaid
flowchart TD
    A[Excel文件] --> B[ZIP文件结构]
    B --> C[xl/worksheets/sheet1.xml]
    B --> D[xl/drawings/drawing1.xml]
    B --> E[xl/media/image1.png]
    
    C --> F[单元格内容解析]
    D --> G[Drawing对象解析]
    E --> H[图片文件读取]
    
    F --> I{内容类型判断}
    I -->|DISPIMG函数| J[WPS图片引用解析]
    I -->|普通文本| K[文本内容处理]
    I -->|空单元格| L[位置坐标匹配]
    
    G --> M[图片位置映射]
    H --> N[图片字节数据]
    J --> O[图片ID映射]
    
    M --> P[UnifiedImageExtractor]
    N --> P
    O --> P
    
    P --> Q[图片数据提取]
    Q --> R[ImageUploadService]
    R --> S[Stone文件服务]
    S --> T[npath路径生成]
    
    T --> U[单元格值更新]
    K --> U
    U --> V[Excel数据行]
    V --> W[CRM批量导入]
    W --> X[业务数据存储]
    
    style A fill:#e3f2fd
    style P fill:#f3e5f5
    style R fill:#fff3e0
    style S fill:#e8f5e8
    style X fill:#fce4ec
```

### 2. 组件依赖和数据流图

```mermaid
graph TB
    subgraph "用户层"
        A[用户上传Excel]
    end
    
    subgraph "控制层"
        B[ImportTask]
        C[ExcelExecutor]
    end
    
    subgraph "装饰器层"
        D[OptimizedImageAwareExcelReaderDecorator]
        E[OptimizedImageAwareRowReader]
    end
    
    subgraph "服务层"
        F[CrmMetadataService]
        G[ImageUploadService]
        H[UnifiedImageExtractor]
    end
    
    subgraph "基础设施层"
        I[基础Excel读取器]
        J[Stone文件服务]
        K[CRM API服务]
    end
    
    subgraph "数据层"
        L[Excel文件系统]
        M[CRM数据库]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    D --> I
    
    E --> F
    E --> G
    E --> H
    
    F --> K
    G --> J
    H --> L
    
    K --> M
    J --> M
    I --> L
    
    style D fill:#e1f5fe
    style E fill:#f3e5f5
    style H fill:#fff3e0
```

## 🔄 错误处理和降级流程

### 错误处理时序图

```mermaid
sequenceDiagram
    participant RowReader as OptimizedImageAware<br/>RowReader
    participant Extractor as UnifiedImageExtractor
    participant UploadService as ImageUploadService
    participant Logger as 日志系统

    RowReader->>Extractor: extractImage(filePath, position, sheetIndex)
    
    alt 图片提取成功
        Extractor-->>RowReader: 返回图片数据
        RowReader->>UploadService: uploadImage(imageData, fileName, format, user)
        
        alt 上传成功
            UploadService-->>RowReader: ImageUploadResult(success=true)
            RowReader->>RowReader: 使用上传后的npath路径
        else 上传失败
            UploadService-->>RowReader: ImageUploadResult(success=false)
            RowReader->>Logger: 记录上传失败日志
            RowReader->>RowReader: 降级到原始单元格值
        end
    else 图片提取失败
        Extractor-->>RowReader: 返回null
        RowReader->>Logger: 记录提取失败日志
        RowReader->>RowReader: 降级到原始单元格值
    end
    
    Note over RowReader: 多层降级机制确保导入不中断
```

## 📋 流程图说明

### 设计特点
1. **分层清晰**: 从用户层到数据层的清晰分层设计
2. **装饰器模式**: 突出展示装饰器模式的处理流程
3. **错误处理**: 完善的错误处理和降级机制
4. **数据流向**: 清晰展示图片数据的完整流转过程

### 关键交互点
1. **灰度控制**: 通过配置控制是否启用图片处理功能
2. **字段识别**: 通过CRM元数据服务智能识别图片字段
3. **多格式支持**: UnifiedImageExtractor支持多种图片存储格式
4. **降级机制**: 任何环节失败都能优雅降级到原始值

### 性能优化点
1. **预筛选**: 只对图片字段进行图片处理
2. **按需处理**: 只有检测到图片时才进行提取和上传
3. **批量上传**: 支持图片的批量上传优化
4. **缓存机制**: CRM字段信息和图片位置信息的缓存
