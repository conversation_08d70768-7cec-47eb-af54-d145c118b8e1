# 图片错误处理集成示例

## 1. ImportTask 中的集成方式

### 1.1 在 doImportData 方法中集成错误检查

```java
private void doImportData(String importApiName, boolean importPreProcessing, Boolean finalBatch) {
    // 现有的导入逻辑...
    IBulkImportRestService.BulkInsertResult result = importRestService.bulkInsert(bulkInsertArg, batchTimeOut);
    
    // 【新增】合并图片处理错误
    result = ImageErrorIntegrationHelper.mergeImageErrors(excelExecutor.getExcelReader(), result, importApiName);
    
    log.info("Bulk Import data current Line:{}, importCount:{} done", doneCount, batchData.size());
    
    // 导入预处理结果
    if (importPreProcessing) {
        importPreProcessingResult(result);
        return;
    }
    
    // 更新结果excel
    updateResultExcel(batchData, result, importApiName);
}
```

### 1.2 在异常处理中集成图片错误

```java
private void doImportData(String importApiName, boolean importPreProcessing, Boolean finalBatch) {
    try {
        // 现有的导入逻辑...
        IBulkImportRestService.BulkInsertResult result = importRestService.bulkInsert(bulkInsertArg, batchTimeOut);
        
        // 合并图片处理错误
        result = ImageErrorIntegrationHelper.mergeImageErrors(excelExecutor.getExcelReader(), result, importApiName);
        
        // 处理结果...
        updateResultExcel(batchData, result, importApiName);
        
    } catch (Exception e) {
        log.error("Error in bulkInsert batch data, current Line:{}, importCount:{}", doneCount, batchData.size(), e);
        
        // 【新增】即使CRM调用失败，也要报告图片错误
        IBulkImportRestService.BulkInsertResult imageErrorResult = 
            ImageErrorIntegrationHelper.createImageOnlyErrorResult(
                excelExecutor.getExcelReader(), importApiName, "Import service failed: " + e.getMessage());
        
        if (imageErrorResult != null) {
            updateResultExcel(batchData, imageErrorResult, importApiName);
        }
    }
}
```

## 2. ExcelExecutor 中的集成方式

### 2.1 在 ExcelExecutor 中添加错误检查方法

```java
public class ExcelExecutor {
    
    /**
     * 检查是否有图片处理错误
     */
    public boolean hasImageProcessingErrors() {
        return ImageErrorIntegrationHelper.hasImageProcessingErrors(excelReader);
    }
    
    /**
     * 获取图片错误摘要
     */
    public ImageProcessingErrorCollector.ErrorCollectionSummary getImageErrorSummary() {
        return ImageErrorIntegrationHelper.getErrorSummary(excelReader);
    }
    
    /**
     * 清理图片错误（在处理完成后调用）
     */
    public void clearImageErrors() {
        ImageErrorIntegrationHelper.clearImageErrors(excelReader);
    }
}
```

### 2.2 在 updateResultCommon 方法中集成

```java
public UpdateExcelResult updateResultCommon(List<DataItem> dataList, IBulkImportRestService.BulkInsertResult result) {
    log.info("updateResultCommon start, bulkInsert result:{}", result.isSuccess());
    
    // 【新增】记录图片错误统计
    if (hasImageProcessingErrors()) {
        ImageProcessingErrorCollector.ErrorCollectionSummary summary = getImageErrorSummary();
        log.info("Image processing errors detected: {}", summary);
    }
    
    // 现有的结果更新逻辑...
    String errorMsg = null;
    boolean success = result.isSuccess();
    // ... 其他逻辑保持不变
    
    return UpdateExcelResult.builder().successCount(successCount).failCount(failCount).build();
}
```

## 3. 完整的错误处理流程

### 3.1 错误收集阶段（在 OptimizedImageAwareRowReader 中）

```java
private String processImageCell(String cellValue, int row, int col, int sheetIndex) {
    try {
        // 图片处理逻辑...
        ImageUploadResult result = imageUploadService.uploadImage(imageData, fileName, format, user);
        
        if (result.isSuccess()) {
            return fileName + "#" + result.getUploadedPath();
        } else {
            // 【关键】记录上传失败错误
            ImageProcessingError error = ImageProcessingError.uploadFailed(
                row, col, getFieldNameForColumn(col), result.getErrorMessage());
            errorCollector.addError(error);
            return cellValue;
        }
    } catch (Exception e) {
        // 【关键】记录处理异常错误
        ImageProcessingError error = ImageProcessingError.builder()
            .rowIndex(row)
            .columnIndex(col)
            .fieldName(getFieldNameForColumn(col))
            .errorMessage(e.getMessage())
            .errorType(ImageProcessingError.ErrorType.OTHER)
            .build();
        errorCollector.addError(error);
        return cellValue;
    }
}
```

### 3.2 错误合并阶段（在 ImportTask 中）

```java
// 在 doImportData 方法中
IBulkImportRestService.BulkInsertResult result = importRestService.bulkInsert(bulkInsertArg, batchTimeOut);

// 合并图片错误
result = ImageErrorIntegrationHelper.mergeImageErrors(excelExecutor.getExcelReader(), result, importApiName);
```

### 3.3 错误展示阶段（在 ExcelExecutor 中）

```java
// 在 updateResultCommon 方法中，错误信息已经合并到 result.getValue().getRowErrorList() 中
List<IBulkImportRestService.BulkInsertResult.RowError> rowErrorList = value.getRowErrorList();

for (IBulkImportRestService.BulkInsertResult.RowError rowError : rowErrorList) {
    // 这里会包含图片错误和CRM错误
    String errorMessage = rowError.getErrorMessage();
    int rowNo = rowError.getRowNo();
    
    // 设置错误单元格
    statusCell.setCellValue(errorMessage);
    statusCell.setCellStyle(failLineStyle);
}
```

## 4. 错误信息格式示例

### 4.1 单个图片错误
```
Image processing failed in field 'Product Image' (Image upload failed): Network timeout after 30 seconds [Size: 2.5 MB]
```

### 4.2 多个图片错误
```
Multiple image processing errors: [Product Image: Image upload failed] [Thumbnail: Unsupported image format] 
```

### 4.3 合并的错误（CRM + 图片）
```
Required field 'Product Name' is missing; Image processing failed in field 'Product Image' (Image upload failed): Server returned error 500
```

## 5. 性能优化建议

### 5.1 内存管理
```java
// 在处理完成后清理错误收集器
try {
    // 导入处理逻辑...
} finally {
    ImageErrorIntegrationHelper.clearImageErrors(excelReader);
}
```

### 5.2 批量错误处理
```java
// 只在批次结束时检查和合并错误，避免频繁检查
if (batchCount % batchHandleCount == 0 || finalBatch) {
    result = ImageErrorIntegrationHelper.mergeImageErrors(excelReader, result, importApiName);
}
```

### 5.3 错误级别控制
```java
// 可以根据错误类型决定是否中断导入
ImageProcessingErrorCollector collector = ImageErrorIntegrationHelper.extractErrorCollector(excelReader);
if (collector != null) {
    int criticalErrors = collector.getErrorCountByType(ImageProcessingError.ErrorType.UPLOAD_FAILED);
    if (criticalErrors > MAX_ALLOWED_ERRORS) {
        throw new DataLoaderBusinessException("Too many image upload failures: " + criticalErrors);
    }
}
```

## 6. 测试建议

### 6.1 单元测试
- 测试错误收集器的基本功能
- 测试错误转换器的转换逻辑
- 测试错误合并的各种场景

### 6.2 集成测试
- 测试完整的错误处理流程
- 测试大量图片错误的性能表现
- 测试错误信息在Excel中的正确显示

### 6.3 边界测试
- 测试没有图片错误的情况
- 测试只有图片错误没有CRM错误的情况
- 测试CRM调用失败但有图片错误的情况
